/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-d1b13bc7 {
  background-color: #f5f5f5;
}
.property-card.data-v-d1b13bc7 {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.image-section.data-v-d1b13bc7 {
  height: 200rpx;
  overflow: hidden;
}
.property-image.data-v-d1b13bc7 {
  width: 100%;
  height: 100%;
}
.content-section.data-v-d1b13bc7 {
  padding: 20rpx;
}
.title-row.data-v-d1b13bc7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.property-title.data-v-d1b13bc7 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}
.delivery-text.data-v-d1b13bc7 {
  font-size: 24rpx;
  color: #ff6b35;
}
.price-row.data-v-d1b13bc7 {
  margin-bottom: 12rpx;
}
.price-text.data-v-d1b13bc7 {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 500;
}
.area-row.data-v-d1b13bc7 {
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.area-text.data-v-d1b13bc7 {
  font-size: 24rpx;
  color: #666666;
}
.feature-row.data-v-d1b13bc7 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20rpx;
  background-color: #eee;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.feature-label.data-v-d1b13bc7 {
  font-size: 26rpx;
  font-weight: 500;
}
.feature-desc.data-v-d1b13bc7 {
  font-size: 24rpx;
  color: #666666;
  margin-left: 20rpx;
}
.action-buttons.data-v-d1b13bc7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12rpx;
  padding-top: 16rpx;
}
.brand-name.data-v-d1b13bc7 {
  font-size: 26rpx;
  margin: 0 0 0 10rpx;
}
.brand-tag.data-v-d1b13bc7 {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.orange-tag.data-v-d1b13bc7 {
  background-color: #fff3e0;
  color: #e65100;
}