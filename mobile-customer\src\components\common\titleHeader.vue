<template>
  <view class="header">
    <view class="line"></view>
    <!-- <image class="speaker-icon" src="/static/images/speaker.png"></image> -->
    <text>{{ title }}</text>
    <!-- <image class="speaker-icon" src="/static/images/speaker.png"></image> -->
    <view class="line"></view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 20rpx 0;
  color: #1296db;
  font-size: 28rpx;

  text {
    margin: 0 15rpx; 
  }
}

.line {
  width: 120rpx; 
  height: 1rpx; 
  background-color: #1296db; 
}

.speaker-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
</style>