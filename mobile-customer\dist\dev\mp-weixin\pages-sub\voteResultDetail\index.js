"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_util = require("../../utils/util.js");
if (!Array) {
  const _easycom_fui_count_down2 = common_vendor.resolveComponent("fui-count-down");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_count_down2 + _easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_count_down = () => "../../components/firstui/fui-count-down/fui-count-down.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_count_down + TitleHeader + VoteNoticeItem + _easycom_fui_button + _easycom_fui_loading)();
}
const TitleHeader = () => "../../components/common/titleHeader.js";
const VoteNoticeItem = () => "../voteNoticeDetail/components/voteNoticeItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const brands = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const handleModifyVote = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/vote/index?id=${id.value}&buildingId=${buildingId.value}`
      });
    };
    const id = common_vendor.ref("");
    const voteEndDate = common_vendor.ref("");
    common_vendor.ref("");
    const buildingId = common_vendor.ref("");
    const voteEndSeconds = common_vendor.ref(0);
    const fetchVoteResult = () => __async(this, null, function* () {
      var _a;
      try {
        loading.value = true;
        const res = yield service_app_vote.getVoteInfoByCurUser({
          params: { voteId: id.value, buildingId: buildingId.value }
        });
        brands.value = ((_a = res.result) == null ? void 0 : _a.map((item) => __spreadProps(__spreadValues({}, item), {
          vote_rate: item.voteRate,
          vote_count: item.voteCount,
          brand_name: item.brandName,
          product_name: item.productName,
          pic_url: item.picUrl,
          price: utils_util.calculatePrePrice(item.price, formulaParams.value)
        }))) || [];
      } catch (error) {
      } finally {
        loading.value = false;
      }
    });
    const minSkuPrice = common_vendor.computed(() => {
      var _a;
      if (brands.value.length <= 0) {
        return 0;
      }
      return Math.min(...(_a = brands.value) == null ? void 0 : _a.map((item) => item.price || 0));
    });
    const maxSkuPrice = common_vendor.computed(() => {
      var _a;
      if (brands.value.length <= 0) {
        return 0;
      }
      return Math.max(...(_a = brands.value) == null ? void 0 : _a.map((item) => item.price || 0));
    });
    const formulaParams = common_vendor.ref({ reduction: 0, rate: 1 });
    const fetchPrePrice = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getPrePrice({
          params: {
            communityId: voteData.value.communityId,
            buildingIds: buildingId.value
          }
        });
        formulaParams.value = res.result;
        console.log("res", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const voteData = common_vendor.ref({});
    const fetchVoteInfo = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value,
            buildingId: buildingId.value
          }
        });
        voteData.value = res.result || {};
        voteEndDate.value = res.result.voteEndDate;
        voteEndSeconds.value = utils_util.calculateVoteEndSeconds(voteEndDate.value);
      } catch (error) {
        console.log("error", error);
        voteData.value = null;
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((options) => __async(this, null, function* () {
      id.value = options.id;
      buildingId.value = options.buildingId;
      yield fetchVoteInfo();
      yield fetchPrePrice();
      fetchVoteResult();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isDays: true,
          isColon: false,
          size: "42",
          width: "50",
          height: "50",
          borderColor: "transparent",
          background: "transparent",
          value: common_vendor.unref(voteEndSeconds)
        }),
        b: common_vendor.p({
          title: "你的投票结果"
        }),
        c: common_vendor.f(common_vendor.unref(brands), (brand, index, i0) => {
          return {
            a: "0e181f56-3-" + i0 + ",0e181f56-0",
            b: common_vendor.p({
              brand,
              index,
              showPrice: true
            }),
            c: index
          };
        }),
        d: common_vendor.t(common_vendor.unref(minSkuPrice)),
        e: common_vendor.t(common_vendor.unref(maxSkuPrice)),
        f: common_vendor.o(handleModifyVote),
        g: common_vendor.p({
          type: "primary",
          width: "184rpx",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "修改投票",
          disabled: common_vendor.unref(brands).length == 0
        }),
        h: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        i: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0e181f56"]]);
wx.createPage(MiniProgramPage);
