"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (VoteNoticeItem + _easycom_fui_button + _easycom_fui_loading)();
}
const VoteNoticeItem = () => "../voteNoticeDetail/components/voteNoticeItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const voteData = common_vendor.ref({});
    const fetchVoteResult = (id) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id,
            status: 2
          }
        });
        voteData.value = __spreadProps(__spreadValues({}, res.result), {
          vote_rate: res.result.voteRate,
          vote_count: res.result.voteCount,
          brand_name: res.result.resultBrandName,
          brandLogo: res.result.resultBrandPic,
          skuName: res.result.skuName,
          product_name: res.result.resultProductName,
          pic_url: res.result.resultBrandPic
        });
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const handleVoteDetail = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/votehasEndResultDetail/index?id=${voteData.value.id}`
      });
    };
    common_vendor.onLoad((options) => {
      fetchVoteResult(options.id);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isShowIndex: false,
          showBottomBorder: false,
          brand: common_vendor.unref(voteData)
        }),
        b: common_vendor.p({
          height: "84rpx",
          radius: "42rpx",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          ["border-width"]: "0",
          text: "查看合约"
        }),
        c: common_vendor.o(handleVoteDetail),
        d: common_vendor.p({
          height: "84rpx",
          radius: "42rpx",
          background: "linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)",
          ["border-width"]: "0",
          text: "查看投票详情"
        }),
        e: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        f: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a0f3029a"]]);
wx.createPage(MiniProgramPage);
