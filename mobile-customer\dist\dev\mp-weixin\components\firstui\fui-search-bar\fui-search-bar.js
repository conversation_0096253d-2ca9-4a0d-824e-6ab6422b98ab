"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "fui-search-bar",
  emits: ["clear", "focus", "blur", "click", "cancel", "input", "update:modelValue", "search"],
  props: {
    background: {
      type: String,
      default: ""
    },
    //搜索栏上下padding（padding-top，padding-bottom）
    paddingTb: {
      type: [Number, String],
      default: 16
    },
    paddingLr: {
      type: [Number, String],
      default: 24
    },
    height: {
      type: [Number, String],
      default: 72
    },
    radius: {
      type: [Number, String],
      default: 8
    },
    color: {
      type: String,
      default: ""
    },
    //input框背景色
    inputBackground: {
      type: String,
      default: "#fff"
    },
    focus: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: "请输入搜索关键词"
    },
    isLeft: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Number, String],
      default: ""
    },
    modelValue: {
      type: [Number, String],
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cancel: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    cancelColor: {
      type: String,
      default: "#7F7F7F"
    },
    searchText: {
      type: String,
      default: "搜索"
    },
    searchColor: {
      type: String,
      default: ""
    },
    //是否显示搜索输入框
    showInput: {
      type: Boolean,
      default: true
    },
    //是否显示输入框占位标签，当平台不支持focus属性时可隐藏
    showLabel: {
      type: Boolean,
      default: true
    },
    //v2.1.0
    fixed: {
      type: Boolean,
      default: false
    }
  },
  created() {
    if (this.value && !this.modelValue) {
      this.val = String(this.value);
    } else {
      this.val = String(this.modelValue);
    }
    if (!this.showLabel || !this.fixed || this.focus || this.val.length > 0) {
      this.plholder = this.placeholder;
    }
    if (this.focus || this.val.length > 0) {
      this.isSearch = true;
    }
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.isFocus = this.focus;
      }, 300);
    });
  },
  watch: {
    focus(val) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.isFocus = val;
          this.isSearch = val;
        }, 20);
      });
    },
    modelValue(newVal) {
      this.val = String(newVal);
      if (this.focus || this.val.length > 0) {
        this.isSearch = true;
      }
    },
    value(val) {
      this.val = String(val);
      if (this.focus || this.val.length > 0) {
        this.isSearch = true;
      }
    },
    placeholder(val) {
      if (!this.showLabel || !this.fixed) {
        this.plholder = this.placeholder;
      }
    }
  },
  computed: {
    getSearchColor() {
      let color = this.searchColor;
      return color;
    }
  },
  data() {
    return {
      isSearch: false,
      isFocus: false,
      val: "",
      plholder: ""
    };
  },
  methods: {
    clearInput() {
      this.val = "";
      this.isFocus = false;
      this.$emit("input", this.val);
      this.$emit("update:modelValue", this.val);
      common_vendor.index.hideKeyboard();
      this.$emit("clear");
    },
    inputFocus(e) {
      if (!this.showLabel) {
        this.isSearch = true;
      }
      this.$emit("focus", e);
    },
    inputBlur(e) {
      this.isFocus = false;
      if (!this.cancel && !this.val) {
        if (this.fixed && this.showLabel) {
          this.plholder = "";
        }
        this.isSearch = false;
      }
      this.$emit("blur", e);
    },
    onShowInput() {
      if (!this.disabled && this.showInput) {
        this.isSearch = true;
        if (this.fixed && this.showLabel) {
          this.plholder = this.placeholder;
        }
        this.$nextTick(() => {
          setTimeout(() => {
            this.isFocus = true;
          }, 50);
        });
      }
      this.$emit("click", {});
    },
    hideInput() {
      if (this.fixed && this.showLabel) {
        this.plholder = "";
      }
      this.isSearch = false;
      this.isFocus = false;
      common_vendor.index.hideKeyboard();
      this.$emit("cancel", {});
    },
    inputChange(e) {
      this.val = e.detail.value;
      this.$emit("input", this.val);
      this.$emit("update:modelValue", this.val);
    },
    search() {
      this.$emit("search", {
        detail: {
          value: this.val
        }
      });
    },
    reset() {
      this.isSearch = false;
      this.isFocus = false;
      this.val = "";
      common_vendor.index.hideKeyboard();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.showInput
  }, $props.showInput ? common_vendor.e({
    b: !$props.color ? 1 : "",
    c: $props.color,
    d: $props.height + "rpx",
    e: $data.plholder,
    f: $data.val,
    g: $data.isFocus,
    h: $props.disabled,
    i: common_vendor.o((...args) => $options.inputBlur && $options.inputBlur(...args)),
    j: common_vendor.o((...args) => $options.inputFocus && $options.inputFocus(...args)),
    k: common_vendor.o((...args) => $options.inputChange && $options.inputChange(...args)),
    l: common_vendor.o((...args) => $options.search && $options.search(...args)),
    m: $data.val.length > 0 && !$props.disabled
  }, $data.val.length > 0 && !$props.disabled ? {
    n: common_vendor.o((...args) => $options.clearInput && $options.clearInput(...args))
  } : {}, {
    o: !$data.isFocus && !$data.isSearch && $props.showLabel && !$props.disabled ? 1 : "",
    p: $props.height + "rpx",
    q: $props.radius + "rpx",
    r: $props.inputBackground
  }) : {}, {
    s: !$data.isFocus && !$data.isSearch && $props.showLabel
  }, !$data.isFocus && !$data.isSearch && $props.showLabel ? {
    t: common_vendor.t($props.placeholder),
    v: common_vendor.n($props.isLeft ? "fui-sb__label-left" : "fui-sb__label-center"),
    w: $props.radius + "rpx",
    x: $props.inputBackground,
    y: common_vendor.o((...args) => $options.onShowInput && $options.onShowInput(...args))
  } : {}, {
    z: $props.height + "rpx",
    A: $props.cancel && $data.isSearch && !$data.val && $props.cancelText && $props.cancelText !== true && $props.cancelText !== "true"
  }, $props.cancel && $data.isSearch && !$data.val && $props.cancelText && $props.cancelText !== true && $props.cancelText !== "true" ? {
    B: common_vendor.t($props.cancelText),
    C: $props.cancelColor,
    D: common_vendor.o((...args) => $options.hideInput && $options.hideInput(...args))
  } : {}, {
    E: $data.val && !$props.disabled && $data.isSearch && $props.searchText && $props.searchText !== true && $props.searchText !== "true"
  }, $data.val && !$props.disabled && $data.isSearch && $props.searchText && $props.searchText !== true && $props.searchText !== "true" ? {
    F: common_vendor.t($props.searchText),
    G: !$props.searchColor ? 1 : "",
    H: $options.getSearchColor,
    I: common_vendor.o((...args) => $options.search && $options.search(...args))
  } : {}, {
    J: !$props.background ? 1 : "",
    K: $props.background,
    L: $props.paddingTb + "rpx",
    M: $props.paddingTb + "rpx",
    N: $props.paddingLr + "rpx",
    O: $props.paddingLr + "rpx"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-31d19ba7"]]);
wx.createComponent(Component);
