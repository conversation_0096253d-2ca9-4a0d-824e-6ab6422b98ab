"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_myAuth = require("../../service/app/myAuth.js");
const utils_util = require("../../utils/util.js");
if (!Array) {
  const _easycom_fui_section2 = common_vendor.resolveComponent("fui-section");
  const _easycom_fui_data_tag2 = common_vendor.resolveComponent("fui-data-tag");
  const _easycom_fui_form_item2 = common_vendor.resolveComponent("fui-form-item");
  const _easycom_fui_input2 = common_vendor.resolveComponent("fui-input");
  const _easycom_fui_form2 = common_vendor.resolveComponent("fui-form");
  const _easycom_fui_checkbox2 = common_vendor.resolveComponent("fui-checkbox");
  const _easycom_fui_label2 = common_vendor.resolveComponent("fui-label");
  const _easycom_fui_checkbox_group2 = common_vendor.resolveComponent("fui-checkbox-group");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_section2 + _easycom_fui_data_tag2 + _easycom_fui_form_item2 + _easycom_fui_input2 + _easycom_fui_form2 + _easycom_fui_checkbox2 + _easycom_fui_label2 + _easycom_fui_checkbox_group2 + _easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_section = () => "../../components/firstui/fui-section/fui-section.js";
const _easycom_fui_data_tag = () => "../../components/firstui/fui-data-tag/fui-data-tag.js";
const _easycom_fui_form_item = () => "../../components/firstui/fui-form-item/fui-form-item.js";
const _easycom_fui_input = () => "../../components/firstui/fui-input/fui-input.js";
const _easycom_fui_form = () => "../../components/firstui/fui-form/fui-form.js";
const _easycom_fui_checkbox = () => "../../components/firstui/fui-checkbox/fui-checkbox.js";
const _easycom_fui_label = () => "../../components/firstui/fui-label/fui-label.js";
const _easycom_fui_checkbox_group = () => "../../components/firstui/fui-checkbox-group/fui-checkbox-group.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_section + _easycom_fui_data_tag + _easycom_fui_form_item + _easycom_fui_input + _easycom_fui_form + _easycom_fui_checkbox + _easycom_fui_label + _easycom_fui_checkbox_group + _easycom_fui_button + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "applyAuth",
  setup(__props) {
    const formRef = common_vendor.ref();
    const formData = common_vendor.ref({
      // 成员类型 0：我的伴侣   1我的子女   2  我的父母
      accessRelationType: 1,
      name: "",
      cardCode: ""
    });
    let loading = common_vendor.ref(false);
    const options = common_vendor.ref([
      {
        text: "伴侣",
        value: 0
      },
      {
        text: "子女",
        value: 1
      },
      {
        text: "父母",
        value: 2
      }
    ]);
    const rules = [
      {
        name: "name",
        rule: ["required"],
        msg: ["请输入姓名"]
      },
      // 身份证号 生产环境校验真实的身份证号
      {
        name: "cardCode",
        rule: ["required"],
        msg: ["请输入身份证号"]
      }
    ];
    const isAgree = common_vendor.ref(false);
    const handleAgree = (e) => {
      console.log("isAgree", e);
      isAgree.value = e.checked;
    };
    const successPageUrl = "http://10.0.9.193:8012/redirectPages/identityAuthRedirect.html";
    const handleSubmit = () => {
      console.log("handleSubmit");
      if (isAgree.value === false) {
        utils_util.showTextToast("请先阅读并同意授权须知");
        return;
      }
      formRef.value.validator(formData.value, rules).then((res) => __async(this, null, function* () {
        var _a;
        console.log("res", res);
        if (res.isPassed) {
          console.log("formData", formData.value);
          try {
            loading.value = true;
            const res2 = yield service_app_myAuth.applyAuth({
              body: {
                name: formData.value.name,
                cardCode: formData.value.cardCode,
                accessRelationType: formData.value.accessRelationType,
                successPage: successPageUrl
              }
            });
            console.log("res", res2);
            if ((_a = res2.result) == null ? void 0 : _a.authUrl) {
              const url = encodeURIComponent(res2.result.authUrl);
              common_vendor.index.redirectTo({
                url: `/pages-sub/myAuth/faceAuth?url=${url}`
              });
            }
          } catch (error) {
            console.log("error", error);
          } finally {
            loading.value = false;
          }
        } else {
          utils_util.showTextToast(res.errorMsg);
        }
      }));
    };
    const handleShowAuthNotice = () => {
      common_vendor.index.navigateTo({ url: "/pages-sub/authNotice/index" });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isLine: true,
          title: "授权人信息",
          descr: "请填写授权人信息"
        }),
        b: common_vendor.o(($event) => common_vendor.unref(formData).accessRelationType = $event),
        c: common_vendor.p({
          defaultBorderColor: "#FBDA41",
          background: "#F1F4FA",
          activeBgColor: "#FEDF48",
          ["border-color"]: "#FBDA41",
          ["active-color"]: "#333",
          radius: "24",
          options: common_vendor.unref(options),
          modelValue: common_vendor.unref(formData).accessRelationType
        }),
        d: common_vendor.p({
          padding: ["30rpx", "0"],
          labelWidth: "100%",
          label: "您是授权人的哪位家庭成员",
          bottomBorder: false,
          prop: "accessRelationType",
          ["error-align"]: "left"
        }),
        e: common_vendor.o(($event) => common_vendor.unref(formData).name = $event),
        f: common_vendor.p({
          ["text-align"]: "right",
          padding: [0],
          borderBottom: false,
          backgroundColor: "transparent",
          placeholder: "授权人姓名",
          modelValue: common_vendor.unref(formData).name
        }),
        g: common_vendor.p({
          label: "授权人姓名",
          padding: ["30rpx", "0"],
          labelWidth: 200,
          prop: "name",
          ["error-align"]: "left"
        }),
        h: common_vendor.o(($event) => common_vendor.unref(formData).cardCode = $event),
        i: common_vendor.p({
          maxlength: 18,
          type: "cardCode",
          ["text-align"]: "right",
          padding: [0],
          borderBottom: false,
          backgroundColor: "transparent",
          placeholder: "请输入证件号",
          modelValue: common_vendor.unref(formData).cardCode
        }),
        j: common_vendor.p({
          padding: ["30rpx", "0"],
          label: "授权人身份证号",
          labelWidth: 280,
          prop: "cardCode",
          ["error-align"]: "left",
          bottomBorder: false
        }),
        k: common_vendor.sr(formRef, "fe1f774f-2,fe1f774f-0", {
          "k": "formRef"
        }),
        l: common_vendor.p({
          ["error-position"]: "1",
          labelSize: "24",
          top: "0",
          model: common_vendor.unref(formData),
          show: false
        }),
        m: common_vendor.o(handleAgree),
        n: common_vendor.p({
          checked: common_vendor.unref(isAgree),
          scaleRatio: 0.9
        }),
        o: common_vendor.p({
          name: "checkbox"
        }),
        p: common_vendor.o(handleShowAuthNotice),
        q: common_vendor.p({
          color: "#FF9900",
          type: "link",
          size: 24
        }),
        r: common_vendor.o(handleSubmit),
        s: common_vendor.p({
          type: "primary"
        }),
        t: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        v: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fe1f774f"]]);
wx.createPage(MiniProgramPage);
