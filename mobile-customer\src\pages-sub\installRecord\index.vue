<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '安装记录',
  },
}
</route>

<template>
  <z-paging
    ref="paging"
    :auto-hide-loading-after-first-loaded="false"
    loading-full-fixed
    :paging-style="{ 'background-color': '#f7f7f7' }"
    v-model="dataList"
    :auto-show-back-to-top="true"
    :back-to-top-bottom="200"
    @query="queryList"
    :auto="true"
    lower-threshold="150rpx"
  >
    <template #loading>
      <fui-loading isMask></fui-loading>
    </template>
    <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
    <template #empty>
      <fui-empty
        :width="386"
        :height="280"
        src="/static/images/img_data_3x.png"
        isFixed
        title="暂无数据"
      ></fui-empty>
    </template>

    <view class="mt20rpx">
      <view class="mb-20rpx" v-for="item in dataList" :key="item.id">
        <install-record-item :item="item" />
      </view>
    </view>
    <template #bottom>
      <safe-bottom-zone></safe-bottom-zone>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import installRecordItem from './components/installRecordItem.vue'

//
const dataList = ref([])

const paging = ref(null)

const queryList = async (pageNo, pageSize) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
  }

  console.log('params', params)
  paging.value.complete([{ id: 1 }, { id: 2 }, { id: 3 }], true)
  // 此处请求仅为演示，请替换为自己项目中的请求
  try {
    // const res = await getMyVote({ params })
    // console.log('getMyVote', res)
    // paging.value.complete(res?.result?.records || [], true)
  } catch (error) {
    // paging.value.complete(false)
  }
}
</script>

<style lang="scss" scoped>
//
</style>
