"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteResult",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const props = __props;
    const content = common_vendor.computed(() => {
      return `${props.item.voteName}投票结果公示。`;
    });
    const handleToVoteResultDetail = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/votePublicDetail/index?id=${props.item.id}`
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.t(common_vendor.unref(content)),
        c: common_vendor.t(__props.item.resultBrandName || "未投出"),
        d: common_vendor.t(__props.item.voteResultTime),
        e: common_vendor.o(handleToVoteResultDetail)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-461db6be"]]);
wx.createComponent(Component);
