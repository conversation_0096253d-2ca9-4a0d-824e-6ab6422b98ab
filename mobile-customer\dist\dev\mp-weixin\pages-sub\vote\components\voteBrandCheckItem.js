"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const pagesSub_vote_vota_data = require("../vota.data.js");
if (!Array) {
  const _easycom_fui_checkbox2 = common_vendor.resolveComponent("fui-checkbox");
  const _easycom_fui_input_number2 = common_vendor.resolveComponent("fui-input-number");
  (_easycom_fui_checkbox2 + _easycom_fui_input_number2)();
}
const _easycom_fui_checkbox = () => "../../../components/firstui/fui-checkbox/fui-checkbox.js";
const _easycom_fui_input_number = () => "../../../components/firstui/fui-input-number/fui-input-number.js";
if (!Math) {
  (_easycom_fui_checkbox + _easycom_fui_input_number)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteBrandCheckItem",
  props: {
    brand: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      default: 0
    },
    selectedCount: {
      type: Number,
      default: 1e3
    },
    // 是否监听排序 0：不监听 1：监听
    // 如果不是手动修改排序，则不监听
    // 比如：点击checkbox时，会给sortOrder赋值，则不监听
    isListenSort: {
      type: Number,
      default: 0
    },
    buildingIds: {
      type: String,
      default: ""
    },
    voteId: {
      type: String,
      default: ""
    }
  },
  emits: ["onCheckBoxChange", "changeVoteSort", "onSkuClick"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const imgPrefix = "http://10.0.9.216:9000/dfloorpublic";
    common_vendor.ref(0);
    const brandLogo = common_vendor.computed(() => {
      console.log("brandLogo", props.brand);
      return props.brand.brandPic ? `${imgPrefix}/${props.brand.brandPic}` : common_assets.failedImg;
    });
    const skuChecked = common_vendor.computed(() => {
      var _a, _b;
      return ((_b = (_a = props.brand) == null ? void 0 : _a.skus) == null ? void 0 : _b.find((item) => item.checked)) || {};
    });
    const voteRateNumber = common_vendor.computed(() => {
      var _a;
      const voteRate = ((_a = props == null ? void 0 : props.brand) == null ? void 0 : _a.voteRate) || "0%";
      return voteRate === "-" ? 0 : parseFloat(voteRate) || 0;
    });
    const emit = __emit;
    const handleCheckBoxChange = (e) => {
      console.log("点击checkbox", e);
      emit("onCheckBoxChange", e);
    };
    const changeVoteSort = (e) => __async(this, null, function* () {
      console.log("改变投票顺序", e);
      if (e.params === 0) {
        return;
      }
      emit("changeVoteSort", props.brand, e.value);
    });
    const handleSkuClick = (sku) => {
      console.log("点击sku", sku);
      sku.checked = !sku.checked;
      if (sku.checked) {
        props.brand.skus.forEach((item) => {
          if (item.code !== sku.code) {
            item.checked = false;
          }
        });
      }
      emit("onSkuClick", sku);
    };
    const handleBrandLogoClick = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/brandDetail/index?id=${props.brand.productId}&buildingId=${props.buildingIds}&voteId=${props.voteId}&communityId=${props.brand.communityId}&supplierId=${props.brand.supplierId}`,
        success: (res) => {
          console.log("success", res);
        }
      });
    };
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: __props.brand.stockStatus !== common_vendor.unref(pagesSub_vote_vota_data.StockStatusEnum).SELL_OUT
      }, __props.brand.stockStatus !== common_vendor.unref(pagesSub_vote_vota_data.StockStatusEnum).SELL_OUT ? {
        b: common_vendor.o(handleCheckBoxChange),
        c: common_vendor.p({
          value: __props.brand.name,
          checked: !!__props.brand.isChecked
        })
      } : {}, {
        d: common_vendor.unref(brandLogo),
        e: common_vendor.o(handleBrandLogoClick),
        f: common_vendor.t(__props.brand.brandName),
        g: common_vendor.n(__props.brand.tagType),
        h: common_vendor.t(__props.brand.productName),
        i: common_vendor.f(__props.brand.skus, (sku, index, i0) => {
          return {
            a: common_vendor.t(sku.name),
            b: common_vendor.n(sku.checked ? "check-item-active" : ""),
            c: index,
            d: common_vendor.o(($event) => handleSkuClick(sku), index)
          };
        }),
        j: common_vendor.unref(voteRateNumber),
        k: common_vendor.t(__props.brand.voteCount),
        l: common_vendor.t(__props.brand.voteRate),
        m: common_vendor.t((_a = common_vendor.unref(skuChecked)) == null ? void 0 : _a.price),
        n: __props.brand.stockStatus !== null && __props.brand.stockStatus !== void 0
      }, __props.brand.stockStatus !== null && __props.brand.stockStatus !== void 0 ? {
        o: common_vendor.t(common_vendor.unref(pagesSub_vote_vota_data.stockStatusMap)[__props.brand.stockStatus].text),
        p: common_vendor.unref(pagesSub_vote_vota_data.stockStatusMap)[__props.brand.stockStatus].bgColor,
        q: common_vendor.unref(pagesSub_vote_vota_data.stockStatusMap)[__props.brand.stockStatus].textColor
      } : {}, {
        r: __props.brand.isChecked
      }, __props.brand.isChecked ? {
        s: common_vendor.o(changeVoteSort),
        t: common_vendor.p({
          modelValue: __props.brand.sortOrder,
          min: 1,
          max: __props.selectedCount,
          params: __props.isListenSort
        })
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-13a7e1c6"]]);
wx.createComponent(Component);
