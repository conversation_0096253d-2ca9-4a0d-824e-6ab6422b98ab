/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.welcome.data-v-59003337 {
  background: #145db2;
}
.top-title.data-v-59003337 {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 56rpx;
  color: #ffffff;
  margin-left: 30rpx;
}
.top-nanjing.data-v-59003337 {
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 36rpx;
  padding: 18rpx 40rpx;
}
.top-nanjing .rigion.data-v-59003337 {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #0b76e1;
}
.common-page.data-v-59003337 {
  padding: 40rpx;
}
.common-page .header-1.data-v-59003337 {
  width: 258rpx;
}
.common-page .header-2.data-v-59003337 {
  width: 373rpx;
}
.common-page .header-3.data-v-59003337 {
  width: 265rpx;
}
.common-page .header-4.data-v-59003337 {
  width: 337rpx;
}
.common-page .header.data-v-59003337 {
  height: 85rpx;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  align-items: center;
  background-color: #0c488f;
  margin-top: 20rpx;
  color: #ffffff;
  padding-right: 20rpx;
}
.common-page .header .number.data-v-59003337 {
  margin-left: 20rpx;
  width: 35rpx;
  height: 28rpx;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 36rpx;
  color: #ffffff;
  line-height: 36rpx;
}
.common-page .header .divide-line.data-v-59003337 {
  margin-left: 30rpx;
  margin-top: 5rpx;
  width: 2rpx;
  height: 36rpx;
  background: #86a4c7;
}
.common-page .header .title.data-v-59003337 {
  margin-left: 22rpx;
  height: 34rpx;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 36rpx;
  color: #ffffff;
  line-height: 36rpx;
}
.common-page .content-card.data-v-59003337 {
  background-color: #4a9ae0;
  border-radius: 0 20rpx 20rpx 20rpx;
  overflow: hidden;
  padding: 30rpx;
}
.common-page .content-card .intro-section.data-v-59003337,
.common-page .content-card .time-section.data-v-59003337 {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.common-page .content-card .intro-section .section-header.data-v-59003337,
.common-page .content-card .time-section .section-header.data-v-59003337 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.common-page .content-card .intro-section .section-header .icon.data-v-59003337,
.common-page .content-card .time-section .section-header .icon.data-v-59003337 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 10rpx;
}
.common-page .content-card .intro-section .section-header .section-title.data-v-59003337,
.common-page .content-card .time-section .section-header .section-title.data-v-59003337 {
  height: 31rpx;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 32rpx;
  color: #145db2;
  margin-bottom: 16rpx;
}
.common-page .content-card .intro-section .section-content.data-v-59003337,
.common-page .content-card .time-section .section-content.data-v-59003337 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
.common-page .content-card .intro-section .section-content .highlight.data-v-59003337,
.common-page .content-card .time-section .section-content .highlight.data-v-59003337 {
  height: 27rpx;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 26rpx;
  color: #145db2;
  line-height: 48rpx;
}
.common-page .content-card .intro-section .section-content .regular-text.data-v-59003337,
.common-page .content-card .time-section .section-content .regular-text.data-v-59003337 {
  display: block;
}
.bottom-zone.data-v-59003337 {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #145db2;
}