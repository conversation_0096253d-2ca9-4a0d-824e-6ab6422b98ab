<template>
  <view class="container" v-if="voteData.status">
    <title-header v-if="showTitle" title="投票结果" />
    <!-- todo 显示很遗憾 -->
    <view class="p-30rpx bg-[#ffffff]" v-if="voteData.status == 4">
      <!-- 顶部恭喜标题 -->
      <view class="header">
        <image
          class="celebrate-icon"
          src="@/pages-sub/static/images/dt_009_2.png"
          mode="widthFix"
        ></image>
        <view class="text-[#529AFF] text-48rpx font-bold">很遗憾</view>
      </view>
      <view class="text-28rpx text-[#333333] mt20rpx">
        <view class="line-height-[44rpx] indent-[2em]">
          您的楼栋投票人和投票套内面积不足有3/4，不符合法定条件，您所在的楼栋本次投票未能达成共识。
        </view>
        <view class="line-height-[44rpx] indent-[2em]">
          我们将尽快启动下一轮投票，您无需重新投票，我们将您本次投票的结果作为下一轮投票的内容，当然，您也可以重新修改您的投票。
        </view>
        <view class="line-height-[1.6]">期待您的楼栋尽快达成共识。</view>
      </view>
    </view>
    <view class="p-30rpx bg-[#ffffff]" v-else>
      <view>
        <!-- 顶部恭喜标题 -->
        <view class="header">
          <image
            class="celebrate-icon"
            src="@/pages-sub/static/images/dt_009_1.png"
            mode="widthFix"
          ></image>
          <view class="title">恭喜您</view>
        </view>

        <!-- 投票成功提示 -->
        <view class="success-tip">
          <text class="tip-text">
            您的楼栋已经有3/4的业主且3/4有效面积的业主参与了投票，同时投票内的业主超过2/3达成了共识，符合法定条件，投票结果已经揭晓。
          </text>
        </view>
        <view>
          <view class="text-28rpx text-[#333333]">本次投票获选的品牌是：</view>
          <view>
            <vote-notice-item
              :isShowIndex="false"
              :showBottomBorder="false"
              :brand="brandInfo"
              :showSkuRate="true"
            />
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="price-info">
          <view class="price-item">
            <text class="price-desc">
              该电梯总价¥{{ voteData.price }}，国补每台¥{{ voteData.subsidyPrice }}，
              电梯回收残值每台¥0， 您最终承担金额：
              <text class="text-32rpx text-[#e74c3c]">￥{{ voteData.finalPrice }}。</text>
            </text>
          </view>
          <view class="price-item">
            <text class="price-desc">
              若您的维修基金账户内余额足够，则优先扣除您的维修基金余额，若不足，需要您另外进行支付。
            </text>
            <!-- <text class="text-32rpx text-[#e74c3c]">¥2000</text>
            <text class="price-desc">
              ，您还需支付
              <text class="text-32rpx text-[#e74c3c]">¥689。</text>
            </text> -->
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import VoteNoticeItem from '@/pages-sub/voteNoticeDetail/components/voteNoticeItem.vue'

const props = defineProps({
  voteData: {
    type: Object,
    default: () => {},
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
})

const brandInfo = computed(() => {
  return {
    vote_rate: props.voteData.voteRateProduct,
    vote_count: props.voteData.voteCountProduct,
    brand_name: props.voteData.resultBrandName,
    brandLogo: props.voteData.resultBrandPic,
    skuName: props.voteData.skuName,
    product_name: props.voteData.resultProductName,
    pic_url: props.voteData.resultBrandPic,
    skuRate: props.voteData.skuRate,
    skuCode_dictText: props.voteData.skuCode_dictText,
  }
})

// 页面逻辑
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  margin-bottom: 20rpx;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;

  .celebrate-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 20rpx;
  }

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #e74c3c;
  }
}

.success-tip {
  text-indent: 2em;
  .tip-text {
    font-size: 28rpx;
    line-height: 1.6;
    display: block;
    margin-bottom: 20rpx;
    color: #e74c3c;
  }

  .question {
    font-size: 28rpx;
    color: #333333;
    font-weight: bold;
  }
}

.price-info {
  margin-bottom: 30rpx;

  .price-item {
    margin-bottom: 16rpx;
    text-indent: 2em;
    .price-desc {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.5;
    }

    .price-highlight {
      font-size: 28rpx;
      color: #e74c3c;
      font-weight: bold;
    }
  }
}
</style>
