<wxs src="./index.wxs" module="handler"/>
<view class="fui-swipe__action-wrap data-v-bff3e82f" style="{{'margin-top:' + g + ';' + ('margin-bottom:' + h)}}"><view class="fui-swipe__action-inner data-v-bff3e82f" show="{{b}}" change:show="{{handler.showChange}}" threshold="{{c}}" change:threshold="{{handler.thresholdChange}}" clickclose="{{d}}" change:clickclose="{{handler.clickCloseChange}}" disabled="{{e}}" change:disabled="{{handler.disabledChange}}" data-app="{{f}}" bindtouchstart="{{handler.touchstart}}" bindtouchmove="{{handler.touchmove}}" bindtouchend="{{handler.touchend}}" bindmousedown="{{handler.mousedown}}" bindmousemove="{{handler.mousemove}}" bindmouseup="{{handler.mouseup}}" bindmouseleave="{{handler.mouseleave}}"><view class="fui-swipe__action-left data-v-bff3e82f"><slot></slot></view><view class="fui-swipe__action-right data-v-bff3e82f"><block wx:if="{{$slots.buttons}}"><slot name="buttons"></slot></block><block wx:else><view wx:for="{{a}}" wx:for-item="item" wx:key="e" class="fui-swipe__action-btn data-v-bff3e82f" style="{{'background:' + item.d}}" bindtouchstart="{{item.f}}" bindtouchend="{{item.g}}" catchtap="{{item.h}}"><text class="fui-swipe__action-text data-v-bff3e82f" style="{{'font-size:' + item.b + ';' + ('color:' + item.c)}}">{{item.a}}</text></view></block></view></view></view>