"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_myAuth = require("../../service/app/myAuth.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_button + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const authList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const relationMap = {
      0: "伴侣",
      1: "子女",
      2: "父母"
    };
    const handleCancelAuth = (item) => {
      common_vendor.index.showModal({
        title: "提示",
        content: `确定${item.accessType ? "取消被授权" : "取消授权"}吗？`,
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            try {
              loading.value = true;
              const res2 = yield service_app_myAuth.cancelAuth({
                params: {
                  id: item.id
                }
              });
              fetchAuthList();
              console.log("res", res2);
            } catch (error) {
              console.log("error", error);
            } finally {
              loading.value = false;
            }
          }
        })
      });
    };
    const fetchAuthList = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_myAuth.getMyAuthList({});
        authList.value = res.result || [];
        loading.value = false;
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const handleAddAuth = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/myAuth/addAuth"
      });
    };
    const handleApplyAuth = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/myAuth/applyAuth"
      });
    };
    common_vendor.onShow(() => {
      fetchAuthList();
    });
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(authList).length === 0
      }, common_vendor.unref(authList).length === 0 ? {} : {
        b: common_vendor.f(common_vendor.unref(authList), (item, index, i0) => {
          return {
            a: common_vendor.t(relationMap[item.accessRelationType]),
            b: common_vendor.t(item.accessType === 1 ? "授权人" : "被授权人"),
            c: common_vendor.n(item.accessType ? "auth-status-active" : ""),
            d: common_vendor.t(item.accessType === 1 ? item.name : item.accessName),
            e: common_vendor.t(item.accessType === 1 ? item.cardCode : item.accessCardCode),
            f: common_vendor.t(item.createTime),
            g: common_vendor.t(item.accessType ? "取消被授权" : "取消授权"),
            h: common_vendor.n(item.accessType ? "btn-cancel-active" : ""),
            i: common_vendor.o(($event) => handleCancelAuth(item), index),
            j: index
          };
        })
      }, {
        c: common_vendor.o(handleAddAuth),
        d: common_vendor.p({
          height: "84rpx",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          ["border-width"]: "0",
          text: "为家人授权"
        }),
        e: common_vendor.o(handleApplyAuth),
        f: common_vendor.p({
          height: "84rpx",
          background: "linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)",
          ["border-width"]: "0",
          text: "申请被授权"
        }),
        g: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        h: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e46a49ed"]]);
wx.createPage(MiniProgramPage);
