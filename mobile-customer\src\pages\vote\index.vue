<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '投票',
    componentPlaceholder: {
      'base-chart': 'view',
    },
  },
}
</route>

<template>
  <view
    class="height-tabbar overflow-y-auto"
    :class="[!isLogined || !userInfo.isRealAuth ? 'bg-[#ffffff]' : 'bg-[#f7f7f7]']"
  >
    <!-- <fui-nav-bar title="投票" splitLine :size="13">
      <template #default>
        <view>
          <VoteHouseSelect @house-vote-select-click="handleHouseVoteSelectClick" />
        </view>
      </template>
    </fui-nav-bar> -->
    <!-- 未登录 -->
    <view v-if="!isLogined">
      <resultTitle>
        <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
          您还没有登录，无法获得您的投票信息，请立即登录
        </view>
        <fui-button
          btn-size="medium"
          text="立即登录"
          type="primary"
          bold
          :margin="['48rpx', '0', '24rpx']"
          @click="handleLoginClick"
        ></fui-button>
      </resultTitle>
    </view>
    <!-- 未认证 -->
    <view v-else-if="!userInfo.isRealAuth">
      <resultTitle imgUrl="/static/images/dt_010_2.png">
        <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
          您还未实名认证，无法参与投票
        </view>
        <fui-button
          btn-size="medium"
          text="立即认证"
          type="primary"
          bold
          :margin="['48rpx', '0', '24rpx']"
          @click="handleCertificationClick"
        ></fui-button>
      </resultTitle>
    </view>
    <view v-show="userInfo.isRealAuth">
      <view class="max-w-300rpx ml30rpx">
        <VoteHouseSelect
          ref="voteHouseSelectRef"
          @house-vote-select-click="handleHouseVoteSelectClick"
          @house-vote-result="handleHouseVoteResult"
        />
      </view>
      <!-- 没有投票信息 -->
      <view v-if="!voteData">
        <!-- <fui-empty
          :width="386"
          :height="280"
          src="/static/images/img_data_3x.png"
          title="您没有可投票的房产哦！"
          isFixed
        ></fui-empty> -->

        <resultTitle imgUrl="/static/images/img_data_3x.png" v-if="houseVoteResult === 1">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您已经完成投票，可至我的-我的投票中进行查看
          </view>
          <fui-button
            btn-size="medium"
            text="查看投票"
            type="primary"
            bold
            :margin="['48rpx', '0', '24rpx']"
            @click="handleJumpMyVote"
          ></fui-button>
        </resultTitle>

        <resultTitle imgUrl="/static/images/img_data_3x.png" v-else-if="houseVoteResult === 2">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您的房产的投票活动还未开始，请留意相关的公告，尽情期待
          </view>
        </resultTitle>

        <resultTitle imgUrl="/static/images/img_data_3x.png" v-else-if="houseVoteResult === 3">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您的房产对应的投票活动已经结束，请至消息中心中查看投票结果
          </view>
          <fui-button
            btn-size="medium"
            text="查看消息"
            type="primary"
            bold
            :margin="['48rpx', '0', '24rpx']"
            @click="handleJumpMessage"
          ></fui-button>
        </resultTitle>
      </view>

      <view v-else>
        <view class="bg-[#ffffff] p-30rpx">
          <view class="text-30rpx font-bold text-[#333] mb20rpx">
            您好，{{ userInfo.realname }}，您正在进行{{ voteData.communityName
            }}{{ voteData.buildingNo }}幢的投票
          </view>
          <view
            class="text-26rpx text-[#666] mb10rpx flex"
            v-for="(item, index) in allAddressList"
            :key="index"
          >
            <text class="mr10rpx">产权房: {{ item.location }}</text>
            <fui-tag
              v-if="item.myself == 1"
              text="被授权"
              type="success"
              :padding="['6rpx', '6rpx']"
            ></fui-tag>
          </view>
          <view class="text-26rpx text-[#1296db]">投票截止日期: {{ voteData.voteEndDate }}</view>
        </view>

        <view class="bg-[#ffffff] px-30rpx pb-10rpx mt20rpx">
          <fui-tabs
            :tabs="buildingListNew"
            center
            selectedColor="#005ED1"
            sliderBackground="#005ED1"
          ></fui-tabs>
          <!-- 投票进度条 -->
          <view class="my20rpx">
            <progress
              :percent="voteData.voteRateValue"
              :stroke-width="12"
              :border-radius="8"
              activeColor="#05D6A2"
            />
            <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
              <text>本楼栋票数: {{ voteData.voteCount }}</text>
              <text>投票率: {{ voteData.voteRate }}</text>
            </view>
          </view>

          <view class="my20rpx">
            <progress
              :percent="voteData.voteAreaRateValue"
              :stroke-width="12"
              :border-radius="8"
              activeColor="#FFAE00"
            />
            <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
              <text>本楼栋投票面积: {{ voteData.voteArea }}m²</text>
              <text>投票率: {{ voteData.voteAreaRate }}</text>
            </view>
          </view>

          <view class="text24rpx font-bold text-center" v-if="voteData.id">
            {{
              !voteData.pass ? '本楼栋暂未达到有效选票数，请尽快投票。' : '本楼栋已达到有效选票数。'
            }}
          </view>
        </view>
        <!-- 倒计时 -->
        <view class="bg-[#ffffff] mt20rpx p-30rpx">
          <view class="flex items-center text-28rpx text-[#333]">
            <text>结束倒计时:</text>
            <view class="flex items-center ml20rpx">
              <fui-count-down
                isDays
                :isColon="false"
                size="42"
                width="50"
                height="50"
                borderColor="transparent"
                background="transparent"
                color="#005ED1"
                colonColor="#005ED1"
                :value="voteData.voteEndSeconds"
              ></fui-count-down>
            </view>
          </view>
        </view>

        <view class="bg-[#ffffff] mt20rpx pt10rpx" v-show="nanJingTop10Data.length > 0">
          <TitleHeader title="南京市电梯更新采购品牌前10名" />
          <view class="charts w100%" :style="{ height: nanJingTop10OChartHeight + 'rpx' }">
            <baseChart :options="top10BrandOption" />
          </view>
        </view>
        <view class="bg-[#ffffff] mt20rpx pt10rpx" v-show="top10BrandData.length > 0">
          <TitleHeader title="本小区电梯更新选择前10名" />
          <view class="charts h500rpx w100%">
            <baseChart :options="top10ElevatorOption" />
          </view>
        </view>
        <view class="bg-[#ffffff] my20rpx pt10rpx">
          <TitleHeader title="南京市老旧电梯更新国补进程" />
          <view class="flex h400rpx">
            <view class="w-50% h100%">
              <baseChart :options="govProcessOption" />
            </view>
            <view class="w-50% h100% flex flex-col gap-y-20rpx justify-center text-26rpx text-#666">
              <view>本轮国家补贴名额：{{ govProcessData.nationalSubsidyQuota || '-' }}台</view>
              <view>已经使用国补名额：{{ govProcessData.usedSubsidyQuota || '-' }}台</view>
              <view>每台电梯国家补贴：{{ govProcessData.subsidyPerElevator || '-' }}元</view>
              <view>先到先得，抓紧投票吧</view>
            </view>
          </view>
        </view>
        <view class="mb20rpx flex justify-center w100%">
          <view class="w100% px30rpx">
            <fui-button
              width="100%"
              radius="100rpx"
              height="84rpx"
              border-width="0"
              background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              @click="handleVoteStepOne"
            >
              立即投票
            </fui-button>
          </view>
        </view>
      </view>
    </view>

    <fui-fab
      :width="128"
      @click="fabClick"
      isDrag
      background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
      v-if="isLogined"
    >
      <view class="flex flex-col items-center">
        <view>
          <fui-icon name="edit-fill" color="#fff" :size="46"></fui-icon>
        </view>
        <view class="text-22rpx text-[#fff]">房产纠错</view>
      </view>
    </fui-fab>
    <fui-loading v-if="loading" isMask />

    <!-- 投票须知 -->
    <fui-landscape :show="showModal" :closable="false" @close="closeModal">
      <view class="fui-ani__box">
        <image class="fui-hd__img" src="/static/images/dt_008.jpg" mode="widthFix"></image>
        <view class="fui-flex__center">
          <view class="w100%">
            <VoteNotice height="50vh" />

            <fui-button
              class="text-center"
              radius="100rpx"
              width="100%"
              :disabled="disableBtn"
              disabled-background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              borderColor="rgba(0,0,0,0)"
              border-width="0"
              @click="handleVoteNoticeOperation"
            >
              已同意并开始投票
              <text v-if="timeNum > 0">（{{ timeNum }}s）</text>
            </fui-button>
            <fui-button
              class="text-center mt20rpx"
              radius="100rpx"
              background="#fff"
              color="#000000"
              borderColor="#ffffff"
              @click="handleExitVote"
              text="退出投票"
            ></fui-button>
          </view>
        </view>
      </view>
    </fui-landscape>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import TitleHeader from '@/components/common/titleHeader.vue'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import baseChart from '@/echarts/components/BaseCharts.vue'
import resultTitle from './components/resultTip.vue'

import {
  addVoteNoticeOperation,
  getAccountInfo,
  getAllGrantedAndSelfList,
  getBuildingRateByVote,
  getHasClickVoteNote,
  getVoteRankProductTop10,
  queryBizVoteInfoById,
  queryBrandTop10,
  queryCommunityList,
  queryGovProcess,
  queryTop10Brand,
  queryVoteElevatorList,
} from '@/service/app'
import VoteHouseSelect from './components/voteHouseSelect.vue'
import VoteNotice from '@/pages/vote/components/voteNotice.vue'
import { calculateVoteEndSeconds } from '@/utils/util'
import {
  setGovProcessChartOption,
  setTop10BrandChartOption,
  setTop10ElevatorChartOption,
} from './data'

const loading = ref(false)

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

// 0:未登录 1：未认证 2：已认证
const loginStatus = ref(2)

const voteData = ref(null)

// 投票小区选择组件ref
const voteHouseSelectRef = ref(null)

const handleLoginClick = () => {
  const redirectUrl = `/pages/vote/index`
  uni.navigateTo({
    url: `/pages-sub/mine/login/index?redirectUrl=${redirectUrl}`,
  })
}
// 跳转到认证页面
const handleCertificationClick = () => {
  uni.navigateTo({
    url: '/pages-sub/realname/index',
  })
}
// 点击fud
const fabClick = () => {
  // 跳转去纠错页面
  // todo 根据用户是否有房产跳转
  if (!voteData.value) {
    // 没有房产 直接跳转纠错页
    uni.navigateTo({
      url: '/pages-sub/myHouse/errorCorrection',
    })
  } else {
    // 有投票信息 跳转我的房产页面
    uni.navigateTo({
      url: '/pages-sub/myHouse/index',
    })
  }
}

// 是否禁用弹窗已阅读并开始投票按钮
const disableBtn = ref(true)

// 是否显示投票须知弹窗
const showModal = ref(false)

// 关闭投票须知弹窗
const closeModal = () => {
  disableBtn.value = true
  showModal.value = false
  clearTimeout()
}

// 投票注意事项操作记录
const handleVoteNoticeOperation = () => {
  voteNoticeOperation(1)
  closeModal()
}

// 退出投票
const handleExitVote = () => {
  voteNoticeOperation(0)
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}

// 倒计时相关功能
let timeInterval = null
// 默认倒计时
const timeCount = import.meta.env.VITE_DIALOG_TIMEOUT_COUNT || 1

const timeNum = ref(0)
const startTimeInterval = (time: number = timeCount) => {
  if (timeInterval) {
    clearTimeout()
  }
  timeNum.value = time
  timeInterval = setInterval(() => {
    timeNum.value = timeNum.value - 1
    if (timeNum.value <= 0) {
      clearTimeout()
      disableBtn.value = false
    }
  }, 1000)
}
const clearTimeout = () => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
    timeNum.value = 0
  }
}

// 南京市电梯更新采购品牌前10名图表option

const top10BrandOption = ref(setTop10BrandChartOption([]))

// 本小区电梯更新选择前10名图表option
const top10ElevatorOption = ref(setTop10ElevatorChartOption([]))

// 南京市老旧电梯更新国补进程图表option
const govProcessData = ref({})
const govProcessOption = ref(
  setGovProcessChartOption([
    {
      name: '',
      value: 0,
    },
  ]),
)

// 跳转到具体投票页面
const handleVoteStepOne = () => {
  // 投票截止时间
  const voteEndDate = voteData.value.voteEndDate
  // 如果status等于2 ，提前出结果 跳转到提前结果详情页
  if (voteData.value.status === 2) {
    uni.navigateTo({
      url: `/pages-sub/voteResultsAdvance/index?id=${id.value}`,
    })
  } else {
    // 如果投过票 跳转到投票结果详情页
    if (voteData.value.voted === true) {
      uni.navigateTo({
        url: `/pages-sub/voteResultDetail/index?id=${id.value}&buildingId=${buildingId.value}`,
      })
    } else {
      // 如果没有投过票，则跳转到投票页面
      uni.navigateTo({
        url: `/pages-sub/vote/index?id=${id.value}&buildingId=${buildingId.value}`,
      })
    }
  }
}
// 投票公告id
const id = ref('')

// 楼栋id
const buildingId = ref('')

// 小区id
const communityId = ref('')

// 获取投票公告详情
// 根据投票公告状态 判断要不要弹窗显示
const fetchVoteInfo = async () => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id: id.value,
        buildingId: buildingId.value,
      },
    })
    console.log('queryBizVoteInfoById', res)
    // 投票信息
    // 倒计时
    const voteEndSeconds = calculateVoteEndSeconds(res.result.voteEndDate)
    voteData.value = {
      ...res.result,
      voteEndSeconds,
      voteRateValue: parseFloat(res.result.voteRate),
      voteAreaRateValue: parseFloat(res.result.voteAreaRate),
    }

    if (res.result.communityId) {
      communityId.value = res.result.communityId
      // 获取小区top10 品牌
      fetchTop10Brand()
      // 获取地址列表
      fetchAddressList()
    }
    // 获取南京市电梯前10
    fetchNanJingTop10()
    // 获取南京市老旧电梯更新国补进程
    fetchGovProcess()

    // 判断有没有点击过投票
    fetchHasClickVoteNote()
  } catch (error) {
    console.log('error', error)
    voteData.value = null
  } finally {
    loading.value = false
  }
}
// 获取小区top10 品牌
const top10BrandData: any = ref([])

const fetchTop10Brand = async () => {
  try {
    loading.value = true
    const res = await getVoteRankProductTop10({
      params: {
        voteId: id.value,
      },
    })
    top10BrandData.value = res.result || []

    top10ElevatorOption.value = setTop10ElevatorChartOption(res.result || [])
    console.log('queryTop10Brand', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取南京市电梯前10
const nanJingTop10Data: any = ref([])
const nanJingTop10OChartHeight = ref<number>(120)

const fetchNanJingTop10 = async () => {
  try {
    loading.value = true
    const res = await queryBrandTop10({
      params: {},
    })

    nanJingTop10OChartHeight.value = (res.result.length ?? 0) * 120 + 40

    nanJingTop10Data.value = res.result || []
    top10BrandOption.value = setTop10BrandChartOption(res.result || [])
    console.log('queryTop10Brand', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取南京市老旧电梯更新国补进程
const fetchGovProcess = async () => {
  try {
    loading.value = true
    const res = await queryGovProcess({
      params: {},
    })
    console.log('queryGovProcess', res)
    govProcessData.value = res.result

    if (!res.result.usedPercentage || res.result.usedPercentage === '-') {
      govProcessData.value.usedPercentage = 0
    }
    // usedPercentage 没有数字或者是null，需要处理（%）或者(-)
    const usedPercentage = Number(govProcessData.value.usedPercentage.replace('%', ''))

    govProcessOption.value = setGovProcessChartOption([
      {
        name: `国补已使用${usedPercentage}%`,
        value: usedPercentage,
      },
    ])
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取地址列表
const allAddressList = ref([])

// 地址列表去重 使用computed
const buildingList = computed(() => {
  return allAddressList.value
    ?.map((c) => {
      return {
        ...c,
        name: `${c.buildingNo}幢`,
      }
    })
    ?.filter((item, index, self) => {
      return index === self.findIndex((t) => t.buildingId === item.buildingId)
    })
})

// 获取地址列表
const fetchAddressList = async () => {
  const res = await getAllGrantedAndSelfList({
    params: {
      communityId: communityId.value,
      buildingIds: buildingId.value,
    },
  })
  allAddressList.value = res.result
  if (res.result.length > 0) {
    // 不用处理 现在返回的投票信息就是按照楼栋的
    // handleBuildingChange(res.result[0])
  }
}

const buildingListNew = ref([])

// 获取栋投票数及投票率
const buildVoteInfo = ref({})
const fetchBuildingRateByVote = async (buildingId: string) => {
  try {
    loading.value = true
    const res = await getBuildingRateByVote({
      params: {
        voteId: id.value,
        buildingIds: buildingId,
      },
    })
    if (res.result && res.result.length > 0) {
      const _buildVoteInfo = res.result[0]
      buildVoteInfo.value = {
        ..._buildVoteInfo,
        voteRateValue: parseFloat(_buildVoteInfo.voteRate),
        voteAreaRateValue: parseFloat(_buildVoteInfo.voteAreaRate),
      }
    } else {
      buildVoteInfo.value = {}
    }
  } catch (error) {
    console.log('error', error)
    buildVoteInfo.value = {}
  } finally {
    loading.value = false
  }
}

// 切换楼栋(不需要  已改成只显示当前栋)
const handleBuildingChange = async (e) => {
  const { index, buildingId } = e
  console.log('handleBuildingChange', e)
  // 获取栋投票数及投票率
  await fetchBuildingRateByVote(buildingId)
}

// 切换小区
const handleHouseVoteSelectClick = (select: string) => {
  buildingListNew.value = []
  console.log('handleHouseVoteSelectClick', select)
  id.value = select.value
  buildingId.value = select.buildingId
  // 楼栋tab数据
  buildingListNew.value.push({
    name: `${select?.buildingNo || '-'}幢`,
    value: select?.buildingId,
  })
  fetchVoteInfo()
}

// 查看获取的可投票的房产结果
// 1: 已投票且已结束
// 2: 没有房产参与投票
// 3: 已结束且未投票
const houseVoteResult = ref<number>(0)
const handleHouseVoteResult = (result: number) => {
  console.log('handleHouseVoteResult', result)
  houseVoteResult.value = result
}

// 投票注意事项操作记录
// 0:拒绝 1:同意
const voteNoticeOperation = async (_operationType: number) => {
  try {
    const res = await addVoteNoticeOperation({
      body: {
        operationType: _operationType,
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
  } catch (error) {
    console.log('error', error)
  }
}

// 判断有没有点击过投票
const fetchHasClickVoteNote = async () => {
  try {
    const res = await getHasClickVoteNote({
      params: {
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
    // 判断是否需要弹窗
    if (res.result?.length === 0) {
      showModal.value = true
      await nextTick()
      startTimeInterval()
    } else {
      showModal.value = false
    }
  } catch (error) {
    console.log('error', error)
  }
}

// 获取个人信息
const fetchUserInfo = async () => {
  const res = await userStore.checkUserInfo()
  console.log('fetchUserInfo', res.result?.isRealAuth)
  await nextTick()
  // 关闭下拉选项
  voteHouseSelectRef.value?.closeDropdown()

  if (res.result?.isRealAuth === 1) {
    voteHouseSelectRef.value?.fetchCommunityList()
  }
}

// 跳转到我的投票页面
const handleJumpMyVote = () => {
  uni.navigateTo({
    url: '/pages-sub/myVote/index',
  })
}

// 跳转到消息页面
const handleJumpMessage = () => {
  uni.switchTab({
    url: '/pages/message/index',
  })
}

onHide(() => {
  showModal.value = false
  clearTimeout()
})

onShow(async () => {
  console.log('onShow', userInfo.value.isRealAuth)

  id.value = ''
  voteData.value = null
  communityId.value = ''
  buildingListNew.value = []
  clearTimeout()
  // 如果已登录 且 未认证 则获取个人信息
  if (isLogined.value) {
    await fetchUserInfo()
  }
  await nextTick()
  // 解决下拉选项是true时，切换页面再回来 还是选中状态
})

onLoad(async () => {
  clearTimeout()
  // showModal.value = true
  // await nextTick()
  // startTimeInterval()
})
</script>

<style lang="scss" scoped>
.fui-ani__box {
  width: 640rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding-bottom: 24rpx;
}
.fui-flex__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}
.fui-hd__img {
  width: 100%;
  height: 80rpx;
  display: block;
  border-radius: 20rpx;
}
</style>
