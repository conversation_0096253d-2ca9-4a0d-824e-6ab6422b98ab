
.fui-button__wrap.data-v-879fd338 {
  position: relative;

  background: transparent !important;
  flex-direction: row;
}
.fui-button.data-v-879fd338 {
  border-width: 0;

  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  border-style: solid;
  position: relative;
  padding-left: 0;
  padding-right: 0;

  overflow: hidden;
  transform: translateZ(0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
}
.fui-button__thin-border.data-v-879fd338 {
  position: absolute;
  width: 200%;
  height: 200%;
  transform-origin: 0 0;
  transform: scale(0.5, 0.5) translateZ(0);
  box-sizing: border-box;
  left: 0;
  top: 0;
  border-radius: 32rpx;
  border-style: solid;
  pointer-events: none;
}
.fui-button__flex-1.data-v-879fd338 {
  flex: 1;

  width: 100%;
}
.fui-button.data-v-879fd338::after {
  border: 0;
}
.fui-button__active.data-v-879fd338 {
  overflow: hidden !important;
}
.fui-button__active.data-v-879fd338::after {
  content: ' ';
  background-color: var(--fui-bg-color-hover, rgba(0, 0, 0, 0.2));
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  transform: none;
  z-index: 2;
  border-radius: 0;
}
.fui-button__text.data-v-879fd338 {
  text-align: center;
  flex-direction: row;
  align-items: center;
  justify-content: center !important;
  padding-left: 0 !important;
}
.fui-button__opacity.data-v-879fd338 {
  opacity: 0.5;
}
.fui-text__bold.data-v-879fd338 {
  font-weight: bold;
}
.fui-button__link.data-v-879fd338 {
  border-color: transparent !important;
  background-color: transparent !important;
}
.fui-button__primary.data-v-879fd338 {
  border-color: var(--fui-color-primary, #465cff) !important;
  background: var(--fui-color-primary, #465cff) !important;
}
.fui-button__success.data-v-879fd338 {
  border-color: var(--fui-color-success, #09be4f) !important;
  background: var(--fui-color-success, #09be4f) !important;
}
.fui-button__warning.data-v-879fd338 {
  border-color: var(--fui-color-warning, #ffb703) !important;
  background: var(--fui-color-warning, #ffb703) !important;
}
.fui-button__danger.data-v-879fd338 {
  border-color: var(--fui-color-danger, #ff2b2b) !important;
  background: var(--fui-color-danger, #ff2b2b) !important;
}
.fui-button__purple.data-v-879fd338 {
  border-color: var(--fui-color-purple, #6831ff) !important;
  background: var(--fui-color-purple, #6831ff) !important;
}
.fui-button__gray.data-v-879fd338 {
  border-color: var(--fui-bg-color-content, #f8f8f8) !important;
  background: var(--fui-bg-color-content, #f8f8f8) !important;
  color: var(--fui-color-primary, #465cff) !important;
}
.fui-btn__gray-color.data-v-879fd338 {
  color: var(--fui-color-primary, #465cff) !important;
}


