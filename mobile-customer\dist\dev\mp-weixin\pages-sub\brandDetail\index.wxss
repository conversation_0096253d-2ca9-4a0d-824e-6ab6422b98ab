/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.rich-text.data-v-454321f3 {
  line-height: 1.6;
}
.bottom-zone.data-v-454321f3 {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.fui-custom__wrap.data-v-454321f3 {
  width: 100%;
  height: 720rpx;
}
.price-info.data-v-454321f3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 128rpx;
  background: linear-gradient(113deg, #ff5c23 53%, #ff9c1c 50%);
}
.content-wrapper.data-v-454321f3 {
  position: relative;
  overflow: hidden;
}
.tag-wrapper.data-v-454321f3 {
  float: left;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}
.tag.data-v-454321f3 {
  display: inline-block;
  padding: 0 12rpx;
  font-size: 24rpx;
  color: #ffffff;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background-color: #22b85e;
  border-radius: 8rpx;
}
.text-content.data-v-454321f3 {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
  text-align: justify;
}