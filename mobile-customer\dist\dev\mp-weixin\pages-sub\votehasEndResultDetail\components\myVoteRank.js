"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  _easycom_fui_empty2();
}
const _easycom_fui_empty = () => "../../../components/firstui/fui-empty/fui-empty.js";
if (!Math) {
  (TitleHeader + VoteNoticeItem + _easycom_fui_empty)();
}
const TitleHeader = () => "../../../components/common/titleHeader.js";
const VoteNoticeItem = () => "../../voteNoticeDetail/components/voteNoticeItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myVoteRank",
  props: {
    brands: {
      type: Array,
      default: () => []
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "您选择的品牌"
        }),
        b: __props.brands.length > 0
      }, __props.brands.length > 0 ? {
        c: common_vendor.f(__props.brands, (brand, index, i0) => {
          return {
            a: "4c0de25b-1-" + i0,
            b: common_vendor.p({
              brand,
              index,
              ["show-price"]: true
            }),
            c: index
          };
        })
      } : {
        d: common_vendor.p({
          src: "/static/images/img_data_3x.png",
          width: 386,
          height: 280,
          title: "暂无数据"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4c0de25b"]]);
wx.createComponent(Component);
