<view class="fui-upload__wrap data-v-2d5d0fa0"><view wx:for="{{a}}" wx:for-item="item" wx:key="o" class="fui-upload__item data-v-2d5d0fa0" style="{{'width:' + f + ';' + ('height:' + g) + ';' + ('border-radius:' + h)}}"><image class="fui-upload__img data-v-2d5d0fa0" style="{{'width:' + b + ';' + ('height:' + c) + ';' + ('border-radius:' + d)}}" src="{{item.a}}" mode="aspectFill" catchtap="{{item.b}}"></image><view wx:if="{{item.c}}" class="fui-upload__mask data-v-2d5d0fa0"><fui-icon wx:if="{{item.d}}" class="data-v-2d5d0fa0" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"></fui-icon><text wx:if="{{item.g}}" class="fui-reupload__btn data-v-2d5d0fa0" catchtap="{{item.h}}">重新上传</text><view wx:if="{{item.i}}" class="fui-upload__loading data-v-2d5d0fa0" ref="fui_reupload_ld"></view><text wx:if="{{item.j}}" class="fui-upload__text data-v-2d5d0fa0">请稍候...</text></view><view wx:if="{{e}}" class="fui-upload__del data-v-2d5d0fa0" style="{{'background:' + item.m}}" catchtap="{{item.n}}"><fui-icon wx:if="{{item.l}}" class="data-v-2d5d0fa0" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"></fui-icon></view></view><view wx:if="{{i}}" class="{{['fui-upload__item', 'data-v-2d5d0fa0', k]}}" style="{{'width:' + l + ';' + ('height:' + m) + ';' + ('background:' + n) + ';' + ('border-radius:' + o) + ';' + ('border-color:' + p) + ';' + ('border-style:' + q)}}" catchtap="{{r}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><fui-icon wx:if="{{j}}" class="data-v-2d5d0fa0" u-i="2d5d0fa0-2" bind:__l="__l" u-p="{{j}}"></fui-icon></block></view></view>