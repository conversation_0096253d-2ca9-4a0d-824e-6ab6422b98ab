<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '投票结果',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="mt20rpx">
      <view class="bg-[#ffffff] mt20rpx p-30rpx">
        <view class="flex items-center text-28rpx text-[#333]">
          <text>结束倒计时:</text>
          <view class="flex items-center ml20rpx">
            <fui-count-down
              isDays
              :isColon="false"
              size="42"
              width="50"
              height="50"
              borderColor="transparent"
              background="transparent"
              :value="voteEndSeconds"
            ></fui-count-down>
          </view>
        </view>
      </view>
      <TitleHeader title="你的投票结果" />

      <view class="bg-[#ffffff] px30rpx py30rpx text-32rpx text-[#333]" v-if="isMultiVote">
        您选择了从多投票，您的票将会投给获得票数最多的3个品牌，请耐心等待
      </view>
      <!-- 投票排名列表 -->
      <view class="brand-list mb190rpx" v-else>
        <view class="bg-[#ffffff] px30rpx" v-for="(brand, index) in brands" :key="index">
          <VoteNoticeItem :brand="brand" :index="index" :showPrice="true" />
        </view>
      </view>
    </view>
    <view class="w100% fixed bottom-0 left-0">
      <view class="bottom-zone px33rpx flex justify-between items-center h124rpx bg-[#ffffff]">
        <view>
          <template v-if="!isMultiVote && brands.length > 0">
            <view class="text-28rpx font-bold">预计您需要承担</view>
            <text class="text-28rpx font-bold text-[#FF2B2B]">
              ￥{{ minSkuPrice }} ~ ￥{{ maxSkuPrice }}
            </text>
          </template>
        </view>

        <fui-button
          type="primary"
          width="184rpx"
          height="84rpx"
          :size="32"
          radius="10rpx"
          text="修改投票"
          :disabled="brands.length == 0 && !isMultiVote"
          @click="handleModifyVote"
        ></fui-button>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import VoteNoticeItem from '@/pages-sub/voteNoticeDetail/components/voteNoticeItem.vue'
import {
  getPrePrice,
  getVoteInfoByCurUser,
  getVoteRankProductTop10,
  queryBizVoteInfoById,
} from '@/service/app'
import { calculatePrePrice, calculateVoteEndSeconds } from '@/utils/util'

const brands = ref([])

const loading = ref(false)

const handleModifyVote = () => {
  uni.navigateTo({
    url: `/pages-sub/vote/index?id=${id.value}&buildingId=${buildingId.value}`,
  })
}

const id = ref('')
const voteEndDate = ref('')
const communityId = ref('')
const buildingId = ref('')

// 计算秒数
const voteEndSeconds = ref(0)

// 是否是从多投票
const isMultiVote = ref(false)

const fetchVoteResult = async () => {
  try {
    loading.value = true
    const res: any = await getVoteInfoByCurUser({
      params: { voteId: id.value, buildingId: buildingId.value },
    })

    if (res.result === 1) {
      isMultiVote.value = true
    } else {
      brands.value =
        res.result?.map((item) => ({
          ...item,
          vote_rate: item.voteRate,
          vote_count: item.voteCount,
          brand_name: item.brandName,
          product_name: item.productName,
          pic_url: item.picUrl,
          price: calculatePrePrice(item.price, formulaParams.value),
        })) || []
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const minSkuPrice = computed(() => {
  if (brands.value.length <= 0) {
    return 0
  }
  return Math.min(...brands.value?.map((item) => item.price || 0))
})
const maxSkuPrice = computed(() => {
  if (brands.value.length <= 0) {
    return 0
  }
  return Math.max(...brands.value?.map((item) => item.price || 0))
})

// 获取预估价格
const formulaParams = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async () => {
  try {
    loading.value = true
    const res = await getPrePrice({
      params: {
        // communityId: voteData.value.communityId,
        buildingId: buildingId.value,
        voteId: id.value,
      },
    })
    formulaParams.value = res.result
    console.log('res', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取投票信息,主要为了获取小区id和buildingCodes
const voteData = ref({})
const fetchVoteInfo = async () => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id: id.value,
        buildingId: buildingId.value,
      },
    })
    voteData.value = res.result || {}
    voteEndDate.value = res.result.voteEndDate
    voteEndSeconds.value = calculateVoteEndSeconds(voteEndDate.value)
  } catch (error) {
    console.log('error', error)
    voteData.value = null
  } finally {
    loading.value = false
  }
}

onLoad(async (options: { id: string; buildingId: string }) => {
  id.value = options.id
  buildingId.value = options.buildingId

  // 获取投票信息
  await fetchVoteInfo()
  // 获取预估价格参数
  await fetchPrePrice()
  // 获取投票结果
  fetchVoteResult()
})
</script>

<style lang="scss" scoped>
.bottom-zone {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
