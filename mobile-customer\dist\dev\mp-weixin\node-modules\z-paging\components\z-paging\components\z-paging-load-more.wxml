<view class="{{['zp-l-container', 'data-v-0a5fd7d6', I && 'zp-l-container-rpx', J && 'zp-l-container-px']}}" style="{{K}}" bindtap="{{L}}"><block wx:if="{{a}}"><text wx:if="{{b}}" class="{{['data-v-0a5fd7d6', c && 'zp-l-line-rpx', d && 'zp-l-line-px']}}" style="{{e + ';' + f}}"/><image wx:if="{{g}}" src="{{h}}" style="{{i}}" class="{{['data-v-0a5fd7d6', 'zp-l-line-loading-custom-image', j && 'zp-l-line-loading-custom-image-animated', k && 'zp-l-line-loading-custom-image-rpx', l && 'zp-l-line-loading-custom-image-px']}}"/><image wx:if="{{m}}" class="{{['data-v-0a5fd7d6', 'zp-line-loading-image', n && 'zp-line-loading-image-rpx', o && 'zp-line-loading-image-px']}}" style="{{p}}" src="{{q}}"/><text wx:if="{{r}}" class="{{['zp-l-circle-loading-view', 'data-v-0a5fd7d6', s && 'zp-l-circle-loading-view-rpx', t && 'zp-l-circle-loading-view-px']}}" style="{{v + ';' + w}}"/><text wx:if="{{x}}" class="{{['data-v-0a5fd7d6', z && 'zp-l-text-rpx', A && 'zp-l-text-px']}}" style="{{B + ';' + C}}">{{y}}</text><text wx:if="{{D}}" class="{{['data-v-0a5fd7d6', E && 'zp-l-line-rpx', F && 'zp-l-line-px']}}" style="{{G + ';' + H}}"/></block></view>