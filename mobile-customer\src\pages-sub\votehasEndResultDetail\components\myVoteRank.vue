<template>
  <view class="mb20rpx">
    <title-header title="您选择的品牌" />
    <view class="px30rpx bg-[#ffffff]" v-if="brands.length > 0">
      <view v-for="(brand, index) in brands" :key="index">
        <VoteNoticeItem :brand="brand" :index="index" :show-price="true" />
      </view>
    </view>
    <view class="mt40px" v-else>
      <fui-empty
        src="/static/images/img_data_3x.png"
        :width="386"
        :height="280"
        title="暂无数据"
      ></fui-empty>
    </view>
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import VoteNoticeItem from '@/pages-sub/voteNoticeDetail/components/voteNoticeItem.vue'

const props = defineProps({
  brands: {
    type: Array,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
//
</style>
