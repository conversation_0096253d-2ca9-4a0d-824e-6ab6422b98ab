<template>
  <view class="brand-item">
    <template v-if="brand.brand_name">
      <view class="flex items-center mb10rpx">
        <view class="flex items-center w-[200rpx]">
          <!-- 品牌LOGO -->
          <view class="ml-10rpx">
            <image class="brand-logo" v-if="picUrl" :src="picUrl" mode="scaleToFill" />
          </view>
        </view>
        <view class="flex-1">
          <view class="flex items-center">
            <!-- 品牌标签 -->
            <view class="brand-tag orange-tag" :class="brand.tagType">{{ brand.brand_name }}</view>
            <!-- 品牌名称 -->
            <view class="brand-name">{{ brand.product_name }}</view>
          </view>
          <view class="flex items-center justify-between text-[24rpx] text-[#666] mb10rpx">
            <!-- <view class="">商品ID：{{ brand.productCode }}</view>
          <view class="">型号：{{ brand.productModel }}</view> -->
          </view>
          <view class="flex gap-20rpx items-center justify-between" v-if="brand.skuCode_dictText">
            <view class="check-item check-item-active">
              {{ brand.skuCode_dictText }}
            </view>
            <!-- <view class="text-[24rpx]" v-if="brand.price">
              总价：
              <text class="text-[##F59A23]">￥{{ brand.price }}</text>
            </view> -->
          </view>
        </view>
      </view>

      <view>
        <!-- 投票进度条 -->
        <progress
          :percent="voteRateNumber"
          :stroke-width="12"
          :border-radius="15"
          activeColor="#0073FF"
        />
        <!-- 投票信息 -->
        <view class="vote-info">
          <text>本小区得票数: {{ brand.vote_count }}</text>
          <text>得票率: {{ brand.vote_rate }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="text-30rpx font-bold text-[#333]">本次活动投票未能达到法定条件，未产生结果，我们将尽快启动新的投票活动，尽情期待</view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { getImageUrl } from '@/utils/util'
import { storeToRefs } from 'pinia'

const props = defineProps({
  brand: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
})
const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const picUrl = computed(() => {
  console.log('props?.brand?.pic_url', props?.brand)
  console.log('props', props?.brand)
  if (props?.brand?.pic_url) {
    return `${import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL}${props?.brand?.pic_url}`
  } else {
    return null
  }
})

const voteRateNumber = computed(() => {
  // 字符串转换成数字
  // 如果vote_rate为- 则返回0
  if (props?.brand?.vote_rate === '-' || !props?.brand?.vote_rate) {
    return 0
  }
  console.log('props?.brand?.vote_rate', props?.brand?.vote_rate)
  return parseFloat(props?.brand?.vote_rate)
})
</script>

<style lang="scss" scoped>
.brand-item {
  background-color: inherit;
  position: relative;
  padding: 30rpx 0rpx;
  display: flex;
  flex-direction: column;
}

.rank-number {
  width: 46rpx;
  height: 46rpx;
  background-color: #ccc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
}

.rank-1 {
  background-color: #e53935;
}

.rank-2 {
  background-color: #fb8c00;
}

.rank-3 {
  background-color: #43a047;
}

.brand-logo {
  width: 140rpx;
  height: 120rpx;
}

.brand-name {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  margin-left: 10rpx;
}

.brand-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.blue-tag {
  background-color: #e3f2fd;
  color: #1976d2;
}

.orange-tag {
  background-color: #fff3e0;
  color: #e65100;
}

.vote-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0 0 0;
}
.check-item {
  background: #eee;
  color: #000;
  // width: 180rpx;
  padding: 12rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
  &-active {
    background: #fde8cd;
    color: #f69f2a;
  }
}
</style>
