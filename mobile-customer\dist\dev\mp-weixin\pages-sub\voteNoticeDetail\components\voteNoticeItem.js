"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
require("../../../store/index.js");
const store_user = require("../../../store/user.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteNoticeItem",
  props: {
    brand: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      default: 0
    },
    isShowIndex: {
      type: Boolean,
      default: true
    },
    showBottomBorder: {
      type: Boolean,
      default: true
    },
    showPrice: {
      type: Boolean,
      default: false
    },
    showMoreSku: {
      type: Boolean,
      default: false
    },
    // 是否显示sku投票率(投票结果页面)
    showSkuRate: {
      type: Boolean,
      default: false
    }
  },
  setup(__props) {
    var _a;
    const props = __props;
    const itemShowMoreSku = common_vendor.ref(((_a = props.brand) == null ? void 0 : _a.showMoreSku) || false);
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const picUrl = common_vendor.computed(() => {
      var _a2;
      console.log("props?.brand?.pic_url", props == null ? void 0 : props.brand);
      return `${"http://10.0.9.216:9000/dfloorpublic"}/${(_a2 = props == null ? void 0 : props.brand) == null ? void 0 : _a2.pic_url}`;
    });
    const voteRateNumber = common_vendor.computed(() => {
      var _a2, _b, _c, _d;
      if (((_a2 = props == null ? void 0 : props.brand) == null ? void 0 : _a2.vote_rate) === "-" || !((_b = props == null ? void 0 : props.brand) == null ? void 0 : _b.vote_rate)) {
        return 0;
      }
      console.log("props?.brand?.vote_rate", (_c = props == null ? void 0 : props.brand) == null ? void 0 : _c.vote_rate);
      return parseFloat((_d = props == null ? void 0 : props.brand) == null ? void 0 : _d.vote_rate);
    });
    const handleShowMoreSku = () => {
      itemShowMoreSku.value = !itemShowMoreSku.value;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.isShowIndex
      }, __props.isShowIndex ? {
        b: common_vendor.t(__props.index + 1),
        c: common_vendor.n("rank-" + (__props.index + 1))
      } : {}, {
        d: common_vendor.unref(picUrl),
        e: common_vendor.t(__props.brand.brand_name),
        f: common_vendor.n(__props.brand.tagType),
        g: common_vendor.t(__props.brand.product_name),
        h: __props.brand.skuName
      }, __props.brand.skuName ? {
        i: common_vendor.t(__props.brand.skuName)
      } : {}, {
        j: __props.showSkuRate
      }, __props.showSkuRate ? {
        k: common_vendor.t(__props.brand.skuCode_dictText || "5年维保"),
        l: common_vendor.t(__props.brand.skuRate)
      } : {}, {
        m: common_vendor.unref(voteRateNumber),
        n: common_vendor.t(__props.brand.vote_count),
        o: common_vendor.t(__props.brand.vote_rate),
        p: __props.showPrice
      }, __props.showPrice ? {
        q: common_vendor.t(__props.brand.price)
      } : {}, {
        r: __props.showMoreSku
      }, __props.showMoreSku ? {
        s: common_vendor.f(__props.brand.sku_list, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.sku_name || item.sku_Name || "5年维保"),
            b: common_vendor.t(item.rate),
            c: item.id
          };
        }),
        t: common_vendor.unref(itemShowMoreSku),
        v: common_assets._imports_0$6,
        w: common_vendor.n(common_vendor.unref(itemShowMoreSku) ? "image-active" : "image-no-active"),
        x: common_vendor.o(handleShowMoreSku)
      } : {}, {
        y: __props.showBottomBorder ? "1px solid #eee" : "none"
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dbb28af1"]]);
wx.createComponent(Component);
