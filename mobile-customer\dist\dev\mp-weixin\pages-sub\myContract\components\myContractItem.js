"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  (_easycom_fui_tag2 + _easycom_fui_button2)();
}
const _easycom_fui_tag = () => "../../../components/firstui/fui-tag/fui-tag.js";
const _easycom_fui_button = () => "../../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  (_easycom_fui_tag + _easycom_fui_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myContractItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["viewSign"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const handleViewSign = () => {
      emit("viewSign", props.item);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(__props.item.voteName),
        b: __props.item.status === 1
      }, __props.item.status === 1 ? {
        c: common_vendor.p({
          text: "已签约",
          theme: "plain",
          type: "success",
          padding: ["8rpx", "16rpx"]
        })
      } : {
        d: common_vendor.p({
          text: "待签约",
          theme: "plain",
          type: "danger",
          padding: ["8rpx", "16rpx"]
        })
      }, {
        e: common_vendor.t(__props.item.communityName),
        f: common_vendor.t(__props.item.partyC),
        g: common_vendor.t(__props.item.supplierName),
        h: common_vendor.t(__props.item.signDeadline),
        i: common_vendor.o(handleViewSign),
        j: common_vendor.p({
          width: "120rpx",
          height: "56rpx",
          size: 24,
          background: "#00BFFF",
          text: "去查看"
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-22482036"]]);
wx.createComponent(Component);
