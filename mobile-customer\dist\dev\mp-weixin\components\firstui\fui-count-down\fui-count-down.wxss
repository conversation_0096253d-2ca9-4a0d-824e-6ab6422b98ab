
.fui-count__down-wrap.data-v-b4032350 {

		display: inline-flex;

		flex-direction: row;
		align-items: center;
		justify-content: center;
		text-align: center;
}
.fui-count__down-item.data-v-b4032350 {

		display: flex;
		white-space: nowrap;

		align-items: center;
		justify-content: center;
		border-radius: 6rpx;
		border-width: 1rpx;
		border-style: solid;
		text-align: center;
}
.fui-count__down-colon.data-v-b4032350 {

		display: flex;

		justify-content: center;
		align-items: center;
		text-align: center;
}
.fui-count__down-ms.data-v-b4032350 {
		overflow: hidden;
		border-radius: 6rpx;
		border-width: 1rpx;
		border-style: solid;
}
.fui-count__down-ms_list.data-v-b4032350 {
		animation: loop-b4032350 1s steps(10) infinite;
}
@keyframes loop-b4032350 {
from {
			transform: translateY(0);
}
to {
			transform: translateY(-100%);
}
}
.fui-count__down-ms_item.data-v-b4032350 {

		display: flex;

		align-items: center;
		justify-content: center;
		text-align: center;
}
