<wxs src="./index.wxs" module="handler"/>
<view class="data-v-2c25f223" catchtouchmove="{{P}}"><view wx:if="{{a}}" class="{{['fui-fab__mask', 'data-v-2c25f223', b && 'fui-fab__mask-show']}}" style="{{c}}" ref="fui_mask_ani" catchtap="{{d}}"></view><view data-disabled="{{G}}" data-width="{{H}}" data-height="{{I}}" data-left="{{J}}" data-top="{{K}}" data-app="{{L}}" prop="{{M}}" change:prop="{{handler.fabreset}}" bindtouchstart="{{handler.touchstart}}" bindtouchmove="{{handler.touchmove}}" bindmousedown="{{handler.mousedown}}" class="{{['fui-fab__btn-wrap', 'data-v-2c25f223', N]}}" style="{{O}}"><view class="{{['fui-fab__btn-list', 'data-v-2c25f223', i && 'fui-fab__btn-hidden', j && 'fui-fab__list-ani', k && 'fui-fab__list-left', l && 'fui-fab__list-right']}}" ref="fui_fab_ani"><view wx:for="{{e}}" wx:for-item="btn" wx:key="H" class="{{['fui-fab__button-box', 'data-v-2c25f223', h]}}" catchtap="{{btn.I}}"><text wx:if="{{btn.a}}" class="fui-fab__btn-text data-v-2c25f223" style="{{'font-size:' + btn.c + ';' + ('color:' + btn.d) + ';' + ('text-align:' + btn.e)}}">{{btn.b}}</text><view class="{{['fui-fab__button', 'data-v-2c25f223', btn.s && 'fui-fab__btn-color']}}" style="{{'width:' + f + ';' + ('height:' + g) + ';' + ('background:' + btn.t)}}"><fui-icon wx:if="{{btn.f}}" class="data-v-2c25f223" u-i="{{btn.g}}" bind:__l="__l" u-p="{{btn.h}}"></fui-icon><image wx:if="{{btn.i}}" class="data-v-2c25f223" src="{{btn.j}}" style="{{'width:' + btn.k + ';' + ('height:' + btn.l) + ';' + ('border-radius:' + btn.m)}}" mode="widthFix"></image><text wx:if="{{btn.n}}" class="fui-fab__btn-abbr data-v-2c25f223" style="{{'font-size:' + btn.p + ';' + ('line-height:' + btn.q) + ';' + ('color:' + btn.r)}}">{{btn.o}}</text></view><button wx:if="{{btn.v}}" class="fui-fab__opentype-btn data-v-2c25f223" open-type="{{btn.w}}" app-parameter="{{btn.x}}" lang="{{btn.y}}" sessionFrom="{{btn.z}}" sendMessageTitle="{{btn.A}}" sendMessagePath="{{btn.B}}" sendMessageImg="{{btn.C}}" showMessageCard="{{btn.D}}" bindcontact="{{btn.E}}" bindopensetting="{{btn.F}}" bindlaunchapp="{{btn.G}}"></button></view></view><view class="{{['fui-fab__btn-main', 'data-v-2c25f223', B && 'fui-fab__btn-color']}}" style="{{'width:' + C + ';' + ('height:' + D) + ';' + ('background:' + E)}}" catchtap="{{F}}"><view class="{{['fui-fab__btn-inner', 'data-v-2c25f223', n && 'fui-fab__btn-ani']}}" ref="fui_fm_ani"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><fui-icon wx:if="{{m}}" class="data-v-2c25f223" u-i="2c25f223-1" bind:__l="__l" u-p="{{m}}"></fui-icon></block></view><button wx:if="{{o}}" class="fui-fab__opentype-btn data-v-2c25f223" open-type="{{p}}" app-parameter="{{q}}" lang="{{r}}" sessionFrom="{{s}}" sendMessageTitle="{{t}}" sendMessagePath="{{v}}" sendMessageImg="{{w}}" showMessageCard="{{x}}" bindcontact="{{y}}" bindopensetting="{{z}}" bindlaunchapp="{{A}}"></button></view></view></view>