<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>
<template>
  <view class="height-tabbar relative overflow-y-auto">
    <view class="header relative">
      <view class="w100% h500rpx relative">
        <image
          class="w100% h100% absolute"
          style=""
          src="/static/images/gy_app_001.jpg"
          mode="aspectFill"
        />
      </view>
      <userInfoItem
        class="absolute bottom-200rpx left-40rpx z-10"
        :userInfo="userInfo"
        :isLogined="isLogined"
      />
    </view>
    <view class="relative top-[-130rpx] z-10">
      <view class="w100% absolute">
        <view class="user-content">
          <view class="">
            <fui-list-cell arrow :padding="listPadding" @click="handleMyHouse">
              <view class="fui-align__center">
                <image
                  class="fui-list__icon"
                  src="/static/images/myHouse.png"
                  mode="widthFix"
                ></image>
                <text>我的房产</text>
              </view>
            </fui-list-cell>
            <fui-list-cell arrow :padding="listPadding" @click="handleMyVote">
              <view class="fui-align__center">
                <image class="fui-list__icon" src="/static/images/vote.png" mode="widthFix"></image>
                <text>我的投票</text>
              </view>
            </fui-list-cell>
            <fui-list-cell arrow :padding="listPadding" @click="handleMyAuth">
              <view class="fui-align__center">
                <image
                  class="fui-list__icon"
                  src="/static/images/myContract.png"
                  mode="widthFix"
                ></image>
                <text>我的授权</text>
              </view>
            </fui-list-cell>
            <view v-if="true">
              <fui-list-cell arrow :padding="listPadding" @click="handleInstallRecord">
                <view class="fui-align__center">
                  <image
                    class="fui-list__icon"
                    src="/static/images/myInstall.png"
                    mode="widthFix"
                  ></image>
                  <text>安装记录</text>
                </view>
              </fui-list-cell>
              <fui-list-cell arrow :padding="listPadding" @click="handleOther">
                <view class="fui-align__center">
                  <image
                    class="fui-list__icon"
                    src="/static/images/feedback.png"
                    mode="widthFix"
                  ></image>
                  <text>意见反馈</text>
                </view>
              </fui-list-cell>
              <fui-list-cell arrow :padding="listPadding" @click="handleMyContract">
                <view class="fui-align__center">
                  <image
                    class="fui-list__icon"
                    src="/static/images/myContract.png"
                    mode="widthFix"
                  ></image>
                  <text>合同签订</text>
                </view>
              </fui-list-cell>
              <fui-list-cell arrow :padding="listPadding" @click="handleMyPay">
                <view class="fui-align__center">
                  <image
                    class="fui-list__icon"
                    src="/static/images/myPay.png"
                    mode="widthFix"
                  ></image>
                  <text>我的支付</text>
                </view>
              </fui-list-cell>
              <fui-list-cell arrow :padding="listPadding" v-if="false" @click="handleOther">
                <view class="fui-align__center">
                  <image
                    class="fui-list__icon"
                    src="/static/images/common/icon_tabbar.png"
                    mode="widthFix"
                  ></image>
                  <text>关爱版</text>
                </view>
              </fui-list-cell>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="absolute bottom-26rpx left-0 right-0">
      <view class="w100% flex justify-center" v-if="isLogined">
        <fui-button btnSize="medium" @click="handleLogout" text="退出登录" />
      </view>
      <view class="flex justify-center items-center flex-wrap mt20rpx">
        <view class="text-24rpx color-[#ff9000]" @click="handleShowAgreement">
          《用户服务协议》
        </view>
        <view class="text-24rpx color-[#ff9000]" @click="handleShowPrivacyPolicy">
          《隐私权政策》
        </view>
        <view class="text-24rpx color-[#ff9000]" @click="handleShowVoteRules">《投票规则》</view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { logout } from '@/service/app'
import userInfoItem from './components/userInfo.vue'

import { useUserStore } from '@/store'
import { checkIsLogin, showTextToast } from '@/utils/util'
import { storeToRefs } from 'pinia'

const previewUrl = import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL
const userStore = useUserStore()

const bgAnimation = `${previewUrl}/material/gy_app_001_animation.png`
const bgCommon = `${previewUrl}/material/gy_app_001.jpg`

const { userInfo, isLogined } = storeToRefs(userStore)

const listPadding = ['20rpx', '12rpx']

// 我的房产
const handleMyHouse = () => {
  if (!isLogined.value) {
    showTextToast('请先登录')
    return
  }
  uni.navigateTo({
    url: '/pages-sub/myHouse/index',
  })
}
// 我的投票
const handleMyVote = () => {
  if (!isLogined.value) {
    showTextToast('请先登录')
    return
  }
  uni.navigateTo({
    url: '/pages-sub/myVote/index',
  })
}

// 我的授权
const handleMyAuth = () => {
  if (!isLogined.value) {
    showTextToast('请先登录')
    return
  }
  if (userInfo.value?.isRealAuth !== 1) {
    showTextToast('请先实名认证')
    return
  }
  uni.navigateTo({
    url: '/pages-sub/myAuth/index',
  })
}

const handleOther = () => {
  showTextToast('暂未开放，敬请期待')
}

// 安装记录
const handleInstallRecord = () => {
  showTextToast('暂未开放，敬请期待')
  return
  uni.navigateTo({
    url: '/pages-sub/installRecord/index',
  })
}

// 合同签订
const handleMyContract = () => {
  if (!isLogined.value) {
    showTextToast('请先登录')
    return
  }
  uni.navigateTo({
    url: '/pages-sub/myContract/index',
  })
}

// 我的支付
const handleMyPay = () => {
  showTextToast('暂未开放，敬请期待')
  return
  uni.navigateTo({
    url: '/pages-sub/myPay/index',
  })
}

// 获取用户信息
const getUserInfo = async () => {
  userStore.checkUserInfo()
}

const handleShowAgreement = () => {
  uni.navigateTo({ url: '/pages-sub/userAgreement/index' })
}
const handleShowPrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages-sub/privacyPolicy/index' })
}
const handleShowVoteRules = () => {
  uni.navigateTo({ url: '/pages-sub/voteRules/index' })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        console.log('res', res)
        // userStore.clearUserInfo()
        try {
          const res = await logout({})
          console.log('res', res)
          userStore.clearUserInfo()
        } catch (error) {
          console.log('error', error)
        }
      }
    },
  })
}

onShow(() => {
  console.log('onShow')
  getUserInfo()
})
</script>

<style scoped lang="scss">
.user-content {
  // position: relative;
  // top: -130rpx;
  padding: 12rpx 24rpx;
  margin: 0rpx 20rpx 0rpx 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
}
.fui-list__item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fui-align__center {
  display: flex;
  align-items: center;
}
.fui-text__explain {
  font-size: 28rpx;
  color: #7f7f7f;
  flex-shrink: 0;
}
.fui-list__icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}
</style>
