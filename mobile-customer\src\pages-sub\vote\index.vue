<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '投票',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] vote-notice overflow-y-auto">
    <view class="bg-[#ffffff] mt20rpx p-30rpx">
      <view class="flex items-center text-28rpx text-[#333]">
        <text>结束倒计时:</text>
        <view class="flex items-center ml20rpx">
          <fui-count-down
            isDays
            :isColon="false"
            size="42"
            width="50"
            height="50"
            borderColor="transparent"
            background="transparent"
            color="#005ED1"
            colonColor="#005ED1"
            :value="voteEndSeconds"
          ></fui-count-down>
        </view>
      </view>
    </view>
    <view>
      <TitleHeader title="请选择至少3个品牌进行投票" />
    </view>
    <view
      class="bg-[#ffffff] py20rpx my10rpx color-[#ff9000] text-[30rpx] flex items-end justify-center"
    >
      本小区选择从多投票的票数：
      <text class="color-[#000] text-[36rpx] mr10rpx">
        {{ multCnt }}
      </text>
      票
    </view>
    <view class="brand-list mb20rpx">
      <view class="relative" v-for="(brand, index) in brands" :key="index">
        <VoteBrandCheckItem
          :brand="brand"
          :index="index"
          :selectedCount="selectedCount"
          :isListenSort="isListenSort"
          :buildingIds="buildingId"
          :voteId="id"
          @changeVoteSort="changeVoteSort"
        />
        <view
          class="absolute left-0 top-0 w66rpx h100% z-[50]"
          @click="handleVoteBrandClick(brand)"
        ></view>
      </view>
    </view>
    <view class="w100%">
      <view class="px40rpx">
        <VoteNotice />
      </view>
    </view>
    <view class="w100% fixed bottom-0 left-0 z-[100]">
      <view class="bottom-zone px33rpx flex justify-between items-center h124rpx bg-[#ffffff]">
        <view>
          <view class="text-24rpx font-bold">预计您需要承担</view>
          <text class="text-24rpx font-bold text-[#FF2B2B]">
            ￥{{ minSkuPrice }} ~ ￥{{ maxSkuPrice }}
          </text>
        </view>
        <view class="flex items-center gap-x-10rpx">
          <fui-button
            background="linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)"
            width="184rpx"
            height="84rpx"
            :size="32"
            radius="10rpx"
            text="从多投票"
            @click="handleMultiVote"
          ></fui-button>
          <fui-button
            type="primary"
            width="184rpx"
            height="84rpx"
            :size="32"
            radius="10rpx"
            text="立即投票"
            @click="handleImmediateVote"
          ></fui-button>
        </view>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
    <!-- 友情提醒 -->
    <fui-landscape :show="showModal" :closable="false" @close="closeModal">
      <view class="fui-ani__box">
        <image class="fui-hd__img" src="/static/images/dt_008.jpg" mode="widthFix"></image>
        <view class="fui-flex__center">
          <view class="w100%">
            <view class="my20rpx">
              <view class="text-40rpx font-bold center mb10rpx">友情提醒</view>
              <view
                class="text-24rpx text-[#999] leading-44rpx tip-content"
                v-if="voteMethod === 0"
              >
                您选择的
                <text class="text-[#FF2B2B]">{{ voteTipInfo.brandNames }}</text>
                品牌的电梯，本次电梯更新已经超过库存数量，
                如您还是选择该品牌的电梯，则安装时间可能较长，预计180天后安装完毕。
              </view>
              <view class="text-24rpx text-[#999] leading-44rpx tip-content" v-else>
                您选择了从多投票，您的票将会投给最终获得票数最多的3个品牌。
              </view>
            </view>
            <fui-button
              class="text-center"
              radius="100rpx"
              width="100%"
              disabled-background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              borderColor="rgba(0,0,0,0)"
              border-width="0"
              @click="handleConfirmVote"
            >
              {{ voteMethod === 0 ? '继续投票' : '继续从多' }}
            </fui-button>
            <fui-button
              class="text-center mt20rpx"
              radius="100rpx"
              background="#fff"
              color="#000000"
              borderColor="#ffffff"
              @click="closeModal"
              text="重新选择"
            ></fui-button>
          </view>
        </view>
      </view>
    </fui-landscape>
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import VoteNotice from '@/pages/vote/components/voteNotice.vue'
import VoteBrandCheckItem from '@/pages-sub/vote/components/voteBrandCheckItem.vue'
import { calculatePrePrice, calculateVoteEndSeconds, showTextToast } from '@/utils/util'
import {
  getPrePrice,
  queryBizVoteInfoById,
  queryExceedBrandList,
  queryMultCnt,
  queryVoteElevatorList,
  voteByUser,
} from '@/service/app'
import { EventName } from '@/utils/const'
import { StockStatusEnum } from './vota.data'

// 投票电梯列表
const brands = ref([])

// 是否监听排序 0 不监听 1 监听
const isListenSort = ref(0)

// 友情提醒
const showModal = ref(false)

const selectedCount = computed(() => {
  return brands.value.filter((item) => item.isChecked).length || 1
})

// 点击品牌checkbox
const handleVoteBrandClick = async (brand: any) => {
  if (brand.stockStatus === StockStatusEnum.SELL_OUT) {
    // showTextToast('该品牌已售罄')
    return
  }
  isListenSort.value = 0
  brand.isChecked = !brand.isChecked
  const index = brands.value.findIndex((item) => item.name === brand.name)
  const checkedBrands = brands.value.filter((item) => item.isChecked)

  await nextTick()
  console.log('brands', brands.value)

  if (brand.isChecked) {
    brand.sortOrder = selectedCount.value
  } else {
    // 取消选中，找出所有大于当前排序的，排序减1
    const oldSortOrder = brand.sortOrder
    brand.sortOrder = 0
    // 重新排序：将大于当前排序的项目都减1
    brands.value.forEach((item) => {
      if (item.isChecked && item.sortOrder > oldSortOrder) {
        item.sortOrder--
      }
    })
  }
  await nextTick()
  isListenSort.value = 1
  await nextTick()
  // fetchPrePrice()
}

const allChecked = computed(() => {
  return brands.value.filter((item) => item.isChecked)
})

// 获取所有选中品牌的sku
const allCheckedSkus = computed(() => {
  console.log('allChecked', allChecked.value)

  return allChecked.value.map((item) => {
    return item?.skus?.find((sku) => sku.checked) || {}
  })
})

// 找出最大的sku价格
const maxSkuPrice = computed(() => {
  if (allCheckedSkus.value.length <= 0) {
    return 0
  }
  return Math.max(...allCheckedSkus.value.map((item) => item.price || 0))
})
// 找出最小的sku价格
const minSkuPrice = computed(() => {
  if (allCheckedSkus.value.length <= 0) {
    return 0
  }
  return Math.min(...allCheckedSkus.value.map((item) => item.price || 0))
})

// 投票方式0：正常投票 1：从多投票
const voteMethod = ref(0)

// 关闭友情提醒
const closeModal = () => {
  showModal.value = false
}

// 继续投票
const handleConfirmVote = () => {
  showModal.value = false

  confirmVote()
}

const confirmVote = async () => {
  // 投票参数
  const _voteParams = {
    voteId: id.value,
    buildingId: buildingId.value,
    method: voteMethod.value,
    // 投票品牌
    // products: allChecked.value.map((item) => {
    //   // 找出所有选中品牌的sku
    //   const checkedSkus = item.skus.find((sku) => sku.checked)
    //   console.log('checkedSkus', checkedSkus)
    //   console.log('item', item)

    //   return {
    //     productId: item.productId,
    //     productSort: item.sortOrder,
    //     skuId: checkedSkus.id,
    //     skuName: checkedSkus?.name,
    //   }
    // }),
    supplierList: allChecked.value.map((item) => {
      // 找出所有选中品牌的sku
      const checkedSkus = item?.skus?.find((sku) => sku.checked)
      console.log('checkedSkus', checkedSkus)
      console.log('item', item)

      return {
        supplierId: item.supplierId,
        sort: item.sortOrder,
        skuCode: checkedSkus?.code,
        productId: item.productId,
        productName: item.productName,
      }
    }),
  }

  console.log('_voteParams', _voteParams)
  console.log('allChecked.value', allChecked.value)
  try {
    loading.value = true
    const res = await voteByUser({
      body: _voteParams,
    })
    console.log('res', res)
    showTextToast(res.message || '投票成功')
    // 使用定时器 防止页面跳转过快，不显示toast
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/vote/index',
      })
    }, 1000)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 从多投票
const handleMultiVote = () => {
  console.log('handleMultiVote')
  voteMethod.value = 1
  showModal.value = true
}

// 立即投票

// 投票提示信息
const voteTipInfo: any = ref({})
const handleImmediateVote = async () => {
  voteMethod.value = 0
  if (allChecked.value.length < 3) {
    uni.showToast({
      title: '请至少选择3个品牌',
      icon: 'none',
    })
    return
  }
  console.log('res', allChecked.value)
  // 查询是否提示
  // 在选中的里面找出需要友情提示的 stockStatus 等于INSUFFICIENT
  const insufficientBrands =
    allChecked.value.filter((item) => item.stockStatus === StockStatusEnum.INSUFFICIENT) || []
  voteTipInfo.value.brandNames = insufficientBrands.map((item) => item.brandName).join(',')

  // try {
  //   loading.value = true
  //   const res = await queryExceedBrandList({
  //     params: {
  //       brandNames: allChecked.value.map((item) => item.brandName).join(','),
  //     },
  //   })
  //   voteTipInfo.value = res.result
  // } catch (error) {
  //   console.log('error', error)
  // } finally {
  //   loading.value = false
  // }

  console.log('voteTipInfo', voteTipInfo.value)
  if (voteTipInfo.value?.brandNames) {
    showModal.value = true
  } else {
    confirmVote()
  }
}

const id = ref('')
const voteEndDate = ref('')
const buildingId = ref('')
const loading = ref(false)
const voteEndSeconds = ref(0)

// 获取投票电梯列表
const fetchVoteElevatorList = async () => {
  try {
    loading.value = true

    const res: any = await queryVoteElevatorList({
      params: {
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
    brands.value =
      res.result?.map((item) => {
        // 处理商品数据
        // let _brandContentJson = ''
        // if (item.content) {
        //   // 处理商品数据，将content字符串转换为json对象
        //   _brandContentJson = JSON.parse(item.content)
        //   // 处理sku数据，并设置默认选中第一个sku,计算价格，通过name排序
        //   _brandContentJson.skus = _brandContentJson.skus
        //     .sort((a, b) => b.name.localeCompare(a.name))
        //     .map((sku, index) => {
        //       return {
        //         ...sku,
        //         checked: index === 0,
        //         price: calculatePrePrice(sku.price, formulaParams.value),
        //       }
        //     })
        // }

        return {
          ...item,
          voteCount: item.voteCount ?? 0,
          voteRate: item.voteRate ?? '0%',
          communityId: voteData.value.communityId,
          sortOrder: Number(item.sort),
          isChecked: false,
          skus:
            item?.bizSkus?.map((sku, index) => {
              return {
                ...sku,

                checked: index === 0,
                skuPrice: sku.price,
                price: calculatePrePrice(sku.price, formulaParams.value),
              }
            }) || [],
        }
      }) || []
    console.log('queryVoteElevatorList', res)
    console.log('brands.value', brands.value)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 改变投票排序
const changeVoteSort = (brand: any, currentSortOrder: number) => {
  console.log('changeVoteSort2222', brand, currentSortOrder)
  const targetItem = brands.value.find((item) => item.id === brand.id)
  if (!targetItem) return

  const oldSort = targetItem.sortOrder

  // 如果新旧排序值相同，不需要处理
  if (oldSort === currentSortOrder) return

  // 根据移动方向调整其他项目的sort值
  if (oldSort < currentSortOrder) {
    // 向后移动：原位置后面且新位置前面（含）的项目sort-1
    brands.value.forEach((item) => {
      if (
        item.id !== targetItem.id &&
        item.sortOrder > oldSort &&
        item.sortOrder <= currentSortOrder
      ) {
        console.log('item.sortOrder--', item.sortOrder)
        item.sortOrder--
      }
    })
  } else {
    // 向前移动：新位置后面（含）且原位置前面的项目sort+1
    brands.value.forEach((item) => {
      if (
        item.id !== targetItem.id &&
        item.sortOrder >= currentSortOrder &&
        item.sortOrder < oldSort
      ) {
        item.sortOrder++
      }
    })
  }

  // 最后更新目标项目的sort值
  targetItem.sortOrder = currentSortOrder
}

// 获取预估价格,用于计算
const formulaParams: any = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async () => {
  try {
    loading.value = true
    const res = await getPrePrice({
      params: {
        // communityId: voteData.value.communityId,
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
    formulaParams.value = res.result
    console.log('res', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const voteData: any = ref({})
// 获取投票信息,主要为了获取小区id和buildingCodes
const fetchVoteInfo = async () => {
  try {
    loading.value = true
    const res: any = await queryBizVoteInfoById({
      params: {
        id: id.value,
        buildingId: buildingId.value,
      },
    })
    voteData.value = res.result || {}
    voteEndDate.value = res.result.voteEndDate
    voteEndSeconds.value = calculateVoteEndSeconds(voteEndDate.value)
  } catch (error) {
    console.log('error', error)
    voteData.value = null
  } finally {
    loading.value = false
  }
}

// 获取从多投票数量
const multCnt = ref(0)
const fetchMultCnt = async () => {
  try {
    const res: any = await queryMultCnt({
      params: {
        voteId: id.value,
      },
    })
    multCnt.value = res.result
  } catch (error) {
    console.log('error', error)
  }
}

onLoad(async (options: { id: string; buildingId: string }) => {
  console.log('options', options)
  id.value = options.id
  buildingId.value = options.buildingId
  // 获取投票信息
  await fetchVoteInfo()
  // 获取预估价格参数
  await fetchPrePrice()
  // 获取投票电梯列表
  fetchVoteElevatorList()

  // 获取从多投票数量
  fetchMultCnt()
  // 监听 商品详情页面 投票品牌选中事件
  uni.$on(EventName.VOTE_BRAND_SELECTED, (e: any) => {
    console.log('VOTE_BRAND_SELECTED', e)
    const { skuId, productId, code, skuName } = e
    // 找出productId 对应的 商品
    const brand = brands.value.find((item) => item.productId === productId)
    console.log('brand-------', brand)

    if (brand) {
      // 判断没有选中的情况下设为选中
      if (!brand.isChecked) {
        handleVoteBrandClick(brand)
      }
      // 找出skuId 对应的 sku 并设为选中, 其它设置false
      brand.skus.forEach((sku) => {
        sku.checked = sku.code === code || sku.name === skuName
      })
    }
  })
})
</script>

<style lang="scss" scoped>
.fui-ani__box {
  width: 640rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding-bottom: 24rpx;
}
.fui-flex__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}
.fui-hd__img {
  width: 100%;
  height: 80rpx;
  display: block;
  border-radius: 20rpx;
}

.vote-notice {
  height: calc(100vh - 150rpx - constant(safe-area-inset-bottom));
  height: calc(100vh - 150rpx - env(safe-area-inset-bottom));
}
.bottom-zone {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

::v-deep .fui-count__down-wrap {
  align-items: baseline !important;
}
.tip-content {
  text-indent: 2em;
}
</style>
