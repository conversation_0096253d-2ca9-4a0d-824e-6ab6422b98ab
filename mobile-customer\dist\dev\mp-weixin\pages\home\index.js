"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_nav_bar2 = common_vendor.resolveComponent("fui-nav-bar");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_section2 = common_vendor.resolveComponent("fui-section");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_nav_bar2 + _easycom_fui_button2 + _easycom_fui_section2 + _easycom_fui_empty2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_nav_bar = () => "../../components/firstui/fui-nav-bar/fui-nav-bar.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_section = () => "../../components/firstui/fui-section/fui-section.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_nav_bar + _easycom_fui_button + _easycom_fui_section + VoteNotice + _easycom_fui_empty + VoteResult + _easycom_fui_loading)();
}
const VoteNotice = () => "../../components/common/voteNotice.js";
const VoteResult = () => "../../components/common/voteResult.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const voteNoticeList = common_vendor.ref([
      // {
      //   id: 1,
      //   content: '江宁区秣陵街道安居苑1栋、5栋、8栋电梯更新开始投票啦！',
      //   date: '2021-01-01',
      // },
      // {
      //   id: 2,
      //   content: '江宁区秣陵街道安居苑1栋、5栋、8栋电梯更新开始投票啦！',
      //   date: '2021-01-02',
      // },
    ]);
    const loading = common_vendor.ref(false);
    const voteResultList = common_vendor.ref([]);
    const fetchVoteNoticeList = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoList({
          params: {
            pageNo: 1,
            pageSize: 4,
            valid: true,
            order: "desc",
            column: "createTime"
          }
        });
        voteNoticeList.value = res.result.records;
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchVoteResultList = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoList({
          params: {
            pageNo: 1,
            pageSize: 4,
            minStatus: 2,
            order: "desc",
            column: "createTime"
          }
        });
        voteResultList.value = res.result.records;
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const handleVoteMore = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/voteNoticeList/index"
      });
    };
    const handleVoteResultMore = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/voteResultList/index"
      });
    };
    const handleWelcome = () => {
      common_vendor.index.navigateTo({
        url: "/pages/welcome/index"
      });
    };
    common_vendor.onShow(() => {
      fetchVoteNoticeList();
      fetchVoteResultList();
    });
    common_vendor.onLoad(() => {
    });
    common_vendor.onUnload(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_vendor.o(handleWelcome),
        c: common_vendor.p({
          title: "首页",
          splitLine: true,
          size: 15,
          isFixed: true,
          isOccupy: true
        }),
        d: common_assets._imports_0$2,
        e: common_vendor.o(handleVoteMore),
        f: common_vendor.p({
          type: "link",
          height: "40rpx",
          color: "#465CFF",
          size: 26
        }),
        g: common_vendor.unref(voteNoticeList).length > 0,
        h: common_vendor.p({
          title: "投票公告",
          isLine: true,
          lineWidth: "4px"
        }),
        i: common_vendor.unref(voteNoticeList).length > 0
      }, common_vendor.unref(voteNoticeList).length > 0 ? {
        j: common_vendor.f(common_vendor.unref(voteNoticeList), (item, k0, i0) => {
          return {
            a: "0d6cf36e-4-" + i0 + ",0d6cf36e-0",
            b: common_vendor.p({
              item
            }),
            c: item.id
          };
        })
      } : {
        k: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          title: "暂无数据"
        })
      }, {
        l: common_vendor.o(handleVoteResultMore),
        m: common_vendor.p({
          type: "link",
          height: "40rpx",
          color: "#465CFF",
          size: 26
        }),
        n: common_vendor.unref(voteResultList).length > 0,
        o: common_vendor.p({
          title: "投票结果公示",
          isLine: true,
          lineWidth: "4px"
        }),
        p: common_vendor.unref(voteResultList).length > 0
      }, common_vendor.unref(voteResultList).length > 0 ? {
        q: common_vendor.f(common_vendor.unref(voteResultList), (item, k0, i0) => {
          return {
            a: "0d6cf36e-8-" + i0 + ",0d6cf36e-0",
            b: common_vendor.p({
              item
            }),
            c: item.id
          };
        })
      } : {
        r: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          title: "暂无数据"
        })
      }, {
        s: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        t: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
wx.createPage(_sfc_main);
