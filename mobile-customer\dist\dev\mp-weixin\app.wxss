/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.test {
  margin-top:32rpx;margin-left:32rpx;
  padding-top: 4px;
  color: red;
}
 page,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}.container{width:100%;}.center{display:flex;align-items:center;justify-content:center;}@media (min-width: 640px){.container{max-width:640px;}}@media (min-width: 768px){.container{max-width:768px;}}@media (min-width: 1024px){.container{max-width:1024px;}}@media (min-width: 1280px){.container{max-width:1280px;}}@media (min-width: 1536px){.container{max-width:1536px;}}._a_visible{visibility:visible !important;}.top-_a_-110rpx_a_{top:-110rpx;}.z-_a_100_a_{z-index:100;}.z-_a_50_a_{z-index:50;}.mb-_a_200rpx_a_{margin-bottom:200rpx;}.h-_a_120rpx_a_{height:120rpx;}.h-_a_300rpx_a_{height:300rpx;}.h-_a_420rpx_a_{height:420rpx;}.h-_a_48rpx_a_{height:48rpx;}.h-100_a_,.h100_a_{height:100%;}.h90_a_{height:90%;}.max-w-90_a_{max-width:90%;}.w-_a_120rpx_a_{width:120rpx;}.w-_a_200rpx_a_{width:200rpx;}.w-_a_260rpx_a_{width:260rpx;}.w-_a_400rpx_a_{width:400rpx;}.w-_a_48rpx_a_{width:48rpx;}.w-100_a_,.w100_a_{width:100%;}.w-50_a_,.w50_a_{width:50%;}.w-58_a_{width:58%;}.gap-_a_20rpx_a_{gap:20rpx;}.border-_a__a_DB393D_a_{--un-border-opacity:1;border-color:rgb(219 57 61 / var(--un-border-opacity));}.border-_a__a_FACD91_a_{--un-border-opacity:1;border-color:rgb(250 205 145 / var(--un-border-opacity));}.rounded-_a_12rpx_a_{border-radius:12rpx;}.rounded-_a_50_a__a_{border-radius:50%;}.rounded-_a_8rpx_a_{border-radius:8rpx;}.bg-_a__a_E6EEFF_a_{--un-bg-opacity:1;background-color:rgb(230 238 255 / var(--un-bg-opacity));}.bg-_a__a_eee_a_{--un-bg-opacity:1;background-color:rgb(238 238 238 / var(--un-bg-opacity));}.bg-_a__a_f5f5f5_a_{--un-bg-opacity:1;background-color:rgb(245 245 245 / var(--un-bg-opacity));}.bg-_a__a_f7f7f7_a_{--un-bg-opacity:1;background-color:rgb(247 247 247 / var(--un-bg-opacity));}.bg-_a__a_F9F4E1_a_{--un-bg-opacity:1;background-color:rgb(249 244 225 / var(--un-bg-opacity));}.bg-_a__a_FBE5E8_a_{--un-bg-opacity:1;background-color:rgb(251 229 232 / var(--un-bg-opacity));}.bg-_a__a_FDF4F4_a_{--un-bg-opacity:1;background-color:rgb(253 244 244 / var(--un-bg-opacity));}.bg-_a__a_fff_a_,.bg-_a__a_ffffff_a_{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity));}.bg-_a__a_FFF1F4_a_{--un-bg-opacity:1;background-color:rgb(255 241 244 / var(--un-bg-opacity));}.px-_a_56rpx_a_{padding-left:56rpx;padding-right:56rpx;}.indent-_a_2em_a_{text-indent:2em;}.text-_a_24rpx_a_{font-size:24rpx;}.text-_a_28rpx_a_{font-size:28rpx;}.text-_a__a__a_F59A23_a_{color:##F59A23;}.text-_a__a_000000_a_{--un-text-opacity:1;color:rgb(0 0 0 / var(--un-text-opacity));}.text-_a__a_006DC5_a_{--un-text-opacity:1;color:rgb(0 109 197 / var(--un-text-opacity));}.text-_a__a_0073FF_a_{--un-text-opacity:1;color:rgb(0 115 255 / var(--un-text-opacity));}.text-_a__a_06A8F0_a_{--un-text-opacity:1;color:rgb(6 168 240 / var(--un-text-opacity));}.text-_a__a_09BE4F_a_{--un-text-opacity:1;color:rgb(9 190 79 / var(--un-text-opacity));}.text-_a__a_1296db_a_{--un-text-opacity:1;color:rgb(18 150 219 / var(--un-text-opacity));}.text-_a__a_145DB2_a_{--un-text-opacity:1;color:rgb(20 93 178 / var(--un-text-opacity));}.text-_a__a_333_a_,.text-_a__a_333333_a_{--un-text-opacity:1;color:rgb(51 51 51 / var(--un-text-opacity));}.text-_a__a_529AFF_a_{--un-text-opacity:1;color:rgb(82 154 255 / var(--un-text-opacity));}.text-_a__a_666_a_,.text-_a__a_666666_a_,.text-_a_666{--un-text-opacity:1;color:rgb(102 102 102 / var(--un-text-opacity));}.text-_a__a_728190_a_{--un-text-opacity:1;color:rgb(114 129 144 / var(--un-text-opacity));}.text-_a__a_7F7F7F_a_{--un-text-opacity:1;color:rgb(127 127 127 / var(--un-text-opacity));}.text-_a__a_999_a_{--un-text-opacity:1;color:rgb(153 153 153 / var(--un-text-opacity));}.text-_a__a_D9001B_a_{--un-text-opacity:1;color:rgb(217 0 27 / var(--un-text-opacity));}.text-_a__a_DB393D_a_{--un-text-opacity:1;color:rgb(219 57 61 / var(--un-text-opacity));}.text-_a__a_e74c3c_a_{--un-text-opacity:1;color:rgb(231 76 60 / var(--un-text-opacity));}.text-_a__a_f69f2a_a_,.text-_a__a_F69F2A_a_{--un-text-opacity:1;color:rgb(246 159 42 / var(--un-text-opacity));}.text-_a__a_F86E3E_a_{--un-text-opacity:1;color:rgb(248 110 62 / var(--un-text-opacity));}.text-_a__a_FF2B2B_a_{--un-text-opacity:1;color:rgb(255 43 43 / var(--un-text-opacity));}.text-_a__a_FF5C23_a_{--un-text-opacity:1;color:rgb(255 92 35 / var(--un-text-opacity));}.text-_a__a_fff_a_,.text-_a__a_ffffff_a_{--un-text-opacity:1;color:rgb(255 255 255 / var(--un-text-opacity));}.line-height-_a_1_a_6_a_{line-height:1.6;}.line-height-_a_44rpx_a_{line-height:44rpx;}.visible{visibility:visible;}.absolute{position:absolute;}.fixed{position:fixed;}.relative{position:relative;}.sticky{position:sticky;}.static{position:static;}.bottom-0{bottom:0;}.bottom-200rpx{bottom:200rpx;}.bottom-26rpx{bottom:26rpx;}.bottom-40rpx{bottom:40rpx;}.left-0{left:0;}.left-40rpx{left:40rpx;}.right-0{right:0;}.right-43rpx{right:43rpx;}.top-0{top:0;}.z-0{z-index:0;}.z-10{z-index:10;}.grid{display:grid;}.m20rpx{margin:20rpx;}.mx3{margin-left:24rpx;margin-right:24rpx;}.mx30rpx{margin-left:30rpx;margin-right:30rpx;}.mx32rpx{margin-left:32rpx;margin-right:32rpx;}.my10rpx{margin-top:10rpx;margin-bottom:10rpx;}.my2{margin-top:16rpx;margin-bottom:16rpx;}.my20rpx{margin-top:20rpx;margin-bottom:20rpx;}.my3{margin-top:24rpx;margin-bottom:24rpx;}.mb-1{margin-bottom:8rpx;}.mb-2{margin-bottom:16rpx;}.mb-20rpx,.mb20rpx{margin-bottom:20rpx;}.mb-3{margin-bottom:24rpx;}.mb10rpx{margin-bottom:10rpx;}.mb180rpx{margin-bottom:180rpx;}.mb190rpx{margin-bottom:190rpx;}.mb40rpx{margin-bottom:40rpx;}.ml-10rpx,.ml10rpx{margin-left:10rpx;}.ml-auto{margin-left:auto;}.ml20rpx{margin-left:20rpx;}.ml30rpx{margin-left:30rpx;}.ml32rpx{margin-left:32rpx;}.mr-20rpx{margin-right:20rpx;}.mr10rpx{margin-right:10rpx;}.mr12rpx{margin-right:12rpx;}.ms{margin-inline-start:32rpx;}.mt-2,.mt2{margin-top:16rpx;}.mt10rpx{margin-top:10rpx;}.mt12rpx{margin-top:12rpx;}.mt20rpx{margin-top:20rpx;}.mt3{margin-top:24rpx;}.mt38rpx{margin-top:38rpx;}.mt40px{margin-top:40px;}.mt40rpx{margin-top:40rpx;}.mt60rpx{margin-top:60rpx;}.mt6rpx{margin-top:6rpx;}.mt80rpx{margin-top:80rpx;}.inline{display:inline;}.block{display:block;}.inline-block{display:inline-block;}.hidden{display:none;}.h-300rpx{height:300rpx;}.h-42rpx{height:42rpx;}.h-60{height:480rpx;}.h-full{height:100%;}.h100dvh{height:100dvh;}.h100vh{height:100vh;}.h124rpx{height:124rpx;}.h128rpx{height:128rpx;}.h140rpx{height:140rpx;}.h160rpx{height:160rpx;}.h180rpx{height:180rpx;}.h1px{height:1px;}.h2rpx{height:2rpx;}.h400rpx{height:400rpx;}.h420rpx{height:420rpx;}.h48rpx{height:48rpx;}.h500rpx{height:500rpx;}.h50vh{height:50vh;}.h80rpx{height:80rpx;}.h900rpx{height:900rpx;}.max-w-300rpx{max-width:300rpx;}.w-116rpx{width:116rpx;}.w-136rpx{width:136rpx;}.w-200rpx{width:200rpx;}.w-260rpx{width:260rpx;}.w-full{width:100%;}.w100rpx{width:100rpx;}.w128rpx{width:128rpx;}.w180rpx{width:180rpx;}.w66rpx{width:66rpx;}.w80rpx{width:80rpx;}.wxs{width:640rpx;}.flex{display:flex;}.inline-flex{display:inline-flex;}.flex-1{flex:1 1 0%;}.flex-shrink{flex-shrink:1;}.flex-col{flex-direction:column;}.flex-wrap{flex-wrap:wrap;}.table{display:table;}.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.items-start{align-items:flex-start;}.items-end{align-items:flex-end;}.items-center{align-items:center;}.self-stretch{align-self:stretch;}.justify-end{justify-content:flex-end;}.justify-center{justify-content:center;}.justify-between{justify-content:space-between;}.gap-2{gap:16rpx;}.gap-20rpx,.gap20rpx{gap:20rpx;}.gap10rpx{gap:10rpx;}.gap-x-10rpx{-moz-column-gap:10rpx;column-gap:10rpx;}.gap-y-10rpx{row-gap:10rpx;}.gap-y-20rpx{row-gap:20rpx;}.gap-y30rpx{row-gap:30rpx;}.space-y-2>view+view,.space-y-2>button+button,.space-y-2>text+text,.space-y-2>image+image{--un-space-y-reverse:0;margin-top:calc(16rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(16rpx * var(--un-space-y-reverse));}.overflow-y-auto{overflow-y:auto;}.text-ellipsis{text-overflow:ellipsis;}.break-all{word-break:break-all;}.b,.border,.border-1{border-width:1px;}.border-b{border-bottom-width:1px;}.border-gray-100{--un-border-opacity:1;border-color:rgb(243 244 246 / var(--un-border-opacity));}.border-gray-300{--un-border-opacity:1;border-color:rgb(209 213 219 / var(--un-border-opacity));}.border-red-500{--un-border-opacity:1;border-color:rgb(239 68 68 / var(--un-border-opacity));}.rounded-10rpx{border-radius:10rpx;}.rounded-20rpx{border-radius:20rpx;}.rounded-lg{border-radius:16rpx;}.border-solid{border-style:solid;}.bg-red-50{--un-bg-opacity:1;background-color:rgb(254 242 242 / var(--un-bg-opacity));}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity));}.p-20rpx,.p20rpx{padding:20rpx;}.p-30rpx,.p30rpx{padding:30rpx;}.p-4{padding:32rpx;}.p3{padding:24rpx;}.px,.px-4,.px32rpx{padding-left:32rpx;padding-right:32rpx;}.px-3,.px3{padding-left:24rpx;padding-right:24rpx;}.px-30rpx,.px30rpx{padding-left:30rpx;padding-right:30rpx;}.px10rpx{padding-left:10rpx;padding-right:10rpx;}.px12rpx{padding-left:12rpx;padding-right:12rpx;}.px20rpx{padding-left:20rpx;padding-right:20rpx;}.px33rpx{padding-left:33rpx;padding-right:33rpx;}.px40rpx{padding-left:40rpx;padding-right:40rpx;}.px50rpx{padding-left:50rpx;padding-right:50rpx;}.px60rpx{padding-left:60rpx;padding-right:60rpx;}.px70rpx{padding-left:70rpx;padding-right:70rpx;}.px80rpx{padding-left:80rpx;padding-right:80rpx;}.px8rpx{padding-left:8rpx;padding-right:8rpx;}.py-1{padding-top:8rpx;padding-bottom:8rpx;}.py-2{padding-top:16rpx;padding-bottom:16rpx;}.py10rpx{padding-top:10rpx;padding-bottom:10rpx;}.py12rpx{padding-top:12rpx;padding-bottom:12rpx;}.py20rpx{padding-top:20rpx;padding-bottom:20rpx;}.py30rpx{padding-top:30rpx;padding-bottom:30rpx;}.py6rpx{padding-top:6rpx;padding-bottom:6rpx;}.pb-10rpx,.pb10rpx{padding-bottom:10rpx;}.pb20rpx{padding-bottom:20rpx;}.pl16rpx{padding-left:16rpx;}.pr{padding-right:32rpx;}.pt-30rpx{padding-top:30rpx;}.pt10rpx{padding-top:10rpx;}.pt20rpx{padding-top:20rpx;}.text-center{text-align:center;}.text-right{text-align:right;}.vertical-base{vertical-align:baseline;}.text-22rpx{font-size:22rpx;}.text-24rpx{font-size:24rpx;}.text-26rpx{font-size:26rpx;}.text-28rpx{font-size:28rpx;}.text-2xl{font-size:48rpx;line-height:64rpx;}.text-30rpx{font-size:30rpx;}.text-32rpx{font-size:32rpx;}.text-34rpx{font-size:34rpx;}.text-40rpx{font-size:40rpx;}.text-48rpx{font-size:48rpx;}.text-lg{font-size:36rpx;line-height:56rpx;}.text-sm{font-size:28rpx;line-height:40rpx;}.text-gray-500{--un-text-opacity:1;color:rgb(107 114 128 / var(--un-text-opacity));}.text-gray-600{--un-text-opacity:1;color:rgb(75 85 99 / var(--un-text-opacity));}.text-gray-700{--un-text-opacity:1;color:rgb(55 65 81 / var(--un-text-opacity));}.text-gray-800{--un-text-opacity:1;color:rgb(31 41 55 / var(--un-text-opacity));}.text-red-500{--un-text-opacity:1;color:rgb(239 68 68 / var(--un-text-opacity));}.font-500{font-weight:500;}.font-600{font-weight:600;}.font-bold{font-weight:700;}.leading-44rpx{line-height:44rpx;}.leading-48rpx{line-height:48rpx;}.tab{-moz-tab-size:4;-o-tab-size:4;tab-size:4;}.outline{outline-style:solid;}.blur{--un-blur:blur(8px);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.ease,.ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}.ease-in{transition-timing-function:cubic-bezier(0.4, 0, 1, 1);} 
/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
  FirstUI组件内置的基础变量
  1.如果你是组件使用者，你可以通过修改这些变量的值来定制自己的组件主题，实现自定义主题功能
  2.如果全局修改需要在项目根目录下App.vue文件中引入此css文件
  3.如果组件中有props属性是针对颜色设置（默认为空值），则优先级：props变量（如果有传值）> 全局主题色
*/
page {
  /* 行为相关颜色 */
  --fui-color-primary: #007eff;
  --fui-color-success: #09be4f;
  --fui-color-warning: #ffb703;
  --fui-color-danger: #ff2b2b;
  --fui-color-purple: #6831ff;
  /* 文字基本颜色、其他辅助色 */
  /* 用于重量级文字信息、标题 */
  --fui-color-title: #181818;
  /* 用于普通级段落信息、引导词 */
  --fui-color-section: #333333;
  /* 用于次要标题内容 */
  --fui-color-subtitle: #7f7f7f;
  /* 用于底部标签、描述、次要文字信息 */
  --fui-color-label: #b2b2b2;
  /* 用于辅助、次要信息、禁用文字等。如：待输入状态描述文字，已点击按钮文字 */
  --fui-color-minor: #cccccc;
  --fui-color-white: #ffffff;
  /* 链接颜色 */
  --fui-color-link: #465cff;
  /* 背景颜色 */
  --fui-bg-color: #ffffff;
  /* 页面背景底色 */
  --fui-bg-color-grey: #f1f4fa;
  /* 内容模块底色 */
  --fui-bg-color-content: #f8f8f8;
  --fui-bg-color-red: rgba(255, 43, 43, 0.05);
  --fui-bg-color-yellow: rgba(255, 183, 3, 0.1);
  --fui-bg-color-purple: rgba(104, 49, 255, 0.05);
  --fui-bg-color-green: rgba(9, 190, 79, 0.05);
  /* 点击背景色 */
  --fui-bg-color-hover: rgba(0, 0, 0, 0.2);
  /* 遮罩颜色 */
  --fui-bg-color-mask: rgba(0, 0, 0, 0.6);
  /* 边框颜色 */
  --fui-color-border: #eeeeee;
  /* 阴影颜色 */
  --fui-color-shadow: rgba(2, 4, 38, 0.05);
  /*禁用态的透明度 */
  --fui-opacity-disabled: 0.5;
  /* 图标尺寸 */
  --fui-img-size-sm: 48rpx;
  --fui-img-size-base: 56rpx;
  --fui-img-size-middle: 64rpx;
  --fui-img-size-lg: 96rpx;
  /* 图片尺寸 */
  --fui-img-sm: 60rpx;
  --fui-img-base: 120rpx;
  --fui-img-lg: 240rpx;
  /* Border Radius */
  --fui-border-radius-sm: 16rpx;
  --fui-border-radius-base: 24rpx;
  --fui-border-radius-lg: 48rpx;
  --fui-border-radius-circle: 50%;
  /* 水平间距 */
  --fui-spacing-row-sm: 16rpx;
  --fui-spacing-row-base: 24rpx;
  --fui-spacing-row-lg: 32rpx;
  /* 垂直间距 */
  --fui-spacing-col-sm: 8rpx;
  --fui-spacing-col-base: 16rpx;
  --fui-spacing-col-lg: 24rpx;
}

/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}
swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.height-no-tabbar {
  height: calc(100vh - var(--window-top));
}
.height-tabbar {
  height: calc(100vh - var(--window-bottom) - var(--window-top));
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}