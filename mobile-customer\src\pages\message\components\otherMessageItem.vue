<template>
  <view class="bg-[#ffffff] px32rpx py20rpx" @click="handleClick">
    <view class="flex flex-col gap-y30rpx">
      <view class="text-26rpx text-[#7F7F7F]">{{ content }}</view>
      <view class="text-24rpx text-[#7F7F7F]">{{ item.createTime }}</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const content = computed(() => {
  if (props.item.communityName) {
    return `关于${props.item.communityName}${props.item.buildingNo}幢${props.item.unitNo}${props.item.houseNo}的纠错反馈`
  } else {
    return `关于${props.item.remark}的纠错反馈`
  }
})

const handleClick = () => {
  uni.navigateTo({
    url: '/pages-sub/messageDetail/index?id=' + props.item.id,
  })
}
</script>

<style lang="scss" scoped>
//
</style>
