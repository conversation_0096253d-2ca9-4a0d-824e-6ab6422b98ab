"use strict";
const setTop10BrandChartOption = (top10BrandData = []) => {
  const previewUrl = "http://10.0.9.193:8091/dfloor/s3/getPublicObjectByStream?fileName=";
  const rich = {
    // value: {
    //   lineHeight: 80,
    //   height: 80,
    //   align: 'center',
    // },
  };
  top10BrandData.forEach((item, index) => {
    item.product_id = (index + 1).toString();
  });
  for (let i = 0; i < top10BrandData.length; i++) {
    rich[top10BrandData[i].product_id] = {
      align: "center",
      height: 46,
      width: 46,
      backgroundColor: {
        image: `${previewUrl}${top10BrandData[i].pic_url}`
      }
    };
    console.log("top10BrandData[i]-------------", top10BrandData[i]);
  }
  console.log("rich", rich);
  return {
    grid: {
      left: "5%",
      // 左边距，百分比表示相对于容器宽度的比例
      right: "10%",
      // 右边距，同样使用百分比
      bottom: "5%",
      // 底边距，可以调整图表的高度
      top: "5%",
      // 顶边距，可以根据需要调整，给标题留出空间
      containLabel: true
      // 新增这个属性确保坐标轴标签在grid内
    },
    title: {
      text: "",
      left: "left",
      // 将标题对齐方式设置为左对齐
      top: "top",
      // 标题位置设置为顶部
      textStyle: {
        fontSize: 12,
        // 设置字体大小
        fontWeight: "normal",
        // 字体粗细
        color: "#333"
        // 字体颜色
      }
    },
    yAxis: {
      type: "category",
      inverse: true,
      data: top10BrandData.map((item) => item.product_id),
      axisLabel: {
        // rotate: 30,  // 标签旋转30度
        fontSize: 12,
        formatter: function(value) {
          return "{" + value + "| }";
        },
        margin: 10,
        width: 50,
        rich
      }
    },
    xAxis: {
      type: "value",
      minInterval: 1,
      position: "top"
    },
    series: [
      {
        data: top10BrandData.map((item) => item.vote_count),
        type: "bar",
        barWidth: "10",
        barCategoryGap: "80",
        // 配置标签显示
        label: {
          show: true,
          // 开启标签显示
          position: "right",
          // 标签位置设置为柱状图顶部
          textStyle: {
            fontSize: 12,
            // 设置字体大小
            color: "#333"
            // 设置字体颜色
          }
        },
        itemStyle: {
          color: function(params) {
            const colorList = ["#A524FF", "#FF7200", "#F8B35A"];
            if (params.dataIndex >= 3) {
              return "#41BDF4";
            } else {
              return colorList[params.dataIndex];
            }
          }
        }
      }
    ]
  };
};
const setTop10ElevatorChartOption = (top10ElevatorData = []) => {
  return {
    grid: {
      left: "5%",
      // 左边距，百分比表示相对于容器宽度的比例
      right: "10%",
      // 右边距，同样使用百分比
      bottom: "10%",
      // 底边距，可以调整图表的高度
      top: "20%",
      // 顶边距，可以根据需要调整，给标题留出空间
      containLabel: true
      // 新增这个属性确保坐标轴标签在grid内
    },
    title: {
      text: "得票数",
      left: "left",
      // 将标题对齐方式设置为左对齐
      top: "top",
      // 标题位置设置为顶部
      textStyle: {
        fontSize: 12,
        // 设置字体大小
        fontWeight: "normal",
        // 字体粗细
        color: "#333"
        // 字体颜色
      }
    },
    xAxis: {
      type: "category",
      data: top10ElevatorData.map((item) => item.brand_name),
      axisLabel: {
        // 旋转
        rotate: 40
      }
    },
    yAxis: {
      type: "value",
      minInterval: 1
    },
    series: [
      {
        data: top10ElevatorData.map((item) => item.vote_count),
        type: "bar",
        barWidth: 10,
        itemStyle: {
          color: "#40CFCF",
          // 圆角
          borderRadius: [10, 10, 0, 0]
        }
      }
    ]
  };
};
const setGovProcessChartOption = (govProcessData) => {
  return {
    tooltip: {
      formatter: "{a} <br/>{b} : {c}%",
      backgroundColor: "rgba(255,255,255, 0.6)",
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: "背景圈",
        type: "gauge",
        radius: "70%",
        center: ["50%", "50%"],
        startAngle: 220,
        endAngle: -40,
        axisLine: {
          // 坐标轴线
          roundCap: true,
          lineStyle: {
            // 属性lineStyle控制线条样式
            width: 8,
            color: [[1, "#E5E5E5"]]
          }
        },
        splitLine: {
          // 分隔线样式
          show: false
        },
        axisLabel: {
          // 刻度标签
          show: false
        },
        pointer: {
          show: false
        },
        axisTick: {
          // 刻度样式
          show: false
        },
        detail: {
          show: false,
          offsetCenter: [0, "50%"],
          fontSize: 12,
          color: "#fff",
          formatter: function() {
            return "SO2";
          }
        }
      },
      // 最外层含中间数据
      {
        name: "",
        type: "gauge",
        radius: "70%",
        startAngle: 220,
        endAngle: -45,
        min: 0,
        max: 100,
        axisLine: {
          show: true,
          roundCap: true,
          lineStyle: {
            width: 8,
            color: [
              [govProcessData[0].value / 100, "#387EFF"],
              [1, "rgba(255,255,255,.0)"]
            ]
          }
        },
        axisTick: {
          show: 0
        },
        splitLine: {
          show: 0
        },
        axisLabel: {
          show: 0
        },
        detail: {
          show: true,
          formatter: "{value}%",
          offsetCenter: [0, 0],
          fontSize: 12,
          color: "#387EFF"
        },
        title: {
          show: true,
          offsetCenter: [0, "90%"],
          fontSize: 11,
          color: "#666666",
          width: 100,
          overflow: "break"
        },
        pointer: {
          icon: "circle",
          // 箭头图标
          length: "10%",
          show: false,
          width: 30,
          height: 30,
          offsetCenter: [0, "-86%"],
          // 箭头位置
          itemStyle: {
            color: "#FFFFFF",
            // 箭头颜色
            shadowColor: "#3ABCFF",
            shadowBlur: 20
          }
        },
        data: govProcessData
      }
    ]
  };
};
exports.setGovProcessChartOption = setGovProcessChartOption;
exports.setTop10BrandChartOption = setTop10BrandChartOption;
exports.setTop10ElevatorChartOption = setTop10ElevatorChartOption;
