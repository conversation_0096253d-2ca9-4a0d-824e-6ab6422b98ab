<template>
  <view class="mb20rpx">
    <title-header title="品牌得票排名" />
    <view class="px30rpx bg-[#ffffff]">
      <view v-for="(brand, index) in brands" :key="index">
        <VoteNoticeItem
          :brand="brand"
          :index="index"
          :show-price="showPrice"
          :show-more-sku="showMoreSku"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import VoteNoticeItem from '@/pages-sub/voteNoticeDetail/components/voteNoticeItem.vue'

const props = defineProps({
  brands: {
    type: Array,
    default: () => [],
  },
  showPrice: {
    type: Boolean,
    default: true,
  },
  showMoreSku: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
//
</style>
