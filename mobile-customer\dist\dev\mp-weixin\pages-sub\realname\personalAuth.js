"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "personalAuth",
  setup(__props) {
    const authUrl = common_vendor.ref("");
    common_vendor.onLoad((options) => {
      console.log("onLoad", options);
      const itemUrl = decodeURIComponent(options.url);
      authUrl.value = itemUrl;
    });
    const handleMessage = (value) => {
      console.log("handleMessage", value);
    };
    const handlePostMessage = (value) => {
      console.log("handlePostMessge", value);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(authUrl),
        b: common_vendor.o(handleMessage),
        c: common_vendor.o(handlePostMessage)
      };
    };
  }
});
wx.createPage(_sfc_main);
