"use strict";
var StockStatusEnum = /* @__PURE__ */ ((StockStatusEnum2) => {
  StockStatusEnum2[StockStatusEnum2["SELL_OUT"] = 0] = "SELL_OUT";
  StockStatusEnum2[StockStatusEnum2["INSUFFICIENT"] = 1] = "INSUFFICIENT";
  StockStatusEnum2[StockStatusEnum2["TIGHT"] = 2] = "TIGHT";
  StockStatusEnum2[StockStatusEnum2["SUFFICIENT"] = 3] = "SUFFICIENT";
  return StockStatusEnum2;
})(StockStatusEnum || {});
const stockStatusMap = {
  [
    0
    /* SELL_OUT */
  ]: {
    text: "售罄",
    bgColor: "#FBE5E8",
    textColor: "#F86E3E"
  },
  [
    1
    /* INSUFFICIENT */
  ]: {
    text: "库存不足",
    bgColor: "#E6EEFF",
    textColor: "#006DC5"
  },
  [
    2
    /* TIGHT */
  ]: {
    text: "库存紧张",
    bgColor: "#E6EEFF",
    textColor: "#006DC5"
  },
  [
    3
    /* SUFFICIENT */
  ]: {
    text: "库存充足",
    bgColor: "#E6EEFF",
    textColor: "#006DC5"
  }
};
exports.StockStatusEnum = StockStatusEnum;
exports.stockStatusMap = stockStatusMap;
