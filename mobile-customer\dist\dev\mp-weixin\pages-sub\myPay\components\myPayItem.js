"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  _easycom_fui_button2();
}
const _easycom_fui_button = () => "../../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  _easycom_fui_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myPayItem",
  setup(__props) {
    const handleViewDetail = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/propertyDetail/index"
      });
    };
    const handleViewHouses = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/houseList/index"
      });
    };
    const handleCollect = () => {
      common_vendor.index.showToast({
        title: "收藏成功",
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleViewDetail),
        b: common_vendor.p({
          height: "56rpx",
          size: 24,
          background: "#eee",
          color: "#000",
          text: "查看支付情况"
        }),
        c: common_vendor.o(handleViewDetail),
        d: common_vendor.p({
          height: "56rpx",
          size: 24,
          background: "#eee",
          color: "#000",
          text: "查看投票结果"
        }),
        e: common_vendor.o(handleViewHouses),
        f: common_vendor.p({
          height: "56rpx",
          size: 24,
          background: "#eee",
          color: "#000",
          text: "查看合约"
        }),
        g: common_vendor.o(handleCollect),
        h: common_vendor.p({
          height: "56rpx",
          size: 24,
          background: "#FF9800",
          text: "立即支付"
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d1b13bc7"]]);
wx.createComponent(Component);
