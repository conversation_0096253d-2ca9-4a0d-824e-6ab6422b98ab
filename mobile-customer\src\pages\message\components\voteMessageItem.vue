<template>
  <view class="h140rpx bg-[#ffffff] px32rpx py20rpx" @click="handleClick">
    <view class="flex justify-between items-center h100%">
      <view class="w-200rpx h140rpx">
        <image
          class="w100% h100% rounded-10rpx"
          src="/static/images/dt_001.jpg"
          mode="scaleToFill"
        />
      </view>

      <view class="flex-1 ml10rpx flex flex-col justify-between h90%">
        <view class="text-26rpx text-[#7F7F7F]">{{ content }}</view>
        <view class="text-24rpx text-[#7F7F7F]">{{ item.updateTime }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const content = computed(() => {
  const _text = props.item.status === 1 ? '开始投票啦' : '投票结果公示'

  // return `${props.item.communityName}${props.item.buildingNo || ''}幢电梯更新${_text}！`
  return `${props.item.voteName}${_text}！`
})

const handleClick = () => {
  if (props.item.status === 1) {
    uni.navigateTo({
      url: `/pages-sub/voteNoticeDetail/index?id=${props.item.id}&communityId=${props.item.communityId}`,
    })
  } else {
    uni.navigateTo({
      url: `/pages-sub/votehasEndResultDetail/index?id=${props.item.id}`,
    })
  }
}
</script>

<style lang="scss" scoped>
//
</style>
