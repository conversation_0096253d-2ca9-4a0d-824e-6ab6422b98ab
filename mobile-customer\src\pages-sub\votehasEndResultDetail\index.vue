<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '投票结果',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="mt20rpx">
      <view class="bg-[#ffffff] px-30rpx pt-30rpx">
        <view class="text-30rpx font-bold text-[#333]">
          您好，{{ userInfo.realname }}，您正在查看{{ voteData.communityName }}的投票结果
        </view>
        <view
          class="text-26rpx text-[#666] mb10rpx flex"
          v-for="(item, index) in allAddressList"
          :key="index"
        >
          <text class="mr10rpx">产权房: {{ item.location }}</text>
          <fui-tag
            v-if="item.myself == 1"
            text="被授权"
            type="success"
            :padding="['6rpx', '6rpx']"
          ></fui-tag>
        </view>
        <view class="text-26rpx text-[#1296db] mb10rpx">
          投票截止日期: {{ voteData.voteEndDate }}
        </view>

        <!-- 投票进度条 -->
        <view class="">
          <progress
            :percent="voteData.voteRateValue"
            :stroke-width="12"
            :border-radius="8"
            activeColor="#0ABF4F"
          />
          <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
            <text>小区投票数: {{ voteData.voteCount }}</text>
            <text>投票率: {{ voteData.voteRate }}</text>
          </view>
        </view>
      </view>

      <view class="bg-[#ffffff] px-30rpx pb-10rpx" v-show="buildingList.length > 0">
        <view v-show="itemShowMore">
          <fui-tabs
            :tabs="buildingList"
            center
            selectedColor="#005ED1"
            sliderBackground="#005ED1"
            @change="handleBuildingChange"
          ></fui-tabs>
          <!-- 投票进度条 -->
          <view class="my20rpx">
            <progress
              :percent="buildVoteInfo.voteRateValue"
              :stroke-width="12"
              :border-radius="8"
              activeColor="#05D6A2"
            />
            <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
              <text>本楼栋票数: {{ buildVoteInfo.voteCount }}</text>
              <text>投票率: {{ buildVoteInfo.voteRate }}</text>
            </view>
          </view>

          <view class="my20rpx">
            <progress
              :percent="buildVoteInfo.voteAreaRateValue"
              :stroke-width="12"
              :border-radius="8"
              activeColor="#FFAE00"
            />
            <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
              <text>本楼栋投票面积: {{ buildVoteInfo.voteArea }}m²</text>
              <text>投票率: {{ buildVoteInfo.voteAreaRate }}</text>
            </view>
          </view>

          <view class="text24rpx font-bold text-center">
            {{
              !buildVoteInfo.pass
                ? '本楼栋暂未达到有效选票数，请尽快投票。'
                : '本楼栋已达到有效选票数。'
            }}
          </view>
        </view>

        <view
          class="flex items-center justify-center"
          :class="[itemShowMore ? 'image-active' : 'image-no-active']"
          @click="handleShowMore"
        >
          <image class="w-[48rpx] h-[48rpx]" src="/static/images/dt_007.png" mode="scaleToFill" />
        </view>
      </view>

      <view class="mt20rpx">
        <fui-tabs :tabs="tabs" center @change="changeTab"></fui-tabs>
        <view>
          <view v-show="currentTab === 0">
            <vote-result :voteData="voteData" />
          </view>
          <view v-show="currentTab === 1">
            <vote-brand-rank :brands="voteResultRank" :showPrice="false" :showMoreSku="true" />
          </view>
          <view v-show="currentTab === 2">
            <vote-my-vote-rank :brands="myVoteRank" />
          </view>
        </view>
      </view>
    </view>
    <fui-loading v-if="loading" isMask></fui-loading>
  </view>
</template>

<script lang="ts" setup>
import voteResult from './components/voteResult.vue'
import voteBrandRank from './components/voteBrandRank.vue'
import voteMyVoteRank from './components/myVoteRank.vue'
import {
  getAddressList,
  getAllGrantedAndSelfList,
  getBuildingRateByVote,
  getPrePrice,
  getVoteInfoByCurUser,
  getVoteRankProductTop10,
  queryBizVoteInfoById,
  querySubsidyPrice,
} from '@/service/app'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { calculatePrePrice } from '@/utils/util'

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const tabs = ref(['投票结果', '得票排名', '我的投票'])

const loading = ref(false)

const voteData: any = ref({})

const voteResultRank: any = ref([])
const myVoteRank: any = ref([])

// 当前选中的tab
const currentTab = ref(0)

const id = ref('')

const changeTab = (option: { index: number }) => {
  console.log('changeTab', option)
  currentTab.value = option.index
}

// 展开 收起
const itemShowMore = ref(true)

// 展开 收起 点击事件
const handleShowMore = () => {
  itemShowMore.value = !itemShowMore.value
}

// 获取投票详情
const fetchVoteDetail = async () => {
  try {
    loading.value = true
    const res: any = await queryBizVoteInfoById({
      params: {
        id: id.value,
      },
    })
    console.log('fetchVoteDetail', res)
    voteData.value = {
      ...res.result,
      voteRateValue: parseFloat(res.result.voteRate.replace('%', '')),
    }
    // 获取地址列表
    fetchAddressList(res.result.communityId, res.result.buildingCodes)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取投票结果
const fetchVoteResult = async () => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id: id.value,
        status: 2,
      },
    })
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 投票结果品牌排名
const fetchVoteResultRank = async () => {
  try {
    const res: any = await getVoteRankProductTop10({
      params: {
        voteId: id.value,
      },
    })
    console.log('fetchVoteResultRank', res)
    voteResultRank.value =
      res.result?.map((item) => ({
        ...item,
        skuName: item.win_sku || '5年维保',
        showMoreSku: false,
        // price: calculatePrePrice(item.price, formulaParams.value),
      })) || []
  } catch (error) {
    console.log('error', error)
  }
}

// 获取我的投票
const fetchMyVoteRank = async (_buildingId) => {
  try {
    const res: any = await getVoteInfoByCurUser({
      params: {
        voteId: id.value,
        buildingId: _buildingId,
      },
    })
    console.log('fetchMyVote', res)
    myVoteRank.value =
      res.result?.map((item) => ({
        ...item,
        vote_rate: item.voteRate,
        vote_count: item.voteCount,
        brand_name: item.brandName,
        product_name: item.productName,
        pic_url: item.picUrl,
        price: calculatePrePrice(item.price, formulaParams.value),
      })) || []
  } catch (error) {
    console.log('error', error)
  }
}

// 获取预估价格参数
const formulaParams: any = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async (_buildingId: string) => {
  try {
    const res = await getPrePrice({
      params: {
        buildingId: _buildingId,
        voteId: id.value,
      },
    })
    formulaParams.value = res.result

    // 计算最终承担的金额(08-25不用计算，接口已返回)
    // voteData.value.finalPrice = calculatePrePrice(voteData.value.price, formulaParams.value)
  } catch (error) {
    console.log('error', error)
  }
}
// 获取地址列表
const allAddressList: any = ref([])

// 地址列表去重 使用computed
const buildingList = computed(() => {
  return (
    allAddressList.value
      ?.map((c) => {
        return {
          ...c,
          name: `${c.buildingNo}幢`,
        }
      })
      ?.filter((item, index, self) => {
        return index === self.findIndex((t) => t.buildingId === item.buildingId)
      }) || []
  )
})

const fetchAddressList = async (communityId: string, buildingIds: string[]) => {
  const res = await getAllGrantedAndSelfList({
    params: {
      communityId,
      buildingIds,
    },
  })
  allAddressList.value = res.result
  if (res.result.length > 0) {
    // 不用处理 现在返回的投票信息就是按照楼栋的
    handleBuildingChange(res.result[0])
  }
}

// 切换楼栋
const handleBuildingChange = async (e) => {
  const { index, buildingId } = e
  console.log('handleBuildingChange', e)
  await fetchPrePrice(buildingId)
  // 获取栋投票数及投票率
  fetchBuildingRateByVote(buildingId)
  fetchMyVoteRank(buildingId)
}

// 获取栋投票数及投票率
const buildVoteInfo = ref({})
const fetchBuildingRateByVote = async (buildingId: string) => {
  try {
    loading.value = true
    const res = await getBuildingRateByVote({
      params: {
        voteId: id.value,
        buildingIds: buildingId,
      },
    })
    if (res.result && res.result.length > 0) {
      const _buildVoteInfo = res.result[0]
      buildVoteInfo.value = {
        ..._buildVoteInfo,
        voteRateValue: parseFloat(_buildVoteInfo.voteRate),
        voteAreaRateValue: parseFloat(_buildVoteInfo.voteAreaRate),
      }
    } else {
      buildVoteInfo.value = {}
    }
  } catch (error) {
    console.log('error', error)
    buildVoteInfo.value = {}
  } finally {
    loading.value = false
  }
}

// 获取国补每台价格
const fetchSubsidyPrice = async () => {
  const res: any = await querySubsidyPrice({})
  console.log('获取国补每台价格--fetchSubsidyPrice', res)
  // subsidyPrice.value = res.result
  voteData.value.subsidyPrice = res.result?.subsidiesPrice || 0
}

onLoad(async (options: { id: string }) => {
  id.value = options.id
  await fetchVoteDetail()
  // await fetchPrePrice()
  // fetchVoteResult()
  fetchVoteResultRank()
  // fetchMyVoteRank() 改成切换楼栋获取我的排名
  fetchSubsidyPrice()
})
</script>

<style lang="scss" scoped>
.bottom-zone {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.image-no-active {
  transform: rotate(0deg);
  transition: transform 0.2s ease-in-out;
}
.image-active {
  transform: rotate(180deg);
  transition: transform 0.2s ease-in-out;
}
</style>
