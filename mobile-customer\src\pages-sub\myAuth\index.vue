<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '我的授权',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="bg-[#ffffff] rounded-20rpx px30rpx py30rpx m20rpx" v-if="authList.length === 0">
      <view class="text-32rpx font-bold text-center">授权须知</view>
      <view class="text-26rpx mt10rpx">1、授权人只可以授权给自己的父母、伴侣、子女。</view>
      <view class="text-26rpx mt10rpx">2、授权后，被授权人将会有权力代替授权人进行投票。</view>
      <view class="text-26rpx mt10rpx">3、被授权人投票的结果视同授权人本人投票的结果。</view>
    </view>
    <view class="auth-list" v-else>
      <!-- 动态渲染不同关系的授权项 -->
      <view v-for="(item, index) in authList" :key="index" class="auth-item">
        <view class="auth-header">
          <text class="relation">{{ relationMap[item.accessRelationType] }}</text>
          <text :class="['auth-status', item.accessType ? 'auth-status-active' : '']">
            {{ item.accessType === 1 ? '授权人' : '被授权人' }}
          </text>
        </view>
        <view class="auth-content">
          <view class="auth-info">
            <view class="flex justify-between">
              <view class="info-row flex-1">
                <text class="info-label">姓名：</text>
                <text class="info-value">
                  {{ item.accessType === 1 ? item.name : item.accessName }}
                </text>
              </view>
              <view class="info-row w-58% justify-end">
                <text class="info-label">身份证号：</text>
                <text class="info-value">
                  {{ item.accessType === 1 ? item.cardCode : item.accessCardCode }}
                </text>
              </view>
            </view>

            <view class="info-row">
              <text class="info-label">授权时间：</text>
              <text class="info-value">{{ item.createTime }}</text>
            </view>
          </view>
          <view class="auth-action">
            <view>
              <button
                :class="['btn-cancel', item.accessType ? 'btn-cancel-active' : '']"
                @click="handleCancelAuth(item)"
              >
                {{ item.accessType ? '取消被授权' : '取消授权' }}
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-zone">
      <view class="flex gap-[20rpx] py20rpx px30rpx">
        <fui-button
          class="w50%"
          height="84rpx"
          background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
          border-width="0"
          text="为家人授权"
          @click="handleAddAuth"
        ></fui-button>
        <fui-button
          class="w50%"
          height="84rpx"
          background="linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)"
          border-width="0"
          text="申请被授权"
          @click="handleApplyAuth"
        ></fui-button>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import { cancelAuth, getMyAuthList } from '@/service/app'
import { EventName } from '@/utils/const'

const authList = ref([])

const loading = ref(false)

const relationMap = {
  0: '伴侣',
  1: '子女',
  2: '父母',
}

// 取消授权处理
const handleCancelAuth = (item: any) => {
  uni.showModal({
    title: '提示',
    content: `确定${item.accessType ? '取消被授权' : '取消授权'}吗？`,
    success: async (res) => {
      if (res.confirm) {
        // 调用取消授权接口
        // 这里可以根据item.id调用相应的API
        try {
          loading.value = true
          const res = await cancelAuth({
            params: {
              id: item.id,
            },
          })
          fetchAuthList()
          console.log('res', res)
        } catch (error) {
          console.log('error', error)
        } finally {
          loading.value = false
        }
      }
    },
  })
}

// 获取授权列表
const fetchAuthList = async () => {
  try {
    loading.value = true
    const res = await getMyAuthList({})
    authList.value = res.result || []
    loading.value = false
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 为家人授权
const handleAddAuth = () => {
  uni.navigateTo({
    url: '/pages-sub/myAuth/addAuth',
  })
}

// 申请被授权
const handleApplyAuth = () => {
  uni.navigateTo({
    url: '/pages-sub/myAuth/applyAuth',
  })
}

onShow(() => {
  fetchAuthList()
})

onLoad(() => {
  // fetchAuthList()
  // uni.$on(EventName.AUTH_LIST_UPDATE, () => {
  //   fetchAuthList()
  // })
})
</script>

<style lang="scss" scoped>
.bottom-zone {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.auth-list {
  padding: 20rpx;
  margin-bottom: 160rpx;
}

.auth-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  padding: 20rpx;
}

.auth-header {
  display: flex;
  justify-content: space-between;
  //   border-bottom: 1rpx solid #f0f0f0;
}

.relation {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.auth-status {
  font-size: 24rpx;
  color: #2196f3;
}

.auth-status-active {
  color: #ff9800;
}

.auth-content {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}

.auth-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #333;
}

.auth-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.btn-cancel {
  font-size: 24rpx;
  color: #2196f3;
  background-color: #e3f2fd;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  height: 60rpx;
  line-height: 40rpx;
}

.btn-cancel-active {
  color: #ff9800;
  background-color: #fff3e0;
}
</style>
