"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
const utils_util = require("../../../utils/util.js");
const service_app_miniProgramLogin = require("../../../service/app/miniProgramLogin.js");
require("../../../utils/http.js");
const common_assets = require("../../../common/assets.js");
const store_user = require("../../../store/user.js");
var define_import_meta_env_default = {};
if (!Array) {
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_input2 = common_vendor.resolveComponent("fui-input");
  const _easycom_fui_form_item2 = common_vendor.resolveComponent("fui-form-item");
  const _easycom_fui_countdown_verify2 = common_vendor.resolveComponent("fui-countdown-verify");
  const _easycom_fui_form2 = common_vendor.resolveComponent("fui-form");
  const _easycom_fui_checkbox2 = common_vendor.resolveComponent("fui-checkbox");
  const _easycom_fui_label2 = common_vendor.resolveComponent("fui-label");
  const _easycom_fui_checkbox_group2 = common_vendor.resolveComponent("fui-checkbox-group");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_icon2 + _easycom_fui_button2 + _easycom_fui_input2 + _easycom_fui_form_item2 + _easycom_fui_countdown_verify2 + _easycom_fui_form2 + _easycom_fui_checkbox2 + _easycom_fui_label2 + _easycom_fui_checkbox_group2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_icon = () => "../../../components/firstui/fui-icon/fui-icon.js";
const _easycom_fui_button = () => "../../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_input = () => "../../../components/firstui/fui-input/fui-input.js";
const _easycom_fui_form_item = () => "../../../components/firstui/fui-form-item/fui-form-item.js";
const _easycom_fui_countdown_verify = () => "../../../components/firstui/fui-countdown-verify/fui-countdown-verify.js";
const _easycom_fui_form = () => "../../../components/firstui/fui-form/fui-form.js";
const _easycom_fui_checkbox = () => "../../../components/firstui/fui-checkbox/fui-checkbox.js";
const _easycom_fui_label = () => "../../../components/firstui/fui-label/fui-label.js";
const _easycom_fui_checkbox_group = () => "../../../components/firstui/fui-checkbox-group/fui-checkbox-group.js";
const _easycom_fui_loading = () => "../../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_icon + _easycom_fui_button + _easycom_fui_input + _easycom_fui_form_item + _easycom_fui_countdown_verify + _easycom_fui_form + _easycom_fui_checkbox + _easycom_fui_label + _easycom_fui_checkbox_group + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const PUBLIC_KEY = define_import_meta_env_default.VITE_PUBLIC_KEY;
    const rsa = new common_vendor.JSEncrypt();
    rsa.setPublicKey(PUBLIC_KEY);
    const { setUserInfo, setToken } = store_user.useUserStore();
    const isAgree = common_vendor.ref(false);
    const formRef = common_vendor.ref();
    const countdownRef = common_vendor.ref();
    const isMiniLogin = common_vendor.ref(false);
    const loginWay = common_vendor.ref(0);
    const handleAgree = (e) => {
      console.log("isAgree", e);
      isAgree.value = e.checked;
    };
    const isLoading = common_vendor.ref(false);
    const code = common_vendor.ref("");
    const dynamicMobileCode = common_vendor.ref("");
    const getphonenumber = (e) => __async(this, null, function* () {
      console.log("getphonenumber", e);
      if (!e.code) {
        return;
      }
      dynamicMobileCode.value = e.code;
      try {
        isLoading.value = true;
        const loginRes = yield common_vendor.index.login();
        console.log("uni.login()", loginRes.code);
        code.value = loginRes.code;
        const res = yield service_app_miniProgramLogin.wxLogin({
          body: {
            // code: code.value,
            code: dynamicMobileCode.value
          }
        });
        console.log("res", res);
        if (res.code === 200) {
          setToken(res.result.token);
          setUserInfo(res.result.userInfo);
          if (redirectUrl.value) {
            common_vendor.index.switchTab({
              url: redirectUrl.value
            });
          } else {
            common_vendor.index.switchTab({
              url: "/pages/mine/index"
            });
          }
        }
        console.log("登录成功", res);
      } catch (error) {
        console.error("登录失败", error);
      } finally {
        isLoading.value = false;
      }
    });
    const handleLogin = () => __async(this, null, function* () {
      if (isAgree.value === false) {
        utils_util.showTextToast("请先同意用户服务协议和隐私权政策");
        return;
      }
    });
    const rules = [
      {
        name: "phoneNumber",
        rule: ["required", "isMobile"],
        msg: ["请输入手机号", "请输入正确的手机号"]
      },
      {
        name: "verifyCode",
        rule: ["required"],
        msg: ["请输入验证码"]
      }
    ];
    const loginFormData = common_vendor.ref({
      phoneNumber: "",
      verifyCode: ""
    });
    const handlePhoneLogin = () => {
      formRef.value.validator(loginFormData.value, rules).then((res) => __async(this, null, function* () {
        console.log("res", res);
        if (res.isPassed) {
          if (isAgree.value === false) {
            utils_util.showTextToast("请先同意用户服务协议和隐私权政策");
            return;
          }
          try {
            isLoading.value = true;
            const loginRes = yield common_vendor.index.login();
            console.log("uni.login()", loginRes.code);
            const res2 = yield service_app_miniProgramLogin.loginByVerifyCode({
              body: {
                code: loginRes.code,
                phoneNumber: loginFormData.value.phoneNumber,
                verifyCode: loginFormData.value.verifyCode
              }
            });
            if (res2.code === 200) {
              setToken(res2.result.token);
              setUserInfo(res2.result.userInfo);
              if (redirectUrl.value) {
                common_vendor.index.switchTab({
                  url: redirectUrl.value
                });
              } else {
                common_vendor.index.switchTab({
                  url: "/pages/mine/index"
                });
              }
            }
          } catch (e) {
            console.log("e", e);
          } finally {
            isLoading.value = false;
          }
        } else {
          utils_util.showTextToast(res.errorMsg);
        }
      }));
    };
    const handleSendCode = () => {
      var _a;
      console.log("获取验证码");
      (_a = countdownRef.value) == null ? void 0 : _a.success();
      formRef.value.validator(loginFormData.value, [
        {
          name: "phoneNumber",
          rule: ["required", "isMobile"],
          msg: ["请输入手机号", "请输入正确的手机号"]
        }
      ]).then((res) => __async(this, null, function* () {
        console.log("res", res);
        if (res.isPassed) {
          try {
            isLoading.value = true;
          } catch (e) {
            console.log("e", e);
          } finally {
            isLoading.value = false;
          }
        } else {
          utils_util.showTextToast(res.errorMsg);
          countdownRef.value.reset();
        }
      }));
    };
    const handleVerifyCodeEnd = () => {
      console.log("倒计时结束");
    };
    const handleChangeLoginWay = (way) => {
      loginWay.value = way;
    };
    const handleShowAgreement = () => {
      console.log("用户服务协议");
      common_vendor.index.navigateTo({ url: "/pages-sub/userAgreement/index" });
    };
    const handleShowPrivacyPolicy = () => {
      console.log("隐私权政策");
      common_vendor.index.navigateTo({ url: "/pages-sub/privacyPolicy/index" });
    };
    const redirectUrl = common_vendor.ref("");
    common_vendor.onLoad((options) => {
      console.log("options", options);
      redirectUrl.value = (options == null ? void 0 : options.redirectUrl) || "";
      console.log("redirectUrl", redirectUrl);
      isMiniLogin.value = true;
      loginWay.value = 0;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(common_assets.gy_app_041)
      }, {}, {
        b: common_vendor.unref(loginWay) === 0
      }, common_vendor.unref(loginWay) === 0 ? common_vendor.e({
        c: common_vendor.unref(isAgree)
      }, common_vendor.unref(isAgree) ? {
        d: common_vendor.p({
          name: "comment",
          size: 40,
          color: "#ffffff"
        }),
        e: common_vendor.o(getphonenumber),
        f: common_vendor.p({
          type: "success",
          radius: "96rpx",
          background: "#51A938",
          color: "#FFFFFF",
          ["open-type"]: "getPhoneNumber"
        })
      } : {
        g: common_vendor.p({
          name: "comment",
          size: 40,
          color: "#ffffff"
        }),
        h: common_vendor.o(handleLogin),
        i: common_vendor.p({
          type: "success",
          radius: "96rpx",
          background: "#51A938",
          color: "#FFFFFF"
        })
      }) : {}, {
        j: common_vendor.unref(loginWay) === 1
      }, common_vendor.unref(loginWay) === 1 ? {
        k: common_vendor.o(($event) => common_vendor.unref(loginFormData).phoneNumber = $event),
        l: common_vendor.p({
          type: "number",
          borderBottom: false,
          padding: ["0"],
          placeholder: "请输入手机号",
          maxlength: 11,
          modelValue: common_vendor.unref(loginFormData).phoneNumber
        }),
        m: common_vendor.p({
          label: "手机号"
        }),
        n: common_vendor.o(($event) => common_vendor.unref(loginFormData).verifyCode = $event),
        o: common_vendor.p({
          type: "number",
          borderBottom: false,
          padding: ["0"],
          placeholder: "请输入验证码",
          modelValue: common_vendor.unref(loginFormData).verifyCode
        }),
        p: common_vendor.sr(countdownRef, "c7632889-10,c7632889-8", {
          "k": "countdownRef"
        }),
        q: common_vendor.o(handleSendCode),
        r: common_vendor.o(handleVerifyCodeEnd),
        s: common_vendor.p({
          seconds: 10
        }),
        t: common_vendor.p({
          label: "验证码"
        }),
        v: common_vendor.sr(formRef, "c7632889-5,c7632889-0", {
          "k": "formRef"
        }),
        w: common_vendor.p({
          top: "0",
          model: common_vendor.unref(loginFormData),
          show: false
        }),
        x: common_vendor.p({
          name: "comment",
          size: 40,
          color: "#ffffff"
        }),
        y: common_vendor.o(handlePhoneLogin),
        z: common_vendor.p({
          type: "success",
          radius: "96rpx",
          background: "#465CFF",
          color: "#FFFFFF"
        })
      } : {}, {
        A: common_vendor.o(handleAgree),
        B: common_vendor.p({
          checked: common_vendor.unref(isAgree),
          scaleRatio: 0.9
        }),
        C: common_vendor.p({
          name: "checkbox"
        }),
        D: common_vendor.o(handleShowAgreement),
        E: common_vendor.p({
          width: "200rpx",
          color: "#FF9900",
          type: "link",
          size: 24
        }),
        F: common_vendor.o(handleShowPrivacyPolicy),
        G: common_vendor.p({
          width: "200rpx",
          color: "#FF9900",
          type: "link",
          size: 24
        }),
        H: common_vendor.unref(isMiniLogin)
      }, common_vendor.unref(isMiniLogin) ? common_vendor.e({
        I: common_vendor.unref(loginWay) === 0
      }, common_vendor.unref(loginWay) === 0 ? {
        J: common_vendor.o(($event) => handleChangeLoginWay(1)),
        K: common_vendor.p({
          name: "telephone",
          size: 60
        })
      } : {}, {
        L: common_vendor.unref(loginWay) === 1
      }, common_vendor.unref(loginWay) === 1 ? {
        M: common_vendor.o(($event) => handleChangeLoginWay(0)),
        N: common_vendor.p({
          name: "comment",
          size: 60
        })
      } : {}) : {}, {
        O: common_vendor.unref(isLoading)
      }, common_vendor.unref(isLoading) ? {
        P: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c7632889"]]);
wx.createPage(MiniProgramPage);
