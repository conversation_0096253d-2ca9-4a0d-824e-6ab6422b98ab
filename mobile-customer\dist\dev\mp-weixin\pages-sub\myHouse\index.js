"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_button + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const houseData = common_vendor.ref([]);
    const fetchMyHouse = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getMyHouse({
          params: {}
        });
        houseData.value = res.result || [];
        console.log("res", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const handleToError = (item) => {
      const { location, communityName, id } = item;
      common_vendor.index.navigateTo({
        url: `/pages-sub/myHouse/errorCorrection?id=${id}&location=${location}&communityName=${communityName}`
      });
    };
    common_vendor.onLoad(() => {
      fetchMyHouse();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(common_vendor.unref(houseData), (item, index, i0) => {
          return {
            a: common_vendor.t(item.location),
            b: common_vendor.t(item.communityName),
            c: common_vendor.t(item.userName),
            d: common_vendor.o(($event) => handleToError(item), index),
            e: "06c65627-1-" + i0 + ",06c65627-0",
            f: index
          };
        }),
        b: common_vendor.p({
          type: "link",
          color: "#465CFF"
        }),
        c: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        d: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-06c65627"]]);
wx.createPage(MiniProgramPage);
