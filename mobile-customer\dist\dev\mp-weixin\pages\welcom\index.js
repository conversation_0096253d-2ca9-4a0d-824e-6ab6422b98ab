"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _easycom_fui_table2 = common_vendor.resolveComponent("fui-table");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_table2 + _easycom_fui_button2 + _component_layout_default_uni)();
}
const _easycom_fui_table = () => "../../components/firstui/fui-table/fui-table.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  (_easycom_fui_table + _easycom_fui_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const headerData = common_vendor.ref([
      {
        prop: "name",
        label: "区属"
      },
      {
        prop: "phone",
        width: 120,
        label: "电话"
      }
    ]);
    const tableData = [
      {
        name: "江北新区",
        phone: "025-58627104"
      },
      {
        name: "玄武区",
        phone: "025-52317206"
      },
      {
        name: "秦淮区",
        phone: "025-87753865"
      },
      {
        name: "建邺区",
        phone: "025-87778417 025-87778035"
      },
      {
        name: "鼓楼区",
        phone: "025-83159703"
      },
      {
        name: "栖霞区",
        phone: "025-85300367"
      },
      {
        name: "雨花台区",
        phone: "025-52883522"
      },
      {
        name: "江宁区",
        phone: "025-52753969"
      },
      {
        name: "浦口区",
        phone: "025-58211022"
      },
      {
        name: "六合区",
        phone: "025-57121868"
      },
      {
        name: "溧水区",
        phone: "025-57221969"
      },
      {
        name: "高淳区",
        phone: "025-56861693"
      }
    ];
    const handleNotice = () => {
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    };
    const handleVote = () => {
      common_vendor.index.switchTab({
        url: "/pages/vote/index"
      });
    };
    const statusBarHeight = common_vendor.ref(0);
    const imageTop = common_vendor.computed(() => {
      return statusBarHeight.value + 10;
    });
    const getStatusBarHeight = () => {
      const res = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = res.statusBarHeight;
      console.log("statusBarHeight", statusBarHeight.value);
    };
    getStatusBarHeight();
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$1,
        b: common_assets._imports_1,
        c: common_vendor.unref(imageTop) + "px",
        d: common_vendor.o(handleNotice),
        e: common_assets._imports_2,
        f: common_vendor.unref(imageTop) + 46 + "px",
        g: common_assets._imports_0,
        h: common_assets._imports_4,
        i: common_assets._imports_5,
        j: common_assets._imports_6,
        k: common_assets._imports_7,
        l: common_assets._imports_8,
        m: common_assets._imports_9,
        n: common_assets._imports_10,
        o: common_assets._imports_11,
        p: common_assets._imports_12,
        q: common_assets._imports_13,
        r: common_assets._imports_14,
        s: common_vendor.p({
          full: true,
          gap: 100,
          itemList: tableData,
          header: common_vendor.unref(headerData)
        }),
        t: common_vendor.o(handleNotice),
        v: common_vendor.p({
          height: "84rpx",
          radius: "42rpx",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          ["border-width"]: "0",
          text: "查看公示"
        }),
        w: common_vendor.o(handleVote),
        x: common_vendor.p({
          height: "84rpx",
          radius: "42rpx",
          background: "linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)",
          ["border-width"]: "0",
          text: "立即投票"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-110edb6b"]]);
wx.createPage(MiniProgramPage);
