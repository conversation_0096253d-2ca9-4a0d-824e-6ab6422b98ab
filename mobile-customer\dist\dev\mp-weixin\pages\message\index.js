"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../store/index.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _component_safe_bottom_zone = common_vendor.resolveComponent("safe-bottom-zone");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_loading2 + _easycom_fui_empty2 + _component_safe_bottom_zone + _easycom_z_paging2 + _component_layout_default_uni)();
}
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_z_paging = () => "../../node-modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_fui_loading + _easycom_fui_empty + VoteMessageItem + OtherMessageItem + _easycom_z_paging)();
}
const VoteMessageItem = () => "./components/voteMessageItem.js";
const OtherMessageItem = () => "./components/otherMessageItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    common_vendor.ref(false);
    common_vendor.ref([]);
    const activeTab = common_vendor.ref("vote");
    const switchTab = (tab) => {
      activeTab.value = tab;
      paging.value.reload();
    };
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const queryList = (pageNo, pageSize) => __async(this, null, function* () {
      var _a, _b;
      const params = {
        pageNo,
        pageSize,
        order: "desc",
        column: "createTime"
      };
      if (["qianyue", "system"].includes(activeTab.value)) {
        paging.value.complete([], true);
        return;
      }
      if (activeTab.value === "vote") {
        params.self = true;
        params.minStatus = 1;
        console.log("params", params);
        try {
          const res = yield service_app_vote.queryBizVoteInfoList({ params });
          console.log("getMyVote", res);
          paging.value.complete(((_a = res == null ? void 0 : res.result) == null ? void 0 : _a.records) || [], true);
        } catch (error) {
          paging.value.complete(false);
        }
      }
      if (activeTab.value === "other") {
        params.self = true;
        console.log("params", params);
        try {
          const res = yield service_app_vote.queryBizErrorHouseList({ params });
          console.log("getMyVote", res);
          paging.value.complete(((_b = res == null ? void 0 : res.result) == null ? void 0 : _b.records) || [], true);
        } catch (error) {
          paging.value.complete(false);
        }
      }
    });
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    common_vendor.onLoad(() => __async(this, null, function* () {
    }));
    common_vendor.onShow(() => __async(this, null, function* () {
      if (!isLogined.value) {
        yield common_vendor.nextTick$1();
        paging.value.complete([], true);
      } else {
        activeTab.value = "vote";
        yield common_vendor.nextTick$1();
        paging.value.reload();
      }
    }));
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_2$1,
        b: common_vendor.o(($event) => switchTab("vote")),
        c: common_assets._imports_1$1,
        d: common_vendor.o(($event) => switchTab("system")),
        e: common_assets._imports_5$1,
        f: common_vendor.o(($event) => switchTab("other")),
        g: common_assets._imports_3,
        h: common_vendor.o(($event) => switchTab("qianyue")),
        i: common_vendor.p({
          isMask: true
        }),
        j: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          isFixed: true,
          title: "暂无数据"
        }),
        k: common_vendor.f(common_vendor.unref(dataList), (item, k0, i0) => {
          return common_vendor.e(common_vendor.unref(activeTab) === "vote" ? {
            a: "d10668a4-4-" + i0 + ",d10668a4-1",
            b: common_vendor.p({
              item
            })
          } : {}, common_vendor.unref(activeTab) === "other" ? {
            c: "d10668a4-5-" + i0 + ",d10668a4-1",
            d: common_vendor.p({
              item
            })
          } : {}, {
            e: item.id
          });
        }),
        l: common_vendor.unref(activeTab) === "vote",
        m: common_vendor.unref(activeTab) === "other",
        n: common_vendor.sr(paging, "d10668a4-1,d10668a4-0", {
          "k": "paging"
        }),
        o: common_vendor.o(queryList),
        p: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        q: common_vendor.p({
          ["auto-hide-loading-after-first-loaded"]: false,
          ["loading-full-fixed"]: true,
          ["paging-style"]: {
            "background-color": "#f7f7f7"
          },
          ["auto-show-back-to-top"]: true,
          ["back-to-top-bottom"]: 200,
          auto: false,
          ["lower-threshold"]: "150rpx",
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
});
wx.createPage(_sfc_main);
