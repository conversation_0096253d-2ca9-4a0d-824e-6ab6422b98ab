"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_fui_search_bar2 = common_vendor.resolveComponent("fui-search-bar");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_loading2 + _easycom_fui_empty2 + _easycom_fui_search_bar2 + _easycom_z_paging2 + _component_layout_default_uni)();
}
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_fui_search_bar = () => "../../components/firstui/fui-search-bar/fui-search-bar.js";
const _easycom_z_paging = () => "../../node-modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_fui_loading + _easycom_fui_empty + _easycom_fui_search_bar + VoteResult + safeBottomZone + _easycom_z_paging)();
}
const VoteResult = () => "../../components/common/voteResult.js";
const safeBottomZone = () => "../../components/common/safeBottomZone.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const searchVal = common_vendor.ref("");
    common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    const queryList = (pageNo, pageSize) => __async(this, null, function* () {
      var _a;
      const params = {
        pageNo,
        pageSize,
        communityName: searchVal.value ? `*${searchVal.value}*` : "",
        minStatus: 2,
        order: "desc",
        column: "voteResultTime"
      };
      console.log("params", params);
      try {
        const res = yield service_app_vote.queryBizVoteInfoList({ params });
        console.log("queryBizVoteInfoList", res);
        paging.value.complete(((_a = res == null ? void 0 : res.result) == null ? void 0 : _a.records) || [], true);
      } catch (error) {
        paging.value.complete(false);
      }
    });
    const search = () => {
      var _a;
      console.log("searchVal", searchVal.value);
      console.log("queryBizVoteInfoList", dataList.value);
      (_a = paging.value) == null ? void 0 : _a.reload();
    };
    const handleClear = () => {
      var _a;
      console.log("清除");
      (_a = paging.value) == null ? void 0 : _a.reload();
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          isMask: true
        }),
        b: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          isFixed: true,
          title: "暂无数据"
        }),
        c: common_vendor.o(search),
        d: common_vendor.o(handleClear),
        e: common_vendor.o(($event) => common_vendor.isRef(searchVal) ? searchVal.value = $event : null),
        f: common_vendor.p({
          placeholder: "请输入小区名称进行搜索",
          background: "#fff",
          radius: "36",
          inputBackground: "#fafafa",
          modelValue: common_vendor.unref(searchVal)
        }),
        g: common_vendor.f(common_vendor.unref(dataList), (item, k0, i0) => {
          return {
            a: "22773e97-5-" + i0 + ",22773e97-1",
            b: common_vendor.p({
              item
            }),
            c: item.id
          };
        }),
        h: common_vendor.sr(paging, "22773e97-1,22773e97-0", {
          "k": "paging"
        }),
        i: common_vendor.o(queryList),
        j: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        k: common_vendor.p({
          ["auto-hide-loading-after-first-loaded"]: false,
          ["loading-full-fixed"]: true,
          ["paging-style"]: {
            "background-color": "#f7f7f7"
          },
          ["auto-show-back-to-top"]: true,
          ["back-to-top-bottom"]: 200,
          auto: true,
          ["lower-threshold"]: "150rpx",
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-22773e97"]]);
wx.createPage(MiniProgramPage);
