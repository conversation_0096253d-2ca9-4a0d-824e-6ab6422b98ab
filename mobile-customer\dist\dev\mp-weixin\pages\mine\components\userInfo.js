"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _easycom_fui_avatar2 = common_vendor.resolveComponent("fui-avatar");
  const _easycom_fui_text2 = common_vendor.resolveComponent("fui-text");
  (_easycom_fui_avatar2 + _easycom_fui_text2)();
}
const _easycom_fui_avatar = () => "../../../components/firstui/fui-avatar/fui-avatar.js";
const _easycom_fui_text = () => "../../../components/firstui/fui-text/fui-text.js";
if (!Math) {
  (_easycom_fui_avatar + _easycom_fui_text)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "userInfo",
  props: {
    userInfo: {
      type: Object,
      default: () => ({})
    },
    isLogined: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  setup(__props) {
    const props = __props;
    const userAvatar = common_vendor.computed(() => {
      return props.userInfo.avatar || "/static/images/gy_app_002.jpg";
    });
    const handleUserInfoClick = () => {
      console.log("handleUserInfoClick", props.userInfo, props.isLogined);
      if (props.isLogined)
        ;
      else {
        common_vendor.index.navigateTo({ url: "/pages-sub/mine/login/index" });
      }
    };
    const goAuth = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/realname/index`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          src: common_vendor.unref(userAvatar),
          shape: "circle",
          width: "120",
          height: "120"
        }),
        b: common_vendor.o(handleUserInfoClick),
        c: common_vendor.t(__props.isLogined ? __props.userInfo.realname || "微信用户" : "登录/注册"),
        d: __props.isLogined
      }, __props.isLogined ? common_vendor.e({
        e: __props.userInfo.isRealAuth !== 1
      }, __props.userInfo.isRealAuth !== 1 ? {
        f: common_vendor.o(goAuth),
        g: common_assets._imports_0$8
      } : {}, {
        h: __props.userInfo.isRealAuth === 1
      }, __props.userInfo.isRealAuth === 1 ? {
        i: common_assets._imports_1$3
      } : {}) : {}, {
        j: __props.isLogined
      }, __props.isLogined ? {
        k: common_vendor.p({
          size: 24,
          color: "#ffffff",
          text: __props.userInfo.phone,
          ["text-type"]: "mobile",
          format: true
        })
      } : {}, {
        l: common_vendor.o(handleUserInfoClick)
      });
    };
  }
});
wx.createComponent(_sfc_main);
