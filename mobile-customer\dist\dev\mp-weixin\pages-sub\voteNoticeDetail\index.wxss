/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-419b10d3 {
  background-color: #f7f7f7;
  height: 100vh;
  overflow-y: auto;
}
.header.data-v-419b10d3 {
  background-color: #ffffff;
  padding: 30rpx 30rpx 10rpx 30rpx;
}
.title.data-v-419b10d3 {
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 32rpx;
  color: #000000;
  margin: 0rpx 0 10rpx 0;
}
.address.data-v-419b10d3,
.deadline.data-v-419b10d3 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.deadline.data-v-419b10d3 {
  color: #1296db;
}
.progress-wrapper.data-v-419b10d3 {
  margin: 20rpx 0;
}
.progress-info.data-v-419b10d3 {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin: 10rpx 0;
}
.countdown.data-v-419b10d3 {
  margin: 30rpx 0 0 0;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  padding: 30rpx;
  background-color: #ffffff;
}
.time-blocks.data-v-419b10d3 {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  color: #005ed1;
}
.time-block.data-v-419b10d3 {
  font-size: 38rpx;
  margin: 0 10rpx;
  font-weight: bold;
}
.bottom-zone.data-v-419b10d3 {
  position: fixed;
  background-color: #ffffff;
  bottom: 0;
  width: 100%;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.alert-info.data-v-419b10d3 {
  width: 689rpx;
  height: 54rpx;
  background: #fdf4f4;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.alert-info .alert-info-text.data-v-419b10d3 {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #ff2b2b;
  line-height: 24rpx;
  margin-left: 20rpx;
}
.toggle-btn.data-v-419b10d3 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #ffffff;
  color: #666666;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}
.toggle-btn .arrow-icon.data-v-419b10d3 {
  margin-left: 10rpx;
  transition: transform 0.3s ease;
}
.toggle-btn .rotate.data-v-419b10d3 {
  transform: rotate(180deg);
}