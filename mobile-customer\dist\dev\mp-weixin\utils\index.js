"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const utils_platform = require("./platform.js");
const pages = [
  {
    path: "pages/welcome/index",
    type: "home",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: ""
    }
  },
  {
    path: "pages/home/<USER>",
    type: "page",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "首页"
    }
  },
  {
    path: "pages/message/index",
    type: "page",
    style: {
      navigationBarTitleText: "消息"
    }
  },
  {
    path: "pages/mine/index",
    type: "page",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "我的"
    }
  },
  {
    path: "pages/vote/index",
    type: "page",
    style: {
      navigationBarTitleText: "投票",
      componentPlaceholder: {
        "base-chart": "view"
      }
    }
  }
];
const subPackages = [
  {
    root: "pages-sub",
    pages: [
      {
        path: "authNotice/index",
        type: "page",
        style: {
          navigationBarTitleText: "授权须知"
        }
      },
      {
        path: "brandDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "商品详情"
        }
      },
      {
        path: "installRecord/index",
        type: "page",
        style: {
          navigationBarTitleText: "安装记录"
        }
      },
      {
        path: "messageDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "消息详情"
        }
      },
      {
        path: "myAuth/addAuth",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "为家人授权"
        }
      },
      {
        path: "myAuth/applyAuth",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "申请被授权"
        }
      },
      {
        path: "myAuth/faceAuth",
        type: "page",
        style: {
          navigationBarTitleText: "授权认证"
        }
      },
      {
        path: "myAuth/index",
        type: "page",
        style: {
          navigationBarTitleText: "我的授权"
        }
      },
      {
        path: "myContract/index",
        type: "page",
        style: {
          navigationBarTitleText: "合同签订"
        }
      },
      {
        path: "myHouse/errorCorrection",
        type: "page",
        style: {
          navigationBarTitleText: "纠错"
        }
      },
      {
        path: "myHouse/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "我的房产"
        }
      },
      {
        path: "myPay/index",
        type: "page",
        style: {
          navigationBarTitleText: "我的支付"
        }
      },
      {
        path: "myVote/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "我的投票"
        }
      },
      {
        path: "privacyPolicy/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "隐私政策"
        }
      },
      {
        path: "realname/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "个人实名认证"
        }
      },
      {
        path: "realname/personalAuth",
        type: "page",
        style: {
          navigationBarTitleText: "个人认证"
        }
      },
      {
        path: "scanCodeVote/index",
        type: "page",
        style: {
          navigationStyle: "custom",
          navigationBarTitleText: "投票"
        }
      },
      {
        path: "userAgreement/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "用户协议"
        }
      },
      {
        path: "vote/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "投票"
        }
      },
      {
        path: "votehasEndResultDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "投票结果"
        }
      },
      {
        path: "voteNoticeDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "公告详情"
        }
      },
      {
        path: "voteNoticeList/index",
        type: "page",
        style: {
          navigationBarTitleText: "公告"
        }
      },
      {
        path: "votePublicDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "公示详情"
        }
      },
      {
        path: "voteResultDetail/index",
        type: "page",
        style: {
          navigationBarTitleText: "投票结果"
        }
      },
      {
        path: "voteResultList/index",
        type: "page",
        style: {
          navigationBarTitleText: "公示"
        }
      },
      {
        path: "voteResultsAdvance/index",
        type: "page",
        style: {
          navigationBarTitleText: "投票信息"
        }
      },
      {
        path: "voteRules/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "投票规则"
        }
      },
      {
        path: "mine/login/index",
        type: "page",
        style: {
          navigationBarTitleText: "登录"
        }
      }
    ]
  },
  {
    root: "echarts",
    pages: [
      {
        path: "index",
        type: "page"
      }
    ]
  }
];
const ensureDecodeURIComponent = (url) => {
  if (url.startsWith("%")) {
    return ensureDecodeURIComponent(decodeURIComponent(url));
  }
  return url;
};
const getUrlObj = (url) => {
  const [path, queryStr] = url.split("?");
  if (!queryStr) {
    return {
      path,
      query: {}
    };
  }
  const query = {};
  queryStr.split("&").forEach((item) => {
    const [key, value] = item.split("=");
    query[key] = ensureDecodeURIComponent(value);
  });
  return { path, query };
};
const getAllPages = (key = "needLogin") => {
  const mainPages = [
    ...pages.filter((page) => !key || page[key]).map((page) => __spreadProps(__spreadValues({}, page), {
      path: `/${page.path}`
    }))
  ];
  const subPages = [];
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj;
    subPageObj.pages.filter((page) => !key || page[key]).forEach((page) => {
      subPages.push(__spreadProps(__spreadValues({}, page), {
        path: `/${root}/${page.path}`
      }));
    });
  });
  const result = [...mainPages, ...subPages];
  return result;
};
const getNeedLoginPages = () => getAllPages("needLogin").map((page) => page.path);
getAllPages("needLogin").map((page) => page.path);
const getEnvBaseUrl = () => {
  let baseUrl = "http://**********:8091/dfloor";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUrl = baseUrl;
        break;
      case "trial":
        baseUrl = baseUrl;
        break;
      case "release":
        baseUrl = baseUrl;
        break;
    }
  }
  return baseUrl;
};
exports.getEnvBaseUrl = getEnvBaseUrl;
exports.getNeedLoginPages = getNeedLoginPages;
exports.getUrlObj = getUrlObj;
