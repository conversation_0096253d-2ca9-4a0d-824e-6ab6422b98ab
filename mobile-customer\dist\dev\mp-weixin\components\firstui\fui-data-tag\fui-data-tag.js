"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "fui-data-tag",
  emits: ["change", "input", "update:modelValue"],
  props: {
    options: {
      type: Array,
      default() {
        return [];
      }
    },
    textKey: {
      type: String,
      default: "text"
    },
    valueKey: {
      type: String,
      default: "value"
    },
    isAllKey: {
      type: String,
      default: "isAll"
    },
    disableKey: {
      type: String,
      default: "disable"
    },
    modelValue: {
      type: [Array, String, Number],
      default() {
        return [];
      }
    },
    value: {
      type: [Array, String, Number],
      default() {
        return [];
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    //最小选择数，仅单选有效，可选值仅0、1
    min: {
      type: [Number, String],
      default: 1
    },
    //最小选择数，仅多选有效
    multipleMin: {
      type: [Number, String],
      default: 0
    },
    //最大选择数
    max: {
      type: [Number, String],
      default: -1
    },
    width: {
      type: [Number, String],
      default: 0
    },
    height: {
      type: [Number, String],
      default: 0
    },
    padding: {
      type: Array,
      default() {
        return ["16rpx", "32rpx"];
      }
    },
    gap: {
      type: [Number, String],
      default: 20
    },
    radius: {
      type: [Number, String],
      default: 6
    },
    size: {
      type: [Number, String],
      default: 24
    },
    color: {
      type: String,
      default: "#333"
    },
    activeColor: {
      type: String,
      default: ""
    },
    background: {
      type: String,
      default: "#fff"
    },
    activeBgColor: {
      type: String,
      default: "#fff"
    },
    //默认边框颜色
    defaultBorderColor: {
      type: String,
      default: ""
    },
    borderColor: {
      type: String,
      default: ""
    },
    //设为true时，圆角值不建议设过大
    mark: {
      type: Boolean,
      default: false
    },
    markSize: {
      type: [Number, String],
      default: 52
    },
    markColor: {
      type: String,
      default: ""
    },
    //V2.1.0+ 标签选择 是否强制一行显示，外层需要自行设置横向滚动容器
    nowrap: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    options(vals) {
      this.initData(vals);
    },
    modelValue(vals) {
      this.modelChange(vals);
    },
    value(vals) {
      this.modelChange(vals);
    }
  },
  created() {
    this.initData(this.options);
  },
  computed: {
    primaryColor() {
      const app = common_vendor.index && common_vendor.index.$fui && common_vendor.index.$fui.color;
      return app && app.primary || "#465CFF";
    }
  },
  data() {
    return {
      icon: "",
      dataList: [],
      val: "",
      vals: []
    };
  },
  methods: {
    initData(vals) {
      vals = JSON.parse(JSON.stringify(vals));
      if (vals && vals.length > 0) {
        if (typeof vals[0] !== "object") {
          vals = vals.map((item, index) => {
            return {
              [this.textKey]: item,
              [this.valueKey]: item,
              selected: false
            };
          });
        } else {
          vals.map((item, index) => {
            item.selected = item.selected || false;
            if (item[this.valueKey] === void 0) {
              item[this.valueKey] = item[this.textKey];
            }
          });
        }
        this.dataList = vals;
        this.modelChange(this.modelValue);
      }
    },
    emitsChange(e) {
      this.$emit("change", e);
      this.$emit("input", e.detail.value);
      this.$emit("update:modelValue", e.detail.value);
    },
    radioChange(index, model) {
      const min = Number(this.min);
      if (this.val === model[this.valueKey] && min > 0)
        return;
      let val = "";
      let i = index;
      let entity = {};
      this.dataList.forEach((item, idx) => {
        if (idx === index) {
          const bool = this.val === item[this.valueKey] && min <= 0;
          val = bool ? "" : item[this.valueKey];
          i = bool ? -1 : index;
          entity = bool ? {} : item;
          item.selected = bool ? false : true;
        } else {
          item.selected = false;
        }
      });
      this.val = val;
      let e = {
        detail: {
          index: i,
          value: val,
          item: entity
        }
      };
      this.emitsChange(e);
    },
    toast(title) {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    },
    checkboxChange(index, model) {
      const min = Number(this.multipleMin);
      const max = Number(this.max);
      let vals = this.vals;
      let i = vals.indexOf(model[this.valueKey]);
      if (vals.length <= min && i !== -1) {
        this.toast(`至少选择${min}个选项`);
        return;
      }
      const isAllIndex = this.dataList.findIndex((item2) => item2[this.isAllKey]);
      let item = this.dataList[index];
      if (isAllIndex !== -1) {
        if (model[this.isAllKey]) {
          item.selected = i !== -1 ? false : true;
          if (item.selected) {
            vals = [model[this.valueKey]];
          } else {
            vals.splice(i, 1);
          }
          this.dataList.forEach((item2, idx) => {
            if (index !== idx) {
              item2.selected = false;
            }
          });
        } else {
          if (vals.length >= max && max !== -1 && i === -1) {
            this.toast(`最多只能选择${max}个选项`);
            return;
          }
          let entity2 = this.dataList[isAllIndex];
          const allI = vals.indexOf(entity2[this.valueKey]);
          if (allI !== -1) {
            entity2.selected = false;
            vals.splice(allI, 1);
          }
          this.dataList.forEach((item2, idx) => {
            if (index === idx) {
              item2.selected = i !== -1 ? false : true;
              if (item2.selected) {
                vals.push(item2[this.valueKey]);
              } else {
                vals.splice(i, 1);
              }
            }
          });
        }
      } else {
        if (vals.length >= max && max !== -1 && i === -1) {
          this.toast(`最多只能选择${max}个选项`);
          return;
        }
        item.selected = i !== -1 ? false : true;
        if (item.selected) {
          vals.push(item[this.valueKey]);
        } else {
          vals.splice(i, 1);
        }
      }
      this.vals = vals;
      const entity = this.dataList.filter((item2) => vals.indexOf(item2[this.valueKey]) != -1);
      let e = {
        detail: {
          value: vals,
          item: entity
        }
      };
      this.emitsChange(e);
    },
    modelChange(vals) {
      if (this.multiple) {
        this.dataList.forEach((item) => {
          if (vals.includes(item[this.valueKey])) {
            item.selected = true;
          } else {
            item.selected = false;
          }
        });
        this.vals = vals;
      } else {
        this.dataList.forEach((item) => {
          if (vals == item[this.valueKey]) {
            item.selected = true;
          } else {
            item.selected = false;
          }
        });
        this.val = vals;
      }
    },
    handleClick(index) {
      let item = this.dataList[index];
      if (item[this.disableKey])
        return;
      if (this.multiple) {
        this.checkboxChange(index, item);
      } else {
        this.radioChange(index, item);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.dataList, (item, index, i0) => {
      return common_vendor.e({
        a: $props.mark && item.selected
      }, $props.mark && item.selected ? {
        b: common_vendor.t($data.icon),
        c: $props.markColor || $options.primaryColor,
        d: $props.markSize + "rpx",
        e: $props.markSize + "rpx"
      } : {}, {
        f: common_vendor.o(($event) => $options.handleClick(index), index),
        g: common_vendor.t(item[$props.textKey]),
        h: item.selected ? $props.activeColor || $options.primaryColor : $props.color,
        i: item[$props.disableKey] ? 1 : "",
        j: item.selected ? $props.activeBgColor : $props.background,
        k: item.selected ? $props.borderColor || $options.primaryColor : $props.defaultBorderColor || $props.background,
        l: index
      });
    }),
    b: $props.size + "rpx",
    c: $props.width ? $props.width + "rpx" : "auto",
    d: $props.height ? $props.height + "rpx" : "auto",
    e: $props.padding[0] || 0,
    f: $props.padding[1] || 0,
    g: $props.padding[2] || $props.padding[0] || 0,
    h: $props.padding[3] || $props.padding[1] || 0,
    i: $props.radius + "rpx",
    j: $props.gap + "rpx",
    k: $props.gap + "rpx",
    l: common_vendor.n($props.nowrap ? "fui-data__tag-nowrap" : "fui-data__tag-flexwrap"),
    m: "-" + $props.gap + "rpx"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-660d5831"]]);
wx.createComponent(Component);
