"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
require("../../../utils/http.js");
const service_app_vote = require("../../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_overflow_hidden2 = common_vendor.resolveComponent("fui-overflow-hidden");
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  const _easycom_fui_dropdown_menu2 = common_vendor.resolveComponent("fui-dropdown-menu");
  (_easycom_fui_overflow_hidden2 + _easycom_fui_icon2 + _easycom_fui_dropdown_menu2)();
}
const _easycom_fui_overflow_hidden = () => "../../../components/firstui/fui-overflow-hidden/fui-overflow-hidden.js";
const _easycom_fui_icon = () => "../../../components/firstui/fui-icon/fui-icon.js";
const _easycom_fui_dropdown_menu = () => "../../../components/firstui/fui-dropdown-menu/fui-dropdown-menu.js";
if (!Math) {
  (_easycom_fui_overflow_hidden + _easycom_fui_icon + _easycom_fui_dropdown_menu)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteHouseSelect",
  props: {
    dropdownMenu: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["houseVoteSelectClick", "houseVoteResult"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const _dropdownMenu = {
      maxHeight: "600",
      minWidth: "350",
      padding: "26rpx",
      isMask: true,
      selectedColor: "#007EFF",
      isCheckMark: true,
      checkmarkColor: "#007EFF",
      isReverse: true,
      size: 24,
      left: 0
    };
    const dropdownMenuProps = common_vendor.ref(__spreadValues(__spreadValues({}, _dropdownMenu), props.dropdownMenu));
    const itemOptions = common_vendor.ref([]);
    const ddmRange = common_vendor.ref();
    const range = common_vendor.ref({ text: "暂无数据", value: "", buildingId: "" });
    const rangeShow = common_vendor.ref(false);
    const dropdownMenuClick = () => {
      console.log("dropdownMenuClick");
      ddmRange.value.show();
      rangeShow.value = true;
    };
    const rangeItemClick = (e) => {
      console.log("rangeItemClick", e);
      console.log("range.value", range.value);
      if (range.value.buildingId === e.buildingId) {
        rangeClose();
        return;
      }
      range.value = e;
      emit("houseVoteSelectClick", e);
      rangeClose();
    };
    const rangeClose = () => {
      console.log("rangeClose");
      rangeShow.value = false;
    };
    const closeDropdown = () => {
      ddmRange.value.close(2);
      rangeClose();
    };
    const emit = __emit;
    common_vendor.onMounted(() => {
      rangeClose();
    });
    const fetchCommunityList = () => __async(this, null, function* () {
      var _a;
      console.log("fetchCommunityList");
      try {
        const res = yield service_app_vote.queryCommunityList({});
        console.log("fetchStoreData", res);
        if (res.result === 1 || res.result === 2 || res.result === 3) {
          itemOptions.value = [];
          emit("houseVoteResult", res.result);
          return;
        }
        itemOptions.value = ((_a = res == null ? void 0 : res.result) == null ? void 0 : _a.map((item, index) => ({
          text: `${item.communityName}${item.buildingNo}幢投票`,
          value: item.id,
          buildingId: item.buildingId,
          buildingNo: item.buildingNo,
          checked: index === 0
        }))) || [];
        if (itemOptions.value.length > 0) {
          console.log("emit-storeSelectClick", itemOptions.value[0]);
          range.value = itemOptions.value[0];
          emit("houseVoteSelectClick", itemOptions.value[0]);
        }
      } catch (error) {
        console.log(error);
      }
    });
    __expose({
      fetchCommunityList,
      closeDropdown
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          size: 28,
          fontWeight: "bold",
          text: common_vendor.unref(range).text
        }),
        b: common_vendor.p({
          name: "turningdown",
          size: 32
        }),
        c: common_vendor.unref(rangeShow) ? 1 : "",
        d: common_vendor.o(dropdownMenuClick),
        e: common_vendor.sr(ddmRange, "c5509dc0-0", {
          "k": "ddmRange"
        }),
        f: common_vendor.o(rangeItemClick),
        g: common_vendor.o(rangeClose),
        h: common_vendor.unref(itemOptions).length > 0,
        i: common_vendor.p(__spreadValues({
          options: common_vendor.unref(itemOptions)
        }, common_vendor.unref(dropdownMenuProps)))
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c5509dc0"]]);
wx.createComponent(Component);
