<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '为家人授权',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="mt20rpx">
      <fui-section isLine title="被授权人信息" descr="请填写被授权人信息"></fui-section>
    </view>

    <view class="bg-[#ffffff] rounded-20rpx px30rpx m20rpx">
      <fui-form
        error-position="1"
        labelSize="24"
        ref="formRef"
        top="0"
        :model="formData"
        :show="false"
      >
        <fui-form-item
          :padding="['30rpx', '0']"
          labelWidth="100%"
          label="您要授权给哪位家庭成员"
          :bottomBorder="false"
          prop="accessRelationType"
          error-align="left"
        >
          <template v-slot:vertical>
            <fui-data-tag
              defaultBorderColor="#FBDA41"
              background="#F1F4FA"
              activeBgColor="#FEDF48"
              border-color="#FBDA41"
              active-color="#333"
              radius="24"
              v-model="formData.accessRelationType"
              :options="options"
            ></fui-data-tag>
          </template>
        </fui-form-item>
        <fui-form-item
          label="被授权人姓名"
          :padding="['30rpx', '0']"
          :labelWidth="200"
          prop="name"
          error-align="left"
        >
          <fui-input
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="被授权人姓名"
            v-model="formData.name"
          ></fui-input>
        </fui-form-item>

        <fui-form-item
          :padding="['30rpx', '0']"
          label="被授权人身份证号"
          :labelWidth="280"
          prop="cardCode"
          error-align="left"
          :bottomBorder="false"
        >
          <fui-input
            :maxlength="18"
            type="cardCode"
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请输入证件号"
            v-model="formData.cardCode"
          ></fui-input>
        </fui-form-item>
      </fui-form>
    </view>
    <view class="bottom-zone">
      <view class="p30rpx">
        <view class="flex items-center">
          <fui-checkbox-group name="checkbox">
            <view class="fui-list__item">
              <fui-label>
                <view class="flex items-center">
                  <fui-checkbox
                    class="flex items-center"
                    :checked="isAgree"
                    :scaleRatio="0.9"
                    @change="handleAgree"
                  ></fui-checkbox>
                  <view class="pl16rpx text-24rpx text-[#7F7F7F]">已阅读并同意</view>
                </view>
              </fui-label>
            </view>
          </fui-checkbox-group>
          <fui-button color="#FF9900" type="link" :size="24" @click="handleShowAuthNotice">
            <text>《授权须知》</text>
          </fui-button>
        </view>
        <fui-button type="primary" @click="handleSubmit">立即授权</fui-button>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import { grantAuth } from '@/service/app'
import { EventName } from '@/utils/const'
import { showTextToast } from '@/utils/util'

const modeEnv = import.meta.env.VITE_USER_NODE_ENV

let loading = ref(false)

const formRef = ref()

const formData = ref({
  // 成员类型 0：我的伴侣   1我的子女   2  我的父母
  accessRelationType: 1,
  name: '',
  cardCode: '',
})

const options = ref([
  {
    text: '我的伴侣',
    value: 0,
  },
  {
    text: '我的子女',
    value: 1,
  },
  {
    text: '我的父母',
    value: 2,
  },
])

const rules = [
  {
    name: 'name',
    rule: ['required'],
    msg: ['请输入姓名'],
  },
  // 身份证号 生产环境校验真实的身份证号
  modeEnv === 'production'
    ? {
        name: 'cardCode',
        rule: ['required', 'isIdCard'],
        msg: ['请输入身份证号', '请输入正确的身份证号'],
        // rule: ['required'],
        // msg: ['请输入身份证号'],
      }
    : {
        name: 'cardCode',
        rule: ['required'],
        msg: ['请输入身份证号'],
      },
]

const isAgree = ref(false)

const handleAgree = (e: any) => {
  console.log('isAgree', e)
  isAgree.value = e.checked
}

const handleSubmit = () => {
  console.log('handleSubmit')
  if (isAgree.value === false) {
    showTextToast('请先阅读并同意授权须知')
    return
  }
  formRef.value.validator(formData.value, rules).then(async (res: any) => {
    console.log('res', res)
    if (res.isPassed) {
      console.log('formData', formData.value)
      try {
        loading.value = true
        const res = await grantAuth({
          body: {
            name: formData.value.name,
            cardCode: formData.value.cardCode,
            accessRelationType: formData.value.accessRelationType,
          },
        })
        console.log('res', res)
        // uni.$emit(EventName.AUTH_LIST_UPDATE)
        uni.navigateBack()
      } catch (error) {
        console.log('error', error)
      } finally {
        loading.value = false
      }
    } else {
      showTextToast(res.errorMsg)
    }
  })
}

const handleShowAuthNotice = () => {
  uni.navigateTo({ url: '/pages-sub/authNotice/index' })
}
</script>

<style lang="scss" scoped>
.bottom-zone {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
