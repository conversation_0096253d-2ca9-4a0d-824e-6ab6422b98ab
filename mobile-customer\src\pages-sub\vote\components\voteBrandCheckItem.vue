<template>
  <view class="brand-item">
    <view class="flex items-center w-[260rpx]">
      <!-- 排名数字 -->
      <view class="rank-number">
        <fui-checkbox
          v-if="brand.stockStatus !== StockStatusEnum.SELL_OUT"
          :value="brand.name"
          :checked="!!brand.isChecked"
          @change="handleCheckBoxChange"
        ></fui-checkbox>
        <view class="w100% h100% bg-[#eee] rounded-[50%]" v-else></view>
      </view>

      <!-- 品牌LOGO -->
      <view>
        <image
          class="brand-logo"
          :src="brandLogo"
          mode="scaleToFill"
          @click="handleBrandLogoClick"
        />
      </view>
    </view>
    <view class="flex-1">
      <view class="flex items-center">
        <!-- 品牌标签 -->
        <view class="brand-tag orange-tag" :class="brand.tagType">{{ brand.brandName }}</view>
        <!-- 品牌名称 -->
        <view class="brand-name">{{ brand.productName }}</view>
      </view>

      <view class="flex gap-20rpx mb-20rpx">
        <view
          :class="['check-item', sku.checked ? 'check-item-active' : '']"
          v-for="(sku, index) in brand.skus"
          :key="index"
          @click="handleSkuClick(sku)"
        >
          {{ sku.name }}
        </view>
      </view>

      <!-- 投票进度条 -->
      <progress
        :percent="voteRateNumber"
        :stroke-width="12"
        :border-radius="8"
        activeColor="#0073FF"
      />

      <!-- 投票信息 -->
      <view class="vote-info">
        <text>本小区得票数: {{ brand.voteCount }}</text>
        <text>得票率: {{ brand.voteRate }}</text>
      </view>

      <view class="text-[#F69F2A] text-24rpx flex items-center justify-between">
        <text>您需要承担:￥ {{ skuChecked?.price }}</text>
        <view
          class="px12rpx py6rpx rounded-[12rpx]"
          v-if="brand.stockStatus !== null && brand.stockStatus !== undefined"
          :style="{
            backgroundColor: stockStatusMap[brand.stockStatus].bgColor,
            color: stockStatusMap[brand.stockStatus].textColor,
          }"
        >
          {{ stockStatusMap[brand.stockStatus].text }}
        </view>
      </view>
      <view class="flex items-center mt10rpx" v-if="brand.isChecked">
        <view class="text-24rpx mr-20rpx">排序</view>
        <fui-input-number
          class="flex"
          :modelValue="brand.sortOrder"
          @change="changeVoteSort"
          :min="1"
          :max="selectedCount"
          :params="isListenSort"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import failedImg from '@/static/images/failed.jpg'
import { EventName } from '@/utils/const'
import { stockStatusMap, StockStatusEnum } from '../vota.data'

const props = defineProps({
  brand: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
  selectedCount: {
    type: Number,
    default: 1000,
  },
  // 是否监听排序 0：不监听 1：监听
  // 如果不是手动修改排序，则不监听
  // 比如：点击checkbox时，会给sortOrder赋值，则不监听
  isListenSort: {
    type: Number,
    default: 0,
  },
  buildingIds: {
    type: String,
    default: '',
  },
  voteId: {
    type: String,
    default: '',
  },
})

const imgPrefix = import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL

// 维保选中项目 0：5年维保  1：10年维保
const checkItem = ref(0)

const brandLogo = computed(() => {
  console.log('brandLogo', props.brand)

  return props.brand.brandPic ? `${imgPrefix}/${props.brand.brandPic}` : failedImg
})

const skuChecked = computed(() => {
  return props.brand?.skus?.find((item: any) => item.checked) || {}
})

const voteRateNumber = computed(() => {
  // 字符串转换成数字
  const voteRate = props?.brand?.voteRate || '0%'
  return voteRate === '-' ? 0 : parseFloat(voteRate) || 0
})

const emit = defineEmits(['onCheckBoxChange', 'changeVoteSort', 'onSkuClick'])
// 点击checkbox
const handleCheckBoxChange = (e: any) => {
  console.log('点击checkbox', e)
  emit('onCheckBoxChange', e)
}
// 改变投票数量
const changeVoteSort = async (e: any) => {
  console.log('改变投票顺序', e)
  if (e.params === 0) {
    return
  }
  emit('changeVoteSort', props.brand, e.value)
}

const handleSkuClick = (sku: any) => {
  console.log('点击sku', sku)
  sku.checked = !sku.checked
  // 如果当前sku被选中，则将其他sku的checked设置为false
  if (sku.checked) {
    props.brand.skus.forEach((item: any) => {
      if (item.code !== sku.code) {
        item.checked = false
      }
    })
  }
  emit('onSkuClick', sku)
}

const handleBrandLogoClick = () => {
  uni.navigateTo({
    url: `/pages-sub/brandDetail/index?id=${props.brand.productId}&buildingId=${props.buildingIds}&voteId=${props.voteId}&communityId=${props.brand.communityId}&supplierId=${props.brand.supplierId}`,
    success: (res: any) => {
      console.log('success', res)
      // 防止buildingIds太长，所以用eventChannel传值
      //res.eventChannel.emit(EventName.PRODUCT_DETAIL_PAGE, props.buildingIds)
    },
  })
}
</script>

<style lang="scss" scoped>
.brand-item {
  background-color: #fff;
  position: relative;
  padding: 30rpx 20rpx;
  display: flex;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}

.rank-number {
  width: 46rpx;
  height: 46rpx;
}

.rank-1 {
  background-color: #e53935;
}

.rank-2 {
  background-color: #fb8c00;
}

.rank-3 {
  background-color: #43a047;
}

.brand-logo {
  margin-left: 10rpx;
  width: 180rpx;
  height: 140rpx;
}

.brand-name {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  margin-left: 10rpx;
}

.brand-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.blue-tag {
  background-color: #e3f2fd;
  color: #1976d2;
}

.orange-tag {
  background-color: #fff3e0;
  color: #e65100;
}

.vote-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0;
}

.check-item {
  background: #eee;
  color: #000;
  // width: 180rpx;
  padding: 12rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
  &-active {
    background: #fde8cd;
    color: #f69f2a;
  }
}
</style>
