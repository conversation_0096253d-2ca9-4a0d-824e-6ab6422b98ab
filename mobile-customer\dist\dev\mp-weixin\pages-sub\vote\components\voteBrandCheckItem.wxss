/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.brand-item.data-v-13a7e1c6 {
  background-color: #fff;
  position: relative;
  padding: 30rpx 20rpx;
  display: flex;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}
.rank-number.data-v-13a7e1c6 {
  width: 46rpx;
  height: 46rpx;
}
.rank-1.data-v-13a7e1c6 {
  background-color: #e53935;
}
.rank-2.data-v-13a7e1c6 {
  background-color: #fb8c00;
}
.rank-3.data-v-13a7e1c6 {
  background-color: #43a047;
}
.brand-logo.data-v-13a7e1c6 {
  margin-left: 10rpx;
  width: 180rpx;
  height: 140rpx;
}
.brand-name.data-v-13a7e1c6 {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  margin-left: 10rpx;
}
.brand-tag.data-v-13a7e1c6 {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}
.blue-tag.data-v-13a7e1c6 {
  background-color: #e3f2fd;
  color: #1976d2;
}
.orange-tag.data-v-13a7e1c6 {
  background-color: #fff3e0;
  color: #e65100;
}
.vote-info.data-v-13a7e1c6 {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0;
}
.check-item.data-v-13a7e1c6 {
  background: #eee;
  color: #000;
  padding: 12rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
}
.check-item-active.data-v-13a7e1c6 {
  background: #fde8cd;
  color: #f69f2a;
}