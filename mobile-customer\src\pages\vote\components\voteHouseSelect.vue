<template>
  <fui-dropdown-menu
    :options="itemOptions"
    @click="rangeItemClick"
    @close="rangeClose"
    v-bind="dropdownMenuProps"
    ref="ddmRange"
    v-show="itemOptions.length > 0"
  >
    <view class="fui-filter__item" @tap="dropdownMenuClick">
      <fui-overflow-hidden
        class="max-w-90%"
        :size="28"
        fontWeight="bold"
        :text="range.text"
      ></fui-overflow-hidden>
      <view class="fui-filter__icon" :class="{ 'fui-icon__ani': rangeShow }">
        <fui-icon name="turningdown" :size="32"></fui-icon>
      </view>
    </view>
  </fui-dropdown-menu>
</template>

<script lang="ts" setup>
import { queryCommunityList } from '@/service/app'

const props = defineProps({
  dropdownMenu: {
    type: Object,
    default: () => ({}),
  },
})
const _dropdownMenu = {
  maxHeight: '600',
  minWidth: '350',
  padding: '26rpx',
  isMask: true,
  selectedColor: '#007EFF',
  isCheckMark: true,
  checkmarkColor: '#007EFF',
  isReverse: true,
  size: 24,
  left: 0,
}
const dropdownMenuProps = ref({ ..._dropdownMenu, ...props.dropdownMenu })

const itemOptions = ref([])

const ddmRange = ref()

const range = ref({ text: '暂无数据', value: '', buildingId: '' })
const rangeShow = ref(false)

const dropdownMenuClick = () => {
  console.log('dropdownMenuClick')
  ddmRange.value.show()
  rangeShow.value = true
}
const rangeItemClick = (e: any) => {
  console.log('rangeItemClick', e)
  console.log('range.value', range.value)
  // 如果点击的选项和当前选项相同，则不进行任何操作
  if (range.value.buildingId === e.buildingId) {
    rangeClose()

    return
  }
  range.value = e
  emit('houseVoteSelectClick', e)

  // 关闭下拉菜单
  rangeClose()
}

const rangeClose = () => {
  console.log('rangeClose')
  rangeShow.value = false
}

const closeDropdown = () => {
  ddmRange.value.close(2)
  rangeClose()
}

const emit = defineEmits(['houseVoteSelectClick', 'houseVoteResult'])

onMounted(() => {
  rangeClose()
  // fetchCommunityList()
})

// 获取当前用户可投票信息
const fetchCommunityList = async () => {
  console.log('fetchCommunityList')
  try {
    const res: any = await queryCommunityList({})
    console.log('fetchStoreData', res)

    // 1: 已投票且已结束
    // 2: 没有房产参与投票
    // 3: 已结束且未投票
    if (res.result === 1 || res.result === 2 || res.result === 3) {
      itemOptions.value = []
      emit('houseVoteResult', res.result)
      return
    }

    itemOptions.value =
      res?.result?.map((item: any, index: number) => ({
        text: `${item.communityName}${item.buildingNo}幢投票`,
        value: item.id,
        buildingId: item.buildingId,
        buildingNo: item.buildingNo,
        checked: index === 0,
      })) || []

    // itemOptions.value = [
    //   {
    //     text: '南京市鼓楼区',
    //     value: '1',
    //     checked: true,
    //   },
    //   {
    //     text: '南京市玄武区',
    //     value: '3',
    //     checked: false,
    //   },
    // ]

    if (itemOptions.value.length > 0) {
      console.log('emit-storeSelectClick', itemOptions.value[0])

      range.value = itemOptions.value[0]
      emit('houseVoteSelectClick', itemOptions.value[0])
    }
  } catch (error) {
    console.log(error)
  }
}

defineExpose({
  fetchCommunityList,
  closeDropdown,
})
</script>

<style lang="scss" scoped>
.fui-filter__bar {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-bottom: 40rpx;
}

.fui-filter__item {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  /* #ifdef H5 */
  cursor: pointer;
  /* #endif */
}

.fui-filter__icon {
  transition: all 0.15s linear;
}

.fui-icon__ani {
  transform: rotate(180deg);
}

.fui-list__cell {
  width: 100%;
}

.fui-select {
  flex: 1;
  height: 80rpx;
  padding: 32rpx;
  box-sizing: border-box;
  position: relative;
}

.fui-select::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #eee;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
}

.fui-input {
  font-size: 32rpx;
  flex: 1;
  padding-right: 8rpx;
  color: #181818;
  pointer-events: none;
}
</style>
