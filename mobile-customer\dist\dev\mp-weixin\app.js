"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
const store_user = require("./store/user.js");
const interceptors_route = require("./interceptors/route.js");
const interceptors_request = require("./interceptors/request.js");
const interceptors_prototype = require("./interceptors/prototype.js");
if (!Math) {
  "./pages/welcome/index.js";
  "./pages/home/<USER>";
  "./pages/message/index.js";
  "./pages/mine/index.js";
  "./pages/vote/index.js";
  "./pages-sub/authNotice/index.js";
  "./pages-sub/brandDetail/index.js";
  "./pages-sub/installRecord/index.js";
  "./pages-sub/messageDetail/index.js";
  "./pages-sub/myAuth/addAuth.js";
  "./pages-sub/myAuth/applyAuth.js";
  "./pages-sub/myAuth/faceAuth.js";
  "./pages-sub/myAuth/index.js";
  "./pages-sub/myContract/index.js";
  "./pages-sub/myHouse/errorCorrection.js";
  "./pages-sub/myHouse/index.js";
  "./pages-sub/myPay/index.js";
  "./pages-sub/myVote/index.js";
  "./pages-sub/privacyPolicy/index.js";
  "./pages-sub/realname/index.js";
  "./pages-sub/realname/personalAuth.js";
  "./pages-sub/scanCodeVote/index.js";
  "./pages-sub/userAgreement/index.js";
  "./pages-sub/vote/index.js";
  "./pages-sub/votehasEndResultDetail/index.js";
  "./pages-sub/voteNoticeDetail/index.js";
  "./pages-sub/voteNoticeList/index.js";
  "./pages-sub/votePublicDetail/index.js";
  "./pages-sub/voteResultDetail/index.js";
  "./pages-sub/voteResultList/index.js";
  "./pages-sub/voteResultsAdvance/index.js";
  "./pages-sub/voteRules/index.js";
  "./pages-sub/mine/login/index.js";
  "./echarts/index.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    common_vendor.onLaunch(() => {
      console.log("App Launch");
      store_user.useUserStore();
    });
    common_vendor.onShow(() => {
      console.log("App Show");
    });
    common_vendor.onHide(() => {
      console.log("App Hide");
    });
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.use(interceptors_route.routeInterceptor);
  app.use(interceptors_request.requestInterceptor);
  app.use(interceptors_prototype.prototypeInterceptor);
  app.use(common_vendor.VueQueryPlugin);
  app.component("layout-default-uni", Layout_Default_Uni);
  app.component("layout-demo-uni", Layout_Demo_Uni);
  return {
    app
  };
}
const Layout_Default_Uni = () => "./layouts/default.js";
const Layout_Demo_Uni = () => "./layouts/demo.js";
createApp().app.mount("#app");
exports.createApp = createApp;
