<template>
  <view class="h140rpx bg-[#ffffff] px32rpx py20rpx" @click="handleClick">
    <view class="flex justify-between items-center h100%">
      <view class="w-200rpx h140rpx rounded-10rpx">
        <image
          class="w100% h100% rounded-10rpx"
          src="/static/images/dt_001.jpg"
          mode="scaleToFill"
        />
      </view>

      <view class="flex-1 ml10rpx flex flex-col justify-between h90%">
        <view class="text-28rpx text-[#000000] font-bold">{{ content }}</view>
        <view class="text-24rpx text-[#7F7F7F]">{{ item.createTime }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const content = computed(() => {
  // return `${props.item.communityName}${props.item.buildingNo || ''}幢电梯更新开始投票啦！`
  return `${props.item.voteName}开始投票啦！`
})

const handleClick = () => {
  uni.navigateTo({
    url: `/pages-sub/voteNoticeDetail/index?id=${props.item.id}&communityId=${props.item.communityId}`,
  })
}
</script>

<style lang="scss" scoped></style>
