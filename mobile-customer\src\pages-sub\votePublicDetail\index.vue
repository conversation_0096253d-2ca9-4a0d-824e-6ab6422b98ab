<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '公示详情',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="mt20rpx">
      <view class="bg-[#ffffff] p-30rpx">
        <view class="text-32rpx font-bold text-[#333]">
          {{ voteData.communityName }}{{ voteData.buildingNo || '' }}幢电梯更新投票结果公示
        </view>
        <view class="text-26rpx text-[#666] my10rpx">地址: {{ voteData.communityLocation }}</view>
        <view class="text-26rpx text-[#1296db] mb10rpx">
          投票结束日期: {{ voteData.voteEndDate }}
        </view>

        <!-- 投票进度条 -->
        <view class="">
          <progress
            :percent="voteData.voteRateValue"
            :stroke-width="15"
            :border-radius="15"
            activeColor="#0ABF4F"
          />
          <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
            <text>小区投票数: {{ voteData.voteCount }}</text>
            <text>投票率: {{ voteData.voteRate }}</text>
          </view>
        </view>
      </view>

      <view class="mt20rpx">
        <view>
          <title-header title="获选品牌" />

          <view class="px-30rpx bg-[#ffffff]">
            <win-brand class="" :brand="winBrand" />
          </view>
        </view>
        <view>
          <title-header title="投票情况" />
          <buildings-vote :voteBuildingsList="buildVoteList" />
        </view>

        <vote-brand-rank :brands="voteResultRank" :show-price="false" :show-more-sku="true" />
      </view>
    </view>
    <fui-loading v-if="loading" isMask></fui-loading>
  </view>
</template>

<script lang="ts" setup>
import TitleHeader from '@/components/common/titleHeader.vue'
import voteBrandRank from '@/pages-sub/votehasEndResultDetail/components/voteBrandRank.vue'
import WinBrand from './components/winBrand.vue'
import BuildingsVote from './components/buildingsVote.vue'

import {
  getBuildingRateByVote,
  getPrePrice,
  getVoteInfoByCurUser,
  getVoteRankProductTop10,
  queryBizVoteInfoById,
} from '@/service/app'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { calculatePrePrice } from '@/utils/util'

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const tabs = ref(['投票结果', '得票排名', '我的投票'])

const loading = ref(false)

const voteData: any = ref({})

const voteResultRank = ref([])
const myVoteRank = ref([])

// 当前选中的tab
const currentTab = ref(0)

const id = ref('')

const winBrand = ref({})
// 获取投票详情
const fetchVoteDetail = async () => {
  try {
    loading.value = true
    const res: any = await queryBizVoteInfoById({
      params: {
        id: id.value,
      },
    })
    console.log('fetchVoteDetail', res)
    voteData.value = {
      ...res.result,
      voteRateValue: parseFloat(res.result.voteRate.replace('%', '')),
    }
    winBrand.value = {
      vote_rate: res.result.voteRateProduct,
      vote_count: res.result.voteCountProduct,
      brand_name: res.result.resultBrandName,
      brandLogo: res.result.resultBrandPic,
      skuCode_dictText: res.result.skuCode_dictText,
      product_name: res.result.resultProductName,
      pic_url: res.result.resultBrandPic,
      productModel: res.result.productModel,
      productId: res.result.productId,
      price: res.result.price,
    }
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 投票结果品牌排名
const fetchVoteResultRank = async () => {
  try {
    const res: any = await getVoteRankProductTop10({
      params: {
        voteId: id.value,
        unVoted: 1,
      },
    })
    console.log('fetchVoteResultRank', res)
    voteResultRank.value =
      res.result?.map((item) => ({
        ...item,
        skuName: item.win_sku || '5年维保',
        showMoreSku: false,
      })) || []
  } catch (error) {
    console.log('error', error)
  }
}

// 获取我的投票
const fetchMyVoteRank = async () => {
  try {
    const res: any = await getVoteInfoByCurUser({
      params: {
        voteId: id.value,
      },
    })
    console.log('fetchMyVote', res)
    myVoteRank.value =
      res.result?.map((item) => ({
        ...item,
        vote_rate: item.voteRate,
        vote_count: item.voteCount,
        brand_name: item.brandName,
        product_name: item.productName,
        pic_url: item.picUrl,
      })) || []
  } catch (error) {
    console.log('error', error)
  }
}

// 获取预估价格参数
const formulaParams = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async () => {
  try {
    const res: any = await getPrePrice({
      params: {
        communityId: voteData.value.communityId,
        buildingIds: voteData.value.buildingCodes,
      },
    })
    formulaParams.value = res.result
  } catch (error) {
    console.log('error', error)
  }
}

// 获取栋投票数及投票率
const buildVoteList = ref([])
const fetchBuildingsRateByVote = async () => {
  try {
    loading.value = true
    const res: any = await getBuildingRateByVote({
      params: {
        voteId: id.value,
      },
    })
    if (res.result && res.result.length > 0) {
      buildVoteList.value = res.result.map((item) => ({
        ...item,
        voteRateValue: parseFloat(item.voteRate),
        voteAreaRateValue: parseFloat(item.voteAreaRate),
      }))
    } else {
      buildVoteList.value = []
    }
  } catch (error) {
    console.log('error', error)
    buildVoteList.value = []
  } finally {
    loading.value = false
  }
}

onLoad(async (options: { id: string }) => {
  id.value = options.id
  // 获取投票详细信息
  await fetchVoteDetail()
  // 获取价格计算参数
  // await fetchPrePrice()
  fetchBuildingsRateByVote()
  // fetchVoteResult()
  fetchVoteResultRank()
  // fetchMyVoteRank()
})
</script>

<style lang="scss" scoped>
.bottom-zone {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
