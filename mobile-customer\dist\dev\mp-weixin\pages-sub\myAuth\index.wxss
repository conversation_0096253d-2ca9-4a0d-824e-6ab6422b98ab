/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bottom-zone.data-v-e46a49ed {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.auth-list.data-v-e46a49ed {
  padding: 20rpx;
  margin-bottom: 160rpx;
}
.auth-item.data-v-e46a49ed {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  padding: 20rpx;
}
.auth-header.data-v-e46a49ed {
  display: flex;
  justify-content: space-between;
}
.relation.data-v-e46a49ed {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.auth-status.data-v-e46a49ed {
  font-size: 24rpx;
  color: #2196f3;
}
.auth-status-active.data-v-e46a49ed {
  color: #ff9800;
}
.auth-content.data-v-e46a49ed {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}
.auth-info.data-v-e46a49ed {
  flex: 1;
}
.info-row.data-v-e46a49ed {
  display: flex;
  margin-bottom: 10rpx;
}
.info-label.data-v-e46a49ed {
  font-size: 26rpx;
  color: #666;
}
.info-value.data-v-e46a49ed {
  font-size: 26rpx;
  color: #333;
}
.auth-action.data-v-e46a49ed {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.btn-cancel.data-v-e46a49ed {
  font-size: 24rpx;
  color: #2196f3;
  background-color: #e3f2fd;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  height: 60rpx;
  line-height: 40rpx;
}
.btn-cancel-active.data-v-e46a49ed {
  color: #ff9800;
  background-color: #fff3e0;
}