<template>
  <view class="bg-[#ffffff]" @click="handleClick">
    <view class="flex justify-between items-center h100%">
      <view class="w-260rpx self-stretch">
        <image class="w100% h100%" src="https://dummyimage.com/200x140" mode="aspectFill" />
      </view>

      <view class="flex-1 flex flex-col justify-between p20rpx gap-y-10rpx">
        <view class="text-28rpx">
          {{ item.communityName }}{{ item.buildingNo ? `栋${item.buildingNo}` : '' }}
        </view>
        <view class="row-zone">
          <view>安装完成时间{{ item.totalCount }}</view>
        </view>
        <view class="row-zone">
          <view>型号：{{ item.voteTime }}</view>
        </view>
        <view class="row-zone">
          <view>编码：{{ item.voteTime }}</view>
        </view>

        <view class="flex">
          <fui-tag type="success" text="交付使用" :padding="['8rpx', '16rpx']" v-if="!item.end" />
          <fui-tag
            type="success"
            background="#aaaaaa"
            :padding="['8rpx', '16rpx']"
            color="#fff"
            text="电梯出厂"
            v-else
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const handleClick = () => {}
</script>

<style lang="scss" scoped>
.row-zone {
  font-size: 24rpx;
}
</style>
