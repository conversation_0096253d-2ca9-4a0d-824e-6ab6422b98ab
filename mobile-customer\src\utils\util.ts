import { useUserStore } from '@/store'
import dayjs from 'dayjs'
import { getEnvBaseUrl } from '.'

export const showTextToast = (title: string) => {
  return new Promise((resolve, reject) => {
    uni.showToast({
      title,
      icon: 'none',
      mask: true,
      duration: 2000,
      complete: () => {
        resolve(true)
      },
    })
  })
}
export const showLoading = (title: string) => {
  uni.showLoading({
    title,
    mask: true,
  })
}
export const hideLoading = () => {
  uni.hideLoading()
}

export const checkIsLogin = (
  jumpToLogin: boolean = false,
  isShowToast: boolean = true,
): boolean => {
  const userStore = useUserStore()
  const isLogined = userStore.isLogined
  if (!isLogined) {
    if (jumpToLogin) {
      uni.navigateTo({
        url: `/pages/login/index`,
      })
    }
    if (isShowToast) {
      showTextToast('请先登录!')
    }
    return false
  }
  return true
}
export const timestamp = () => +Date.now()

/**
 * Promise封装的定时器，可以在定时结束后自动清除
 * @param time 定时时间(毫秒)
 * @returns Promise，在指定时间后resolve
 */
export const delay = (time: number): Promise<void> => {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      clearTimeout(timer) // 清除定时器
      resolve()
    }, time)
  })
}

// 判断是否具有某个角色
// STORE_LEADER: 店长
// MAINTENANCE :维修人员
// DEPT_LEADER :部门经理
// MANAGER :租房管家
// GY_ADMIN 业务管理员
// PR_SA :超级管理员
// 多个角色使用','分隔
export const hasRole = (role: string) => {
  const userStore = useUserStore()
  const roleList = userStore.userInfo?.roleList

  const _roleArr = role.split(',')

  return roleList?.some((item) => _roleArr.includes(item.code)) || false
}

export const showModal = async (
  title: string,
  content: string,
  confirmColor: string = '#007EFF',
) => {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title,
      content,
      confirmColor,
      success: function (res) {
        if (res.confirm) {
          resolve(true)
        } else {
          reject(new Error('用户点击取消'))
        }
      },
    })
  })
}

// 根据水电表获取单位
export const getShuiDianUnit = (name: string) => {
  console.log(name)
  let unit = ''
  switch (name) {
    case '电表':
      unit = '度'
      break
    case '冷水表':
      unit = '吨'
      break
    case '热水表':
      unit = '吨'
      break
    case '暖气表':
      unit = 'm³'
      break
  }
  return unit
}

/**
 *  Format the money to a string with the specified decimal places and add a minus sign if it is negative
 *  @param {number} money - The number to be formatted
 *  @param {number} [fix=2] - The number of decimal places to keep
 *  @param {boolean} [isSeparate=false] - Whether to separate the thousands place with a comma
 *  @returns {string} - The formatted string
 */
export const moneyFormatter = (money: number, fix: number = 2, isSeparate: boolean = false) => {
  if (!money) return '--'
  if (isNaN(money)) return '--'
  // 如果money.toString()小于0，则取绝对值
  const isNegative = (money || 0).toString().includes('-')

  if (isNegative) {
    money = Math.abs(money)
  }
  const resultFixed = parseFloat((money || 0).toString())
    .toFixed(fix)
    .toString()

  const result = isSeparate
    ? resultFixed
        .split('')
        .reverse()
        .join('')
        .replace(/(\d{3})/g, '$1,')
        .replace(/\,$/, '')
        .split('')
        .reverse()
        .join('')
    : resultFixed
  return isNegative ? `-${result}` : result
}

/**
 * 计算投票结束时间的剩余秒数
 * @param voteEndDate 投票结束日期
 * @returns 剩余秒数
 */
export const calculateVoteEndSeconds = (voteEndDate: string): number => {
  try {
    const voteEndDateTime = dayjs(voteEndDate).format('YYYY-MM-DD 23:59:59')
    const remainingSeconds = dayjs(voteEndDateTime).diff(dayjs(), 'second')

    console.log('dayjs---剩余秒数:', remainingSeconds)

    return remainingSeconds > 0 ? remainingSeconds : 0
  } catch (error) {
    // 解析投票结束日期
    const endDate = new Date(voteEndDate)

    // 设置为当天的23:59:59
    endDate.setHours(23, 59, 59, 999)

    // 获取当前时间
    const now = new Date()

    // 计算时间差（毫秒）
    const diffMilliseconds = endDate.getTime() - now.getTime()

    // 转换为秒
    const remainingSeconds = Math.floor(diffMilliseconds / 1000)

    console.log('剩余秒数:22222', remainingSeconds)

    return remainingSeconds > 0 ? remainingSeconds : 0
  }
}

// 获取图片地址
export const getImageUrl = (token: string, fileName: string) => {
  console.log('token', token)
  console.log('fileName', fileName)
  const _apiPrefix = 'minio/getObjectByStream'
  const _baseUrl = getEnvBaseUrl()
  return `${_baseUrl}/${_apiPrefix}?token=${token}&fileName=${fileName}`
}

// 计算预估费用 formulaParams是一个数组

export const calculatePrePrice = (
  productPrice: number | string,
  formulaParams: Array<any> | { reduction: number; rate: number; total?: number } = [],
  recycleValue: number = 0,
) => {
  console.log('productPrice', productPrice)
  console.log('formulaParams', formulaParams)
  if (typeof productPrice === 'string') {
    productPrice = parseFloat(productPrice)
  }

  const _productPrice = productPrice - (recycleValue || 0)

  let allTotal: number = 0
  // formulaParams 遍历 计算
  if (Array.isArray(formulaParams)) {
    for (let i = 0; i < formulaParams.length; i++) {
      const { reduction, rate } = formulaParams[i]
      allTotal = allTotal + (_productPrice - reduction) * rate
    }
  } else {
    return 0
  }

  // 向上取整 2.333取2.34
  return allTotal.toFixed(2)
}
