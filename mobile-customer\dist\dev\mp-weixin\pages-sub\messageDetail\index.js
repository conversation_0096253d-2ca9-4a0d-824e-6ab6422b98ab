"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
require("../../store/index.js");
const utils_index = require("../../utils/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_upload2 = common_vendor.resolveComponent("fui-upload");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_upload2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_upload = () => "../../components/firstui/fui-upload/fui-upload.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_upload + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const userStore = store_user.useUserStore();
    const { userInfo } = common_vendor.storeToRefs(userStore);
    const baseUrl = utils_index.getEnvBaseUrl();
    const fileStreamPath = "/s3/getObjectByStream";
    const fileList = common_vendor.ref([]);
    const fileReplyList = common_vendor.ref([]);
    const messageDetail = common_vendor.ref({});
    const getMessageDetail = (id) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizErrorHouseDetail({
          params: {
            id
          }
        });
        messageDetail.value = res.result || {};
        if (messageDetail.value.imgUrl) {
          fileList.value = messageDetail.value.imgUrl.split(",").map(
            (item) => `${baseUrl}${fileStreamPath}?fileName=${item}&token=${userInfo.value.token}`
          );
        }
        if (messageDetail.value.imgUrlReply) {
          fileReplyList.value = messageDetail.value.imgUrlReply.split(",").map(
            (item) => `${baseUrl}${fileStreamPath}?fileName=${item}&token=${userInfo.value.token}`
          );
        }
        console.log("fileList", fileList.value);
        console.log("fileReplyList", fileReplyList.value);
      } catch (error) {
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((options) => __async(this, null, function* () {
      console.log("options", options);
      getMessageDetail(options.id);
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(messageDetail).remark
      }, common_vendor.unref(messageDetail).remark ? {
        b: common_vendor.t(common_vendor.unref(messageDetail).createTime),
        c: common_vendor.t(common_vendor.unref(messageDetail).remark),
        d: common_vendor.p({
          isAdd: false,
          isDel: false,
          radius: "8",
          fileList: common_vendor.unref(fileList),
          width: "160",
          height: "160"
        })
      } : {}, {
        e: common_vendor.unref(messageDetail).remarkReply
      }, common_vendor.unref(messageDetail).remarkReply ? {
        f: common_vendor.t(common_vendor.unref(messageDetail).updateTime),
        g: common_vendor.t(common_vendor.unref(messageDetail).updateBy),
        h: common_vendor.t(common_vendor.unref(messageDetail).remarkReply),
        i: common_vendor.p({
          isAdd: false,
          isDel: false,
          radius: "8",
          fileList: common_vendor.unref(fileReplyList),
          width: "160",
          height: "160"
        })
      } : {}, {
        j: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        k: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
wx.createPage(_sfc_main);
