<template>
  <view class="container">
    <!-- 协议/公告卡片 -->
    <view class="agreement-card">
      <!-- 标题 -->
      <view class="title-section">
        <view class="flex-1">
          <text class="agreement-title">{{ item.voteName }}电梯采购协议</text>
        </view>
        <fui-tag
          v-if="item.status === 1"
          text="已签约"
          theme="plain"
          type="success"
          :padding="['8rpx', '16rpx']"
        ></fui-tag>

        <fui-tag
          v-else
          text="待签约"
          theme="plain"
          type="danger"
          :padding="['8rpx', '16rpx']"
        ></fui-tag>
      </view>

      <!-- 协议信息 -->
      <view class="info-section">
        <!-- 发起方 -->
        <view class="info-row">
          <text class="info-label">发起方：</text>
          <text class="info-value">{{ item.communityName }}全体业主</text>
        </view>

        <!-- 签署方 -->
        <view class="info-row">
          <text class="info-label">签署方：</text>
          <text class="info-value">{{ item.partyC }}/{{ item.supplierName }}</text>
        </view>

        <!-- 签署截止日期 -->
        <view class="flex items-center justify-between">
          <view class="flex items-start">
            <text class="info-label">签署截止日期：</text>
            <text class="info-value">{{ item.signDeadline }}</text>
          </view>
          <view class="action-section">
            <fui-button
              width="120rpx"
              height="56rpx"
              :size="24"
              background="#00BFFF"
              text="去查看"
              @click="handleViewSign"
            />
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['viewSign'])

// 去签署
const handleViewSign = () => {
  emit('viewSign', props.item)
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
}

.agreement-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx 24rpx 1rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

// 标题区域
.title-section {
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agreement-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
}

// 信息区域
.info-section {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
  line-height: 1.4;
}
</style>
