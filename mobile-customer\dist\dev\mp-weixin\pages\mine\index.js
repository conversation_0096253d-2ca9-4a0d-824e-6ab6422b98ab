"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const service_app_miniProgramLogin = require("../../service/app/miniProgramLogin.js");
require("../../utils/http.js");
require("../../store/index.js");
const utils_util = require("../../utils/util.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_list_cell2 = common_vendor.resolveComponent("fui-list-cell");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_list_cell2 + _easycom_fui_button2 + _component_layout_default_uni)();
}
const _easycom_fui_list_cell = () => "../../components/firstui/fui-list-cell/fui-list-cell.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  (userInfoItem + _easycom_fui_list_cell + _easycom_fui_button)();
}
const userInfoItem = () => "./components/userInfo.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const listPadding = ["22rpx", "12rpx"];
    const handleMyHouse = () => {
      if (!isLogined.value) {
        utils_util.showTextToast("请先登录");
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages-sub/myHouse/index"
      });
    };
    const handleMyVote = () => {
      if (!isLogined.value) {
        utils_util.showTextToast("请先登录");
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages-sub/myVote/index"
      });
    };
    const handleMyAuth = () => {
      var _a;
      if (!isLogined.value) {
        utils_util.showTextToast("请先登录");
        return;
      }
      if (((_a = userInfo.value) == null ? void 0 : _a.isRealAuth) !== 1) {
        utils_util.showTextToast("请先实名认证");
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages-sub/myAuth/index"
      });
    };
    const handleOther = () => {
      utils_util.showTextToast("暂未开放，敬请期待");
    };
    const handleInstallRecord = () => {
      utils_util.showTextToast("暂未开放，敬请期待");
      return;
    };
    const handleMyContract = () => {
      if (!isLogined.value) {
        utils_util.showTextToast("请先登录");
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages-sub/myContract/index"
      });
    };
    const handleMyPay = () => {
      utils_util.showTextToast("暂未开放，敬请期待");
      return;
    };
    const getUserInfo = () => __async(this, null, function* () {
      userStore.checkUserInfo();
    });
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定退出登录吗？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            console.log("res", res);
            try {
              const res2 = yield service_app_miniProgramLogin.logout({});
              console.log("res", res2);
              userStore.clearUserInfo();
            } catch (error) {
              console.log("error", error);
            }
          }
        })
      });
    };
    common_vendor.onShow(() => {
      console.log("onShow");
      getUserInfo();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$3,
        b: common_vendor.p({
          userInfo: common_vendor.unref(userInfo),
          isLogined: common_vendor.unref(isLogined)
        }),
        c: common_assets._imports_1$2,
        d: common_vendor.o(handleMyHouse),
        e: common_vendor.p({
          arrow: true,
          padding: listPadding
        }),
        f: common_assets._imports_2$1,
        g: common_vendor.o(handleMyVote),
        h: common_vendor.p({
          arrow: true,
          padding: listPadding
        }),
        i: common_assets._imports_3$1,
        j: common_vendor.o(handleMyAuth),
        k: common_vendor.p({
          arrow: true,
          padding: listPadding
        })
      }, common_vendor.e({
        l: common_assets._imports_4$1,
        m: common_vendor.o(handleInstallRecord),
        n: common_vendor.p({
          arrow: true,
          padding: listPadding
        }),
        o: common_assets._imports_5$1,
        p: common_vendor.o(handleOther),
        q: common_vendor.p({
          arrow: true,
          padding: listPadding
        }),
        r: common_assets._imports_3$1,
        s: common_vendor.o(handleMyContract),
        t: common_vendor.p({
          arrow: true,
          padding: listPadding
        }),
        v: common_assets._imports_6$1,
        w: common_vendor.o(handleMyPay),
        x: common_vendor.p({
          arrow: true,
          padding: listPadding
        })
      }, {}), {
        B: common_vendor.unref(isLogined)
      }, common_vendor.unref(isLogined) ? {
        C: common_vendor.o(handleLogout),
        D: common_vendor.p({
          btnSize: "medium",
          text: "退出登录"
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9023ef44"]]);
wx.createPage(MiniProgramPage);
