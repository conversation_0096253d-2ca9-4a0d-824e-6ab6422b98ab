"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../store/index.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_util = require("../../utils/util.js");
const pages_vote_data = require("./data.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  const _easycom_fui_tabs2 = common_vendor.resolveComponent("fui-tabs");
  const _easycom_fui_count_down2 = common_vendor.resolveComponent("fui-count-down");
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  const _easycom_fui_fab2 = common_vendor.resolveComponent("fui-fab");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_landscape2 = common_vendor.resolveComponent("fui-landscape");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _easycom_fui_tag2 + _easycom_fui_tabs2 + _easycom_fui_count_down2 + _easycom_fui_icon2 + _easycom_fui_fab2 + _easycom_fui_loading2 + _easycom_fui_landscape2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_tag = () => "../../components/firstui/fui-tag/fui-tag.js";
const _easycom_fui_tabs = () => "../../components/firstui/fui-tabs/fui-tabs.js";
const _easycom_fui_count_down = () => "../../components/firstui/fui-count-down/fui-count-down.js";
const _easycom_fui_icon = () => "../../components/firstui/fui-icon/fui-icon.js";
const _easycom_fui_fab = () => "../../components/firstui/fui-fab/fui-fab.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_landscape = () => "../../components/firstui/fui-landscape/fui-landscape.js";
if (!Math) {
  (_easycom_fui_button + resultTitle + VoteHouseSelect + _easycom_fui_tag + _easycom_fui_tabs + _easycom_fui_count_down + TitleHeader + baseChart + _easycom_fui_icon + _easycom_fui_fab + _easycom_fui_loading + VoteNotice + _easycom_fui_landscape)();
}
const TitleHeader = () => "../../components/common/titleHeader.js";
const baseChart = () => "../../echarts/components/BaseCharts.js";
const resultTitle = () => "./components/resultTip.js";
const VoteHouseSelect = () => "./components/voteHouseSelect.js";
const VoteNotice = () => "./components/voteNotice.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    common_vendor.ref(2);
    const voteData = common_vendor.ref(null);
    const voteHouseSelectRef = common_vendor.ref(null);
    const handleLoginClick = () => {
      const redirectUrl = `/pages/vote/index`;
      common_vendor.index.navigateTo({
        url: `/pages-sub/mine/login/index?redirectUrl=${redirectUrl}`
      });
    };
    const handleCertificationClick = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/realname/index"
      });
    };
    const fabClick = () => {
      if (!voteData.value) {
        common_vendor.index.navigateTo({
          url: "/pages-sub/myHouse/errorCorrection"
        });
      } else {
        common_vendor.index.navigateTo({
          url: "/pages-sub/myHouse/index"
        });
      }
    };
    const disableBtn = common_vendor.ref(true);
    const showModal = common_vendor.ref(false);
    const closeModal = () => {
      disableBtn.value = true;
      showModal.value = false;
      clearTimeout();
    };
    const handleVoteNoticeOperation = () => {
      voteNoticeOperation(1);
      closeModal();
    };
    const handleExitVote = () => {
      voteNoticeOperation(0);
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    };
    let timeInterval = null;
    const timeCount = "1";
    const timeNum = common_vendor.ref(0);
    const startTimeInterval = (time = timeCount) => {
      if (timeInterval) {
        clearTimeout();
      }
      timeNum.value = time;
      timeInterval = setInterval(() => {
        timeNum.value = timeNum.value - 1;
        if (timeNum.value <= 0) {
          clearTimeout();
          disableBtn.value = false;
        }
      }, 1e3);
    };
    const clearTimeout = () => {
      if (timeInterval) {
        clearInterval(timeInterval);
        timeInterval = null;
        timeNum.value = 0;
      }
    };
    const top10BrandOption = common_vendor.ref(pages_vote_data.setTop10BrandChartOption([]));
    const top10ElevatorOption = common_vendor.ref(pages_vote_data.setTop10ElevatorChartOption([]));
    const govProcessData = common_vendor.ref({});
    const govProcessOption = common_vendor.ref(
      pages_vote_data.setGovProcessChartOption([
        {
          name: "",
          value: 0
        }
      ])
    );
    const handleVoteStepOne = () => {
      voteData.value.voteEndDate;
      if (voteData.value.status === 2) {
        common_vendor.index.navigateTo({
          url: `/pages-sub/voteResultsAdvance/index?id=${id.value}`
        });
      } else {
        if (voteData.value.voted === true) {
          common_vendor.index.navigateTo({
            url: `/pages-sub/voteResultDetail/index?id=${id.value}&buildingId=${buildingId.value}`
          });
        } else {
          common_vendor.index.navigateTo({
            url: `/pages-sub/vote/index?id=${id.value}&buildingId=${buildingId.value}`
          });
        }
      }
    };
    const id = common_vendor.ref("");
    const buildingId = common_vendor.ref("");
    const communityId = common_vendor.ref("");
    const fetchVoteInfo = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value,
            buildingId: buildingId.value
          }
        });
        console.log("queryBizVoteInfoById", res);
        const voteEndSeconds = utils_util.calculateVoteEndSeconds(res.result.voteEndDate);
        voteData.value = __spreadProps(__spreadValues({}, res.result), {
          voteEndSeconds,
          voteRateValue: parseFloat(res.result.voteRate),
          voteAreaRateValue: parseFloat(res.result.voteAreaRate)
        });
        if (res.result.communityId) {
          communityId.value = res.result.communityId;
          fetchTop10Brand();
          fetchAddressList();
        }
        fetchNanJingTop10();
        fetchGovProcess();
        fetchHasClickVoteNote();
      } catch (error) {
        console.log("error", error);
        voteData.value = null;
      } finally {
        loading.value = false;
      }
    });
    const top10BrandData = common_vendor.ref([]);
    const fetchTop10Brand = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getVoteRankProductTop10({
          params: {
            voteId: id.value
          }
        });
        top10BrandData.value = res.result || [];
        top10ElevatorOption.value = pages_vote_data.setTop10ElevatorChartOption(res.result || []);
        console.log("queryTop10Brand", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const nanJingTop10Data = common_vendor.ref([]);
    const nanJingTop10OChartHeight = common_vendor.ref(120);
    const fetchNanJingTop10 = () => __async(this, null, function* () {
      var _a;
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBrandTop10({
          params: {}
        });
        nanJingTop10OChartHeight.value = ((_a = res.result.length) != null ? _a : 0) * 120 + 40;
        nanJingTop10Data.value = res.result || [];
        top10BrandOption.value = pages_vote_data.setTop10BrandChartOption(res.result || []);
        console.log("queryTop10Brand", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchGovProcess = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryGovProcess({
          params: {}
        });
        console.log("queryGovProcess", res);
        govProcessData.value = res.result;
        if (!res.result.usedPercentage || res.result.usedPercentage === "-") {
          govProcessData.value.usedPercentage = 0;
        }
        const usedPercentage = Number(govProcessData.value.usedPercentage.replace("%", ""));
        govProcessOption.value = pages_vote_data.setGovProcessChartOption([
          {
            name: `国补已使用${usedPercentage}%`,
            value: usedPercentage
          }
        ]);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const allAddressList = common_vendor.ref([]);
    common_vendor.computed(() => {
      var _a, _b;
      return (_b = (_a = allAddressList.value) == null ? void 0 : _a.map((c) => {
        return __spreadProps(__spreadValues({}, c), {
          name: `${c.buildingNo}幢`
        });
      })) == null ? void 0 : _b.filter((item, index, self) => {
        return index === self.findIndex((t) => t.buildingId === item.buildingId);
      });
    });
    const fetchAddressList = () => __async(this, null, function* () {
      const res = yield service_app_vote.getAllGrantedAndSelfList({
        params: {
          communityId: communityId.value,
          buildingIds: buildingId.value
        }
      });
      allAddressList.value = res.result;
      if (res.result.length > 0)
        ;
    });
    const buildingListNew = common_vendor.ref([]);
    common_vendor.ref({});
    const handleHouseVoteSelectClick = (select) => {
      buildingListNew.value = [];
      console.log("handleHouseVoteSelectClick", select);
      id.value = select.value;
      buildingId.value = select.buildingId;
      buildingListNew.value.push({
        name: `${(select == null ? void 0 : select.buildingNo) || "-"}幢`,
        value: select == null ? void 0 : select.buildingId
      });
      fetchVoteInfo();
    };
    const houseVoteResult = common_vendor.ref(0);
    const handleHouseVoteResult = (result) => {
      console.log("handleHouseVoteResult", result);
      houseVoteResult.value = result;
    };
    const voteNoticeOperation = (_operationType) => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.addVoteNoticeOperation({
          body: {
            operationType: _operationType,
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
      } catch (error) {
        console.log("error", error);
      }
    });
    const fetchHasClickVoteNote = () => __async(this, null, function* () {
      var _a;
      try {
        const res = yield service_app_vote.getHasClickVoteNote({
          params: {
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
        if (((_a = res.result) == null ? void 0 : _a.length) === 0) {
          showModal.value = true;
          yield common_vendor.nextTick$1();
          startTimeInterval();
        } else {
          showModal.value = false;
        }
      } catch (error) {
        console.log("error", error);
      }
    });
    const fetchUserInfo = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      const res = yield userStore.checkUserInfo();
      console.log("fetchUserInfo", (_a = res.result) == null ? void 0 : _a.isRealAuth);
      yield common_vendor.nextTick$1();
      (_b = voteHouseSelectRef.value) == null ? void 0 : _b.closeDropdown();
      if (((_c = res.result) == null ? void 0 : _c.isRealAuth) === 1) {
        (_d = voteHouseSelectRef.value) == null ? void 0 : _d.fetchCommunityList();
      }
    });
    const handleJumpMyVote = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/myVote/index"
      });
    };
    const handleJumpMessage = () => {
      common_vendor.index.switchTab({
        url: "/pages/message/index"
      });
    };
    common_vendor.onHide(() => {
      showModal.value = false;
      clearTimeout();
    });
    common_vendor.onShow(() => __async(this, null, function* () {
      console.log("onShow", userInfo.value.isRealAuth);
      id.value = "";
      voteData.value = null;
      communityId.value = "";
      buildingListNew.value = [];
      clearTimeout();
      if (isLogined.value) {
        yield fetchUserInfo();
      }
      yield common_vendor.nextTick$1();
    }));
    common_vendor.onLoad(() => __async(this, null, function* () {
      clearTimeout();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !common_vendor.unref(isLogined)
      }, !common_vendor.unref(isLogined) ? {
        b: common_vendor.o(handleLoginClick),
        c: common_vendor.p({
          ["btn-size"]: "medium",
          text: "立即登录",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        })
      } : !common_vendor.unref(userInfo).isRealAuth ? {
        e: common_vendor.o(handleCertificationClick),
        f: common_vendor.p({
          ["btn-size"]: "medium",
          text: "立即认证",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        }),
        g: common_vendor.p({
          imgUrl: "/static/images/dt_010_2.png"
        })
      } : {}, {
        d: !common_vendor.unref(userInfo).isRealAuth,
        h: common_vendor.sr(voteHouseSelectRef, "d383e1a4-5,d383e1a4-0", {
          "k": "voteHouseSelectRef"
        }),
        i: common_vendor.o(handleHouseVoteSelectClick),
        j: common_vendor.o(handleHouseVoteResult),
        k: !voteData.value
      }, !voteData.value ? common_vendor.e({
        l: houseVoteResult.value === 1
      }, houseVoteResult.value === 1 ? {
        m: common_vendor.o(handleJumpMyVote),
        n: common_vendor.p({
          ["btn-size"]: "medium",
          text: "查看投票",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        }),
        o: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : houseVoteResult.value === 2 ? {
        q: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : houseVoteResult.value === 3 ? {
        s: common_vendor.o(handleJumpMessage),
        t: common_vendor.p({
          ["btn-size"]: "medium",
          text: "查看消息",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        }),
        v: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : {}, {
        p: houseVoteResult.value === 2,
        r: houseVoteResult.value === 3
      }) : common_vendor.e({
        w: common_vendor.t(common_vendor.unref(userInfo).realname),
        x: common_vendor.t(voteData.value.communityName),
        y: common_vendor.t(voteData.value.buildingNo),
        z: common_vendor.f(allAddressList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.location),
            b: item.myself == 1
          }, item.myself == 1 ? {
            c: "d383e1a4-11-" + i0 + ",d383e1a4-0",
            d: common_vendor.p({
              text: "被授权",
              type: "success",
              padding: ["6rpx", "6rpx"]
            })
          } : {}, {
            e: index
          });
        }),
        A: common_vendor.t(voteData.value.voteEndDate),
        B: common_vendor.p({
          tabs: buildingListNew.value,
          center: true,
          selectedColor: "#005ED1",
          sliderBackground: "#005ED1"
        }),
        C: voteData.value.voteRateValue,
        D: common_vendor.t(voteData.value.voteCount),
        E: common_vendor.t(voteData.value.voteRate),
        F: voteData.value.voteAreaRateValue,
        G: common_vendor.t(voteData.value.voteArea),
        H: common_vendor.t(voteData.value.voteAreaRate),
        I: voteData.value.id
      }, voteData.value.id ? {
        J: common_vendor.t(!voteData.value.pass ? "本楼栋暂未达到有效选票数，请尽快投票。" : "本楼栋已达到有效选票数。")
      } : {}, {
        K: common_vendor.p({
          isDays: true,
          isColon: false,
          size: "42",
          width: "50",
          height: "50",
          borderColor: "transparent",
          background: "transparent",
          color: "#005ED1",
          colonColor: "#005ED1",
          value: voteData.value.voteEndSeconds
        }),
        L: common_vendor.p({
          title: "南京市电梯更新采购品牌前10名"
        }),
        M: common_vendor.p({
          options: top10BrandOption.value
        }),
        N: nanJingTop10OChartHeight.value + "rpx",
        O: nanJingTop10Data.value.length > 0,
        P: common_vendor.p({
          title: "本小区电梯更新选择前10名"
        }),
        Q: common_vendor.p({
          options: top10ElevatorOption.value
        }),
        R: top10BrandData.value.length > 0,
        S: common_vendor.p({
          title: "南京市老旧电梯更新国补进程"
        }),
        T: common_vendor.p({
          options: govProcessOption.value
        }),
        U: common_vendor.t(govProcessData.value.nationalSubsidyQuota || "-"),
        V: common_vendor.t(govProcessData.value.usedSubsidyQuota || "-"),
        W: common_vendor.t(govProcessData.value.subsidyPerElevator || "-"),
        X: common_vendor.o(handleVoteStepOne),
        Y: common_vendor.p({
          width: "100%",
          radius: "100rpx",
          height: "84rpx",
          ["border-width"]: "0",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
        })
      }), {
        Z: common_vendor.unref(userInfo).isRealAuth,
        aa: common_vendor.unref(isLogined)
      }, common_vendor.unref(isLogined) ? {
        ab: common_vendor.p({
          name: "edit-fill",
          color: "#fff",
          size: 46
        }),
        ac: common_vendor.o(fabClick),
        ad: common_vendor.p({
          width: 128,
          isDrag: true,
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
        })
      } : {}, {
        ae: loading.value
      }, loading.value ? {
        af: common_vendor.p({
          isMask: true
        })
      } : {}, {
        ag: common_assets._imports_0$4,
        ah: timeNum.value > 0
      }, timeNum.value > 0 ? {
        ai: common_vendor.t(timeNum.value)
      } : {}, {
        aj: common_vendor.o(handleVoteNoticeOperation),
        ak: common_vendor.p({
          radius: "100rpx",
          width: "100%",
          disabled: disableBtn.value,
          ["disabled-background"]: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          borderColor: "rgba(0,0,0,0)",
          ["border-width"]: "0"
        }),
        al: common_vendor.o(handleExitVote),
        am: common_vendor.p({
          radius: "100rpx",
          background: "#fff",
          color: "#000000",
          borderColor: "#ffffff",
          text: "退出投票"
        }),
        an: common_vendor.o(closeModal),
        ao: common_vendor.p({
          show: showModal.value,
          closable: false
        }),
        ap: common_vendor.n(!common_vendor.unref(isLogined) || !common_vendor.unref(userInfo).isRealAuth ? "bg-_a__a_ffffff_a_" : "bg-_a__a_f7f7f7_a_")
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d383e1a4"]]);
wx.createPage(MiniProgramPage);
