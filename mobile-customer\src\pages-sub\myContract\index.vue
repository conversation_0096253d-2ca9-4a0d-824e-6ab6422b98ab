<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '合同签订',
  },
}
</route>

<template>
  <view>
    <z-paging
      ref="paging"
      :auto-hide-loading-after-first-loaded="false"
      loading-full-fixed
      :paging-style="{ 'background-color': '#f7f7f7' }"
      v-model="dataList"
      :auto-show-back-to-top="true"
      :back-to-top-bottom="200"
      @query="queryList"
      :auto="true"
      lower-threshold="150rpx"
    >
      <template #loading>
        <fui-loading isMask></fui-loading>
      </template>
      <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
      <template #empty>
        <fui-empty
          :width="386"
          :height="280"
          src="/static/images/img_data_3x.png"
          isFixed
          title="暂无数据"
        ></fui-empty>
      </template>

      <view class="mt20rpx">
        <view class="mb-20rpx px20rpx" v-for="item in dataList" :key="item.id">
          <my-contract-item :item="item" @viewSign="handleView(item)" />
        </view>
      </view>
      <template #bottom>
        <safe-bottom-zone></safe-bottom-zone>
      </template>
    </z-paging>
    <fui-loading isMask v-if="loading"></fui-loading>
  </view>
</template>

<script lang="ts" setup>
import { queryContractList } from '@/service/app'
import myContractItem from './components/myContractItem.vue'
import { getEnvBaseUrl } from '@/utils'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { showTextToast } from '@/utils/util'
import safeBottomZone from '@/components/common/safeBottomZone.vue'

const userStore = useUserStore()

const { userInfo } = storeToRefs(userStore)

const baseUrl = getEnvBaseUrl()
const fileContractStreamPath = import.meta.env.VITE_FILE_STREAM_PATH
//
const dataList = ref([])

const paging = ref(null)

const queryList = async (pageNo, pageSize) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
  }

  console.log('params', params)
  // 此处请求仅为演示，请替换为自己项目中的请求
  try {
    const res = await queryContractList({ params })
    console.log('getMyVote', res)
    paging.value.completeByNoMore(res?.result || [], true)
  } catch (error) {
    paging.value.complete(false)
  }
}
const loading = ref(false)
const handleView = (item) => {
  if (loading.value) {
    return
  }
  loading.value = true
  // 获取文件后缀
  const fileSuffix = item?.contractUrl?.split('.')?.pop() || 'docx'
  const _url = `${baseUrl}${fileContractStreamPath}?fileName=${item.contractUrl}&token=${userInfo.value.token}`
  uni.downloadFile({
    url: _url,
    success: function (res) {
      console.log('contractDetail-getSignDocumentRelId-res-success', res)
      const _filePath = res.tempFilePath
      uni.openDocument({
        filePath: _filePath,
        fileType: fileSuffix,
        success: function (res) {
          console.log('打开文档成功')
        },
        fail: function (res) {
          console.log('打开文档失败', res)
          showTextToast('打开文档失败')
        },
      })
    },
    fail: function (res) {
      console.log('打开文档失败222', res)
      showTextToast('打开文档失败')
    },
    complete: () => {
      loading.value = false
    },
  })
}
</script>

<style lang="scss" scoped>
//
</style>
