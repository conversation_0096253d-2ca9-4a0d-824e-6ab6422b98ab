<template>
  <view class="h160rpx bg-[#ffffff] px32rpx py20rpx" @click="handleToVoteResultDetail">
    <view class="flex justify-between items-center h100%">
      <view class="w-200rpx h160rpx rounded-10rpx">
        <image
          class="w100% h100% rounded-10rpx"
          src="/static/images/dt_001.jpg"
          mode="scaleToFill"
        />
      </view>

      <view class="flex-1 ml10rpx flex flex-col justify-between h90%">
        <view class="text-28rpx text-[#000000] font-bold">{{ content }}</view>
        <view class="text-26rpx text-[#09BE4F]">投票胜出品牌：{{ item.resultBrandName }}</view>
        <view class="text-24rpx text-[#7F7F7F]">{{ item.voteResultTime }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const content = computed(() => {
  // return `${props.item.communityName}${props.item.buildingNo || ''}幢电梯更新投票结果公示。`
  return `${props.item.voteName}投票结果公示。`
})

// 跳转到投票结果详情
const handleToVoteResultDetail = () => {
  uni.navigateTo({
    url: `/pages-sub/votePublicDetail/index?id=${props.item.id}`,
  })
}
</script>

<style lang="scss" scoped>
//
</style>
