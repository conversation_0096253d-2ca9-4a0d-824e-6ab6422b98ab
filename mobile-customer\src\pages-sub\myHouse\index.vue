<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的房产',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="p-20rpx">
      <view
        class="bg-[#ffffff] p-20rpx rounded-20rpx mb-20rpx flex justify-between items-center"
        v-for="(item, index) in houseData"
        :key="index"
      >
        <view class="flex flex-col gap-y-10rpx text-24rpx">
          <view class="flex items-center">
            <view class="text-28rpx font-bold">{{ item.location }}</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#666]">小区名称：</view>
            <view>{{ item.communityName }}</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#666]">户主姓名：</view>
            <view>{{ item.userName }}</view>
          </view>
        </view>
        <view class="w100rpx">
          <fui-button type="link" color="#465CFF" @click="handleToError(item)">纠错</fui-button>
        </view>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import { getMyHouse } from '@/service/app'

const loading = ref(false)

//
const houseData = ref([])

const fetchMyHouse = async () => {
  try {
    loading.value = true
    const res = await getMyHouse({
      params: {},
    })
    houseData.value = res.result || []
    console.log('res', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const handleToError = (item) => {
  const { location, communityName, id } = item
  uni.navigateTo({
    url: `/pages-sub/myHouse/errorCorrection?id=${id}&location=${location}&communityName=${communityName}`,
  })
}

onLoad(() => {
  fetchMyHouse()
})
</script>

<style lang="scss" scoped>
//
</style>
