"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_const = require("../../utils/const.js");
const utils_util = require("../../utils/util.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  const _easycom_fui_bottom_popup2 = common_vendor.resolveComponent("fui-bottom-popup");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _easycom_fui_empty2 + _easycom_fui_loading2 + _easycom_fui_icon2 + _easycom_fui_bottom_popup2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_icon = () => "../../components/firstui/fui-icon/fui-icon.js";
const _easycom_fui_bottom_popup = () => "../../components/firstui/fui-bottom-popup/fui-bottom-popup.js";
if (!Math) {
  (_easycom_fui_button + _easycom_fui_empty + _easycom_fui_loading + _easycom_fui_icon + _easycom_fui_bottom_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const productDetail = common_vendor.ref({
      name: "",
      price: 0,
      description: "",
      content: "",
      pic: "",
      imgs: "",
      totalStocks: 0,
      skus: [],
      parameters: []
    });
    const selectedSku = common_vendor.ref({});
    const showPopup = common_vendor.ref(false);
    const productImages = common_vendor.computed(() => {
      if (!productDetail.value.imgs)
        return [];
      return productDetail.value.imgs.split(",").map((img) => img.trim());
    });
    const getImageUrl = (imgPath) => {
      if (imgPath == null ? void 0 : imgPath.startsWith("http"))
        return imgPath;
      return `${"http://**********:9000/dfloorpublic"}/${imgPath}`;
    };
    common_vendor.computed(() => {
      var _a;
      return ((_a = productDetail.value) == null ? void 0 : _a.skus[0]) || {};
    });
    const youNeedToPay = common_vendor.computed(() => {
      return utils_util.calculatePrePrice(selectedSku.value.price, formulaParams.value);
    });
    const selectSkuChange = (sku) => {
      console.log("selectSkuChange", sku);
      selectedSku.value = sku;
    };
    const openPopup = () => {
      showPopup.value = true;
    };
    const closePopup = () => {
      showPopup.value = false;
    };
    const handleSelectVote = () => __async(this, null, function* () {
      if (!selectedSku.value) {
        common_vendor.index.showToast({
          title: "请选择商品规格",
          icon: "none"
        });
        return;
      }
      yield common_vendor.nextTick$1();
      common_vendor.index.navigateBack({
        delta: 1,
        success: () => {
          common_vendor.index.$emit(utils_const.EventName.VOTE_BRAND_SELECTED, {
            skuId: selectedSku.value.id,
            code: selectedSku.value.code,
            productId: productDetail.value.id,
            skuName: selectedSku.value.name
          });
        },
        fail: (err) => {
          console.log("err", err);
        }
      });
    });
    const formatRichText = (html) => {
      let newContent = html.replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"').replace(/\<div style=\"/gi, '<div style="width:100%;word-break:break-all;word-wrap:normal;').replace(/\s{2,}/g, "");
      return newContent;
    };
    const fetchProductDetail = (id) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getProductDetail({
          params: {
            id
          }
        });
        if (res.code === 200 && res.result) {
          const content = formatRichText(res.result.content);
          productDetail.value = __spreadProps(__spreadValues({}, res.result), {
            content
          });
          if (res.result.skus && res.result.skus.length > 0) {
            selectedSku.value = res.result.skus[0];
          }
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取商品详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const productVoteCnt = common_vendor.ref(0);
    const fetchProductVoteCnt = () => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.queryProductVoteCnt({
          params: { supplierId: supplierId.value, voteId: voteId.value }
        });
        console.log("fetchProductVoteCnt", res);
        productVoteCnt.value = res.result;
      } catch (error) {
      }
    });
    const guobuConfig = common_vendor.ref({});
    const fetchSubsidyPrice = () => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.querySubsidyPrice({});
        console.log("fetchSubsidyPrice", res);
        guobuConfig.value = res.result;
      } catch (error) {
      }
    });
    const formulaParams = common_vendor.ref({ reduction: 0, rate: 1 });
    const fetchPrePrice = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getPrePrice({
          params: {
            communityId: communityId.value,
            buildingIds: buildingIds.value
          }
        });
        formulaParams.value = res.result;
        console.log("res", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad(
      (options) => {
        console.log("options", options);
        if (options.id) {
          communityId.value = options.communityId;
          buildingIds.value = options.buildingId;
          supplierId.value = options.supplierId;
          voteId.value = options.voteId;
          fetchProductDetail(options.id);
          fetchProductVoteCnt();
          fetchSubsidyPrice();
          fetchPrePrice();
        }
      }
    );
    const communityId = common_vendor.ref("");
    const buildingIds = common_vendor.ref("");
    const supplierId = common_vendor.ref("");
    const voteId = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const instance = common_vendor.getCurrentInstance().proxy;
      instance == null ? void 0 : instance.getOpenerEventChannel();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(productDetail).id
      }, common_vendor.unref(productDetail).id ? common_vendor.e({
        b: common_vendor.f(common_vendor.unref(productImages), (img, index, i0) => {
          return {
            a: getImageUrl(img),
            b: index
          };
        }),
        c: common_vendor.t(common_vendor.unref(youNeedToPay)),
        d: common_vendor.t(common_vendor.unref(productDetail).sold),
        e: common_vendor.t(common_vendor.unref(selectedSku).price),
        f: common_vendor.t(common_vendor.unref(guobuConfig).subsidiesPrice),
        g: common_vendor.t(common_vendor.unref(productVoteCnt)),
        h: common_vendor.unref(guobuConfig).remainCnt > 0
      }, common_vendor.unref(guobuConfig).remainCnt > 0 ? {} : {}, {
        i: common_vendor.t(common_vendor.unref(productDetail).name),
        j: common_vendor.t(common_vendor.unref(productDetail).description)
      }, {}, {
        l: common_vendor.f(common_vendor.unref(productDetail).parameters, (param, k0, i0) => {
          return {
            a: common_vendor.t(param.parameterName),
            b: common_vendor.t(param.parameterName === "楼层" ? common_vendor.unref(productDetail).floors : param.parameterName === "型号" ? common_vendor.unref(productDetail).model : param.parameterValue),
            c: param.id
          };
        }),
        m: common_vendor.unref(productDetail).content,
        n: common_vendor.o(openPopup),
        o: common_vendor.p({
          type: "primary",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "选中投票"
        })
      }) : {
        p: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          isFixed: true,
          title: "暂无数据"
        })
      }, {
        q: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        r: common_vendor.p({
          isMask: true
        })
      } : {}, {
        s: common_vendor.p({
          name: "close",
          size: 42,
          color: "#B2B2B2"
        }),
        t: common_vendor.o(closePopup),
        v: common_vendor.unref(productDetail).brandLogo
      }, common_vendor.unref(productDetail).brandLogo ? {
        w: getImageUrl(common_vendor.unref(productDetail).brandLogo)
      } : {}, {
        x: common_vendor.t(common_vendor.unref(youNeedToPay)),
        y: common_vendor.t(common_vendor.unref(selectedSku).price),
        z: common_vendor.t(common_vendor.unref(guobuConfig).subsidiesPrice),
        A: common_vendor.t(common_vendor.unref(productVoteCnt)),
        B: common_vendor.f(common_vendor.unref(productDetail).skus, (sku, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(sku.name),
            b: sku.id,
            c: common_vendor.n(((_a = common_vendor.unref(selectedSku)) == null ? void 0 : _a.id) === sku.id ? " border  border-solid border-_a__a_DB393D_a_ bg-_a__a_FFF1F4_a_ text-_a__a_DB393D_a_" : "bg-_a__a_f5f5f5_a_ text-_a__a_666666_a_"),
            d: common_vendor.o(($event) => selectSkuChange(sku), sku.id)
          };
        }),
        C: common_vendor.o(handleSelectVote),
        D: common_vendor.p({
          type: "primary",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "选中投票"
        }),
        E: common_vendor.o(closePopup),
        F: common_vendor.p({
          show: common_vendor.unref(showPopup)
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-454321f3"]]);
wx.createPage(MiniProgramPage);
