<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '投票',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <fui-nav-bar title="扫码投票" splitLine :size="15" isFixed isOccupy>
      <template #default>
        <view class="flex items-center" @click="handleScanCode">
          <fui-icon name="scan" :size="40" color="#145DB2"></fui-icon>
          <!-- <view class="w-[40rpx] h-[40rpx]">
            <image
              class="vertical-base w100% h100%"
              src="/static/images/dt_002_1.png"
              mode="scaleToFill"
            />
          </view> -->
          <view class="text-[#145DB2] text-24rpx ml6rpx">扫码投票</view>
        </view>
      </template>
    </fui-nav-bar>
    <view v-show="userInfo.isRealAuth">
      <view class="max-w-300rpx ml30rpx">
        <VoteHouseSelect
          ref="voteHouseSelectRef"
          @house-vote-select-click="handleHouseVoteSelectClick"
          @house-vote-result="handleHouseVoteResult"
        />
      </view>

      <view v-if="!voteData">
        <resultTitle imgUrl="/static/images/img_data_3x.png" v-if="houseVoteResult === 1">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您已经完成投票，可至我的-我的投票中进行查看
          </view>
          <fui-button
            btn-size="medium"
            text="查看投票"
            type="primary"
            bold
            :margin="['48rpx', '0', '24rpx']"
            @click="handleJumpMyVote"
          ></fui-button>
        </resultTitle>

        <resultTitle imgUrl="/static/images/img_data_3x.png" v-else-if="houseVoteResult === 2">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您的房产的投票活动还未开始，请留意相关的公告，尽情期待
          </view>
        </resultTitle>

        <resultTitle imgUrl="/static/images/img_data_3x.png" v-else-if="houseVoteResult === 3">
          <view class="mt40rpx text-32rpx text-[#333] px70rpx flex text-center">
            您的房产对应的投票活动已经结束，请至消息中心中查看投票结果
          </view>
          <fui-button
            btn-size="medium"
            text="查看消息"
            type="primary"
            bold
            :margin="['48rpx', '0', '24rpx']"
            @click="handleJumpMessage"
          ></fui-button>
        </resultTitle>
      </view>

      <view v-else>
        <view class="bg-[#ffffff] p-30rpx">
          <view class="text-30rpx font-bold text-[#333] mb20rpx">
            您好，{{ userInfo.realname }}，您正在进行{{ voteData.communityName
            }}{{ voteData.buildingNo }}幢的投票
          </view>
          <view
            class="text-26rpx text-[#666] mb10rpx flex"
            v-for="(item, index) in allAddressList"
            :key="index"
          >
            <text class="mr10rpx">产权房: {{ item.location }}</text>
            <fui-tag
              v-if="item.myself == 1"
              text="被授权"
              type="success"
              :padding="['6rpx', '6rpx']"
            ></fui-tag>
          </view>
          <view class="text-26rpx text-[#1296db]">投票截止日期: {{ voteData.voteEndDate }}</view>
        </view>

        <view class="bg-[#ffffff] px-30rpx pb-10rpx mt20rpx">
          <fui-tabs
            :tabs="buildingListNew"
            center
            selectedColor="#005ED1"
            sliderBackground="#005ED1"
          ></fui-tabs>
          <!-- 投票进度条 -->
          <view class="my20rpx">
            <progress
              :percent="voteData.voteRateValue"
              :stroke-width="12"
              :border-radius="8"
              activeColor="#05D6A2"
            />
            <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
              <text>本楼栋票数: {{ voteData.voteCount }}</text>
              <text>投票率: {{ voteData.voteRate }}</text>
            </view>
          </view>
        </view>
        <!-- 倒计时 -->
        <view class="bg-[#ffffff] mt20rpx p-30rpx">
          <view class="flex items-center text-28rpx text-[#333]">
            <text>结束倒计时:</text>
            <view class="flex items-center ml20rpx">
              <fui-count-down
                isDays
                :isColon="false"
                size="42"
                width="50"
                height="50"
                borderColor="transparent"
                background="transparent"
                color="#005ED1"
                colonColor="#005ED1"
                :value="voteData.voteEndSeconds"
              ></fui-count-down>
            </view>
          </view>
        </view>
        <TitleHeader title="请扫码添加3个品牌进行投票" />

        <view class="bg-[#ffffff] mt20rpx pt10rpx" v-if="brands.length > 0">
          <view class="brand-list mb20rpx">
            <fui-swipeaction-group>
              <fui-swipe-action
                @click="onSwipeClick($event, brand)"
                v-for="(brand, index) in brands"
                :key="index"
              >
                <view class="relative" :key="index">
                  <VoteBrandCheckItem
                    :showCheckBox="false"
                    :brand="brand"
                    :index="index"
                    :selectedCount="selectedCount"
                    :isListenSort="isListenSort"
                    :buildingIds="buildingId"
                    :voteId="id"
                    @changeVoteSort="changeVoteSort"
                  />
                </view>
              </fui-swipe-action>
            </fui-swipeaction-group>
          </view>
        </view>
        <view class="w100%">
          <view class="px40rpx">
            <VoteNotice />
          </view>
        </view>
      </view>
      <view class="w100% fixed bottom-0 left-0 z-[100]" v-show="brands.length > 0">
        <view class="bottom-zone px33rpx flex justify-between items-center h124rpx bg-[#ffffff]">
          <view>
            <view class="text-24rpx font-bold">预计您需要承担</view>
            <text class="text-24rpx font-bold text-[#FF2B2B]">
              ￥{{ minSkuPrice }} ~ ￥{{ maxSkuPrice }}
            </text>
          </view>
          <view class="flex items-center gap-x-10rpx">
            <fui-button
              type="primary"
              width="184rpx"
              height="84rpx"
              :size="32"
              radius="10rpx"
              text="立即投票"
              @click="handleImmediateVote"
            ></fui-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 投票须知 -->
    <fui-landscape :show="showModal" :closable="false" @close="closeModal">
      <view class="fui-ani__box">
        <image class="fui-hd__img" src="/static/images/dt_008.jpg" mode="widthFix"></image>
        <view class="fui-flex__center">
          <view class="w100%">
            <VoteNotice height="50vh" />

            <fui-button
              class="text-center"
              radius="100rpx"
              width="100%"
              :disabled="disableBtn"
              disabled-background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              borderColor="rgba(0,0,0,0)"
              border-width="0"
              @click="handleVoteNoticeOperation"
            >
              已同意并开始投票
              <text v-if="timeNum > 0">（{{ timeNum }}s）</text>
            </fui-button>
            <fui-button
              class="text-center mt20rpx"
              radius="100rpx"
              background="#fff"
              color="#000000"
              borderColor="#ffffff"
              @click="handleExitVote"
              text="退出投票"
            ></fui-button>
          </view>
        </view>
      </view>
    </fui-landscape>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import {
  addVoteNoticeOperation,
  getAllGrantedAndSelfList,
  getHasClickVoteNote,
  getPrePrice,
  queryBizVoteInfoById,
  querySupplierByVoteId,
  voteByUser,
} from '@/service/app'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import VoteHouseSelect from '@/pages/vote/components/voteHouseSelect.vue'
import { calculatePrePrice, calculateVoteEndSeconds, showTextToast } from '@/utils/util'
import VoteNotice from '@/pages/vote/components/voteNotice.vue'
import { getUrlObj } from '@/utils'
import resultTitle from '@/pages/vote/components/resultTip.vue'
import VoteBrandCheckItem from '@/pages-sub/vote/components/voteBrandCheckItem.vue'
import TitleHeader from '@/components/common/titleHeader.vue'

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const loading = ref(false)
const voteData = ref(null)

// 投票电梯列表
const brands = ref([])

// 是否监听排序 0 不监听 1 监听
const isListenSort = ref(0)

const selectedCount = computed(() => {
  return brands.value.filter((item) => item.isChecked).length || 1
})

// 投票公告id
const id = ref('')

// 楼栋id
const buildingId = ref('')

// 小区id
const communityId = ref('')

// 投票小区选择组件ref
const voteHouseSelectRef = ref(null)

// 是否禁用弹窗已阅读并开始投票按钮
const disableBtn = ref(true)

const buildingListNew = ref([])

// 是否显示投票须知弹窗
const showModal = ref(false)

// 关闭投票须知弹窗
const closeModal = () => {
  disableBtn.value = true
  showModal.value = false
  clearTimeout()
}

// 跳转到我的投票页面
const handleJumpMyVote = () => {
  uni.navigateTo({
    url: '/pages-sub/myVote/index',
  })
}

// 跳转到消息页面
const handleJumpMessage = () => {
  uni.switchTab({
    url: '/pages/message/index',
  })
}

// 投票注意事项操作记录
const handleVoteNoticeOperation = () => {
  voteNoticeOperation(1)
  closeModal()
}

// 退出投票
const handleExitVote = () => {
  voteNoticeOperation(0)
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}

// 投票注意事项操作记录
// 0:拒绝 1:同意
const voteNoticeOperation = async (_operationType: number) => {
  try {
    const res = await addVoteNoticeOperation({
      body: {
        operationType: _operationType,
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
  } catch (error) {
    console.log('error', error)
  }
}

// 判断有没有点击过投票
const fetchHasClickVoteNote = async () => {
  try {
    const res = await getHasClickVoteNote({
      params: {
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
    // 判断是否需要弹窗
    if (res.result?.length === 0) {
      showModal.value = true
      await nextTick()
      startTimeInterval()
    } else {
      showModal.value = false
    }
  } catch (error) {
    console.log('error', error)
  }
}

// 获取个人信息
const fetchUserInfo = async () => {
  const res = await userStore.checkUserInfo()
  console.log('fetchUserInfo', res?.result?.isRealAuth)
  await nextTick()
  // 关闭下拉选项
  voteHouseSelectRef.value?.closeDropdown()
  // 已认证
  if (res.result?.isRealAuth === 1) {
    // 获取可投票列表
    voteHouseSelectRef.value?.fetchCommunityList()
  } else {
    uni.navigateTo({
      url: '/pages-sub/realname/index',
    })
  }
}

// 倒计时相关功能
let timeInterval = null
// 默认倒计时
const timeCount = import.meta.env.VITE_DIALOG_TIMEOUT_COUNT || 1

const timeNum = ref(0)
const startTimeInterval = (time: number = timeCount) => {
  if (timeInterval) {
    clearTimeout()
  }
  timeNum.value = time
  timeInterval = setInterval(() => {
    timeNum.value = timeNum.value - 1
    if (timeNum.value <= 0) {
      clearTimeout()
      disableBtn.value = false
    }
  }, 1000)
}
const clearTimeout = () => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
    timeNum.value = 0
  }
}

// 获取地址列表
const allAddressList = ref([])
// 获取地址列表
const fetchAddressList = async () => {
  const res = await getAllGrantedAndSelfList({
    params: {
      communityId: communityId.value,
      buildingIds: buildingId.value,
    },
  })
  allAddressList.value = res.result
  if (res.result.length > 0) {
    // 不用处理 现在返回的投票信息就是按照楼栋的
    // handleBuildingChange(res.result[0])
  }
}

// 获取投票公告详情
// 根据投票公告状态 判断要不要弹窗显示
const fetchVoteInfo = async () => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id: id.value,
        buildingId: buildingId.value,
      },
    })
    console.log('queryBizVoteInfoById', res)
    // 投票信息
    // 倒计时
    const voteEndSeconds = calculateVoteEndSeconds(res.result.voteEndDate)
    voteData.value = {
      ...res.result,
      voteEndSeconds,
      voteRateValue: parseFloat(res.result.voteRate),
      voteAreaRateValue: parseFloat(res.result.voteAreaRate),
    }

    if (res.result.communityId) {
      communityId.value = res.result.communityId
      // 获取地址列表
      fetchAddressList()
      await fetchPrePrice()
    }

    if (launchId.value) {
      await fetchBrandById(launchId.value)
      launchId.value = ''
    }

    // 判断有没有点击过投票
    fetchHasClickVoteNote()
  } catch (error) {
    console.log('error', error)
    voteData.value = null
  } finally {
    loading.value = false
  }
}

// 切换小区
const handleHouseVoteSelectClick = async (select: string) => {
  buildingListNew.value = []

  // todo 清除之前的品牌供应商
  brands.value = []
  console.log('handleHouseVoteSelectClick', select)
  id.value = select.value
  buildingId.value = select.buildingId
  // 楼栋tab数据
  buildingListNew.value.push({
    name: `${select?.buildingNo || '-'}幢`,
    value: select?.buildingId,
  })
  await fetchVoteInfo()
}

// 查看获取的可投票的房产结果
// 1: 已投票且已结束
// 2: 没有房产参与投票
// 3: 已结束且未投票
const houseVoteResult = ref<number>(0)
const handleHouseVoteResult = (result: number) => {
  console.log('handleHouseVoteResult', result)
  houseVoteResult.value = result
}

// 获取预估价格,用于计算
const formulaParams: any = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async () => {
  try {
    loading.value = true
    const res = await getPrePrice({
      params: {
        voteId: id.value,
        buildingId: buildingId.value,
      },
    })
    formulaParams.value = res.result
    console.log('res', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const fetchBrandById = async (_supplierId) => {
  try {
    loading.value = true
    const res: any = await querySupplierByVoteId({
      params: {
        voteId: id.value,
        buildingId: buildingId.value,
        supplierId: _supplierId,
      },
    })
    console.log('querySupplierByVoteId', res)
    if (!res.result || res.result.length === 0) {
      showTextToast('没有获取到商品数据')
      return
    }

    const hasExist = brands.value.find((item) => item.id === res.result[0].id)
    if (hasExist) {
      showTextToast('已经添加过该商品')
      return
    }

    const _currentLength = brands.value.length
    isListenSort.value = 0
    const _brands =
      res.result.map((item, index) => {
        // 处理商品数据
        return {
          ...item,
          voteCount: item.voteCount ?? 0,
          voteRate: item.voteRate ?? '0%',
          communityId: voteData.value.communityId,
          sortOrder: _currentLength + 1,
          isChecked: true,
          skus:
            item?.bizSkus?.map((sku, index) => {
              return {
                ...sku,

                checked: index === 0,
                skuPrice: sku.price,
                price: calculatePrePrice(sku.price, formulaParams.value),
              }
            }) || [],
        }
      }) || []

    brands.value = [...brands.value, ..._brands]
    await nextTick()
    isListenSort.value = 1
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const allChecked = computed(() => {
  return brands.value.filter((item) => item.isChecked)
})

// 获取所有选中品牌的sku
const allCheckedSkus = computed(() => {
  console.log('allChecked', allChecked.value)

  return allChecked.value.map((item) => {
    return item?.skus?.find((sku) => sku.checked) || {}
  })
})

// 找出最大的sku价格
const maxSkuPrice = computed(() => {
  if (allCheckedSkus.value.length <= 0) {
    return 0
  }
  return Math.max(...allCheckedSkus.value.map((item) => item.price || 0))
})
// 找出最小的sku价格
const minSkuPrice = computed(() => {
  if (allCheckedSkus.value.length <= 0) {
    return 0
  }
  return Math.min(...allCheckedSkus.value.map((item) => item.price || 0))
})

// 改变投票排序
const changeVoteSort = (brand: any, currentSortOrder: number) => {
  console.log('changeVoteSort2222', brand, currentSortOrder)
  const targetItem = brands.value.find((item) => item.id === brand.id)
  if (!targetItem) return

  const oldSort = targetItem.sortOrder

  // 如果新旧排序值相同，不需要处理
  if (oldSort === currentSortOrder) return

  // 根据移动方向调整其他项目的sort值
  if (oldSort < currentSortOrder) {
    // 向后移动：原位置后面且新位置前面（含）的项目sort-1
    brands.value.forEach((item) => {
      if (
        item.id !== targetItem.id &&
        item.sortOrder > oldSort &&
        item.sortOrder <= currentSortOrder
      ) {
        console.log('item.sortOrder--', item.sortOrder)
        item.sortOrder--
      }
    })
  } else {
    // 向前移动：新位置后面（含）且原位置前面的项目sort+1
    brands.value.forEach((item) => {
      if (
        item.id !== targetItem.id &&
        item.sortOrder >= currentSortOrder &&
        item.sortOrder < oldSort
      ) {
        item.sortOrder++
      }
    })
  }

  // 最后更新目标项目的sort值
  targetItem.sortOrder = currentSortOrder
}

// 投票提示信息
const voteTipInfo: any = ref({})
const handleImmediateVote = async () => {
  if (brands.value.length < 3) {
    showTextToast('请至少选择3个品牌')
    return
  }
  // 投票参数
  const _voteParams = {
    voteId: id.value,
    buildingId: buildingId.value,
    method: 0,

    supplierList: allChecked.value.map((item) => {
      // 找出所有选中品牌的sku
      const checkedSkus = item?.skus?.find((sku) => sku.checked)
      console.log('checkedSkus', checkedSkus)
      console.log('item', item)

      return {
        supplierId: item.supplierId,
        sort: item.sortOrder,
        skuCode: checkedSkus?.code,
        productId: item.productId,
        productName: item.productName,
      }
    }),
  }

  console.log('_voteParams', _voteParams)
  console.log('allChecked.value', allChecked.value)
  try {
    loading.value = true
    const res = await voteByUser({
      body: _voteParams,
    })
    console.log('res', res)
    showTextToast(res.message || '投票成功')
    // 使用定时器 防止页面跳转过快，不显示toast
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/welcome/index',
      })
    }, 1000)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const onSwipeClick = (e, item) => {
  console.log('onSwipeClick', e)
  console.log('onSwipeClick', item)
  // 二次确认
  uni.showModal({
    title: '提示',
    content: '确认删除？',
    success: async (res) => {
      if (res.confirm) {
        brands.value.splice(e.index, 1)
      } else if (res.cancel) {
        console.log('用户点击了取消')
      }
    },
  })
}

onHide(async () => {
  // 防止切换后台 会刷数据
  isScanCode.value = true
})

onShow(async () => {
  // 如果是扫码后进入onshow 则什么都不做
  if (isScanCode.value) {
    isScanCode.value = false
    return
  }
  // 实时获取最新用户信息，防止token过期
  await userStore.checkUserInfo()
  await nextTick()
  id.value = ''
  voteData.value = null
  buildingId.value = ''
  communityId.value = ''
  buildingListNew.value = []
  brands.value = []
  clearTimeout()
  console.log('isLogined', isLogined.value)
  console.log('isScanCode', isScanCode.value)
  if (!isLogined.value) {
    uni.navigateTo({
      url: `/pages-sub/mine/login/index`,
    })
  } else {
    await fetchUserInfo()
  }
})

// 第一次进来的参数，等获取到投票活动id后，再请求商品数据
const launchId = ref(null)
onLoad(async () => {
  const launchOptions = await uni.getLaunchOptionsSync()

  console.log('getLaunchOptions', launchOptions)
  console.log('getLaunchOptions--scene', launchOptions?.query?.scene)
  //   if (!launchOptions?.query?.scene) {
  //     showTextToast('没有获取到商品数据')
  //   }
  launchId.value = launchOptions?.query?.scene
  console.log('launchId', launchId.value)
})

// 是否扫码
const isScanCode = ref(false)
// 扫码
const handleScanCode = () => {
  isScanCode.value = true
  uni.scanCode({
    success: function (res) {
      console.log('res', res)
      const path = res.path
      const { query } = getUrlObj(path)
      console.log('query', query)
      if (query.scene) {
        fetchBrandById(query.scene)
      } else {
        showTextToast('没有获取到正确的商品条码')
      }
    },
    fail: function (err) {
      console.log('err', err)
      if (err.errMsg === 'scanCode:fail cancel') return
      showTextToast('扫码失败')
    },
    complete: function (res) {
      console.log('isScanCode', isScanCode.value)
    },
  })
}
</script>

<style lang="scss" scoped>
:deep(.fui-nav__left) {
  width: 200rpx;
}
</style>
<style lang="scss">
page {
  background-color: #f5f5f5;
}
</style>
<style lang="scss" scoped>
.fui-ani__box {
  width: 640rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding-bottom: 24rpx;
}
.fui-flex__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}
.fui-hd__img {
  width: 100%;
  height: 80rpx;
  display: block;
  border-radius: 20rpx;
}
</style>
