"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
require("./index.js");
const showTextToast = (title) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.showToast({
      title,
      icon: "none",
      mask: true,
      duration: 2e3,
      complete: () => {
        resolve(true);
      }
    });
  });
};
const calculateVoteEndSeconds = (voteEndDate) => {
  try {
    const voteEndDateTime = common_vendor.dayjs(voteEndDate).format("YYYY-MM-DD 23:59:59");
    const remainingSeconds = common_vendor.dayjs(voteEndDateTime).diff(common_vendor.dayjs(), "second");
    console.log("dayjs---剩余秒数:", remainingSeconds);
    return remainingSeconds > 0 ? remainingSeconds : 0;
  } catch (error) {
    const endDate = new Date(voteEndDate);
    endDate.setHours(23, 59, 59, 999);
    const now = /* @__PURE__ */ new Date();
    const diffMilliseconds = endDate.getTime() - now.getTime();
    const remainingSeconds = Math.floor(diffMilliseconds / 1e3);
    console.log("剩余秒数:22222", remainingSeconds);
    return remainingSeconds > 0 ? remainingSeconds : 0;
  }
};
const calculatePrePrice = (productPrice, formulaParams) => {
  console.log("productPrice", productPrice);
  console.log("formulaParams", formulaParams);
  if (typeof productPrice === "string") {
    productPrice = parseFloat(productPrice);
  }
  const { reduction, rate } = formulaParams;
  return ((productPrice - reduction) * rate).toFixed(2);
};
exports.calculatePrePrice = calculatePrePrice;
exports.calculateVoteEndSeconds = calculateVoteEndSeconds;
exports.showTextToast = showTextToast;
