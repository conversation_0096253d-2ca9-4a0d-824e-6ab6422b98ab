<template>
  <view class="">
    <view class="w100% h420rpx">
      <image class="w100% h100%" src="/static/images/dt_004.jpg" mode="scaleToFill"></image>
    </view>
    <view class="mt40rpx w100% h100% flex flex-col justify-center items-center">
      <view class="w100% h100% flex justify-center items-center">
        <image :src="imgUrl" class="w-[400rpx] h-[300rpx]" mode="widthFix"></image>
      </view>
      <slot></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  imgUrl: {
    type: String,
    default: '/static/images/dt_010_1.png',
  },
})
</script>

<style lang="scss" scoped>
//
</style>
