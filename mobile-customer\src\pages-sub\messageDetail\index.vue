<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '消息详情',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] height-no-tabbar overflow-y-auto">
    <view class="bg-[#ffffff] mt20rpx p3" v-if="messageDetail.remark">
      <view class="text-[#666] text-24rpx">{{ messageDetail.createTime }}</view>

      <view class="text-24rpx mt10rpx">
        {{ messageDetail.remark }}
      </view>
      <view class="mt10rpx">
        <fui-upload
          :isAdd="false"
          :isDel="false"
          radius="8"
          :fileList="fileList"
          width="160"
          height="160"
        ></fui-upload>
      </view>
    </view>
    <view class="bg-[#ffffff] mt20rpx p3" v-if="messageDetail.remarkReply">
      <view class="text-[#06A8F0] text-24rpx flex justify-between items-center">
        <view>{{ messageDetail.updateTime }}</view>
        <view>回复人：{{ messageDetail.updateBy }}</view>
      </view>
      <view class="text-24rpx mt10rpx">
        {{ messageDetail.remarkReply }}
      </view>
      <view class="mt10rpx">
        <fui-upload
          :isAdd="false"
          :isDel="false"
          radius="8"
          :fileList="fileReplyList"
          width="160"
          height="160"
        ></fui-upload>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import { queryBizErrorHouseDetail } from '@/service/app'
import { useUserStore } from '@/store'
import { getEnvBaseUrl } from '@/utils'
import { storeToRefs } from 'pinia'

const loading = ref(false)

const userStore = useUserStore()

const { userInfo } = storeToRefs(userStore)

const baseUrl = getEnvBaseUrl()
const fileStreamPath = import.meta.env.VITE_FILE_STREAM_PATH

const fileList = ref([])
const fileReplyList = ref([])

const messageDetail = ref<any>({})

// 获取消息详情
const getMessageDetail = async (id: string) => {
  try {
    loading.value = true
    const res = await queryBizErrorHouseDetail({
      params: {
        id,
      },
    })
    messageDetail.value = res.result || {}
    if (messageDetail.value.imgUrl) {
      fileList.value = messageDetail.value.imgUrl
        .split(',')
        .map(
          (item: any) =>
            `${baseUrl}${fileStreamPath}?fileName=${item}&token=${userInfo.value.token}`,
        )
    }
    if (messageDetail.value.imgUrlReply) {
      fileReplyList.value = messageDetail.value.imgUrlReply
        .split(',')
        .map(
          (item: any) =>
            `${baseUrl}${fileStreamPath}?fileName=${item}&token=${userInfo.value.token}`,
        )
    }
    console.log('fileList', fileList.value)
    console.log('fileReplyList', fileReplyList.value)
  } catch (error) {
  } finally {
    loading.value = false
  }
}

onLoad(async (options: { id: string }) => {
  console.log('options', options)
  getMessageDetail(options.id)
})
</script>

<style lang="scss" scoped></style>
