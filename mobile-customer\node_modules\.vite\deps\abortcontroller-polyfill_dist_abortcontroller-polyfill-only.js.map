{"version": 3, "sources": ["../../abortcontroller-polyfill/dist/abortcontroller-polyfill-only.js"], "sourcesContent": ["(function (factory) {\n  typeof define === 'function' && define.amd ? define(factory) :\n  factory();\n})((function () { 'use strict';\n\n  function _arrayLikeToArray(r, a) {\n    (null == a || a > r.length) && (a = r.length);\n    for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n    return n;\n  }\n  function _assertThisInitialized(e) {\n    if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    return e;\n  }\n  function _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n  }\n  function _classCallCheck(a, n) {\n    if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n  }\n  function _defineProperties(e, r) {\n    for (var t = 0; t < r.length; t++) {\n      var o = r[t];\n      o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n    }\n  }\n  function _createClass(e, r, t) {\n    return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n      writable: !1\n    }), e;\n  }\n  function _createForOfIteratorHelper(r, e) {\n    var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (!t) {\n      if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) {\n        t && (r = t);\n        var n = 0,\n          F = function () {};\n        return {\n          s: F,\n          n: function () {\n            return n >= r.length ? {\n              done: !0\n            } : {\n              done: !1,\n              value: r[n++]\n            };\n          },\n          e: function (r) {\n            throw r;\n          },\n          f: F\n        };\n      }\n      throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var o,\n      a = !0,\n      u = !1;\n    return {\n      s: function () {\n        t = t.call(r);\n      },\n      n: function () {\n        var r = t.next();\n        return a = r.done, r;\n      },\n      e: function (r) {\n        u = !0, o = r;\n      },\n      f: function () {\n        try {\n          a || null == t.return || t.return();\n        } finally {\n          if (u) throw o;\n        }\n      }\n    };\n  }\n  function _get() {\n    return _get = \"undefined\" != typeof Reflect && Reflect.get ? Reflect.get.bind() : function (e, t, r) {\n      var p = _superPropBase(e, t);\n      if (p) {\n        var n = Object.getOwnPropertyDescriptor(p, t);\n        return n.get ? n.get.call(arguments.length < 3 ? e : r) : n.value;\n      }\n    }, _get.apply(null, arguments);\n  }\n  function _getPrototypeOf(t) {\n    return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n      return t.__proto__ || Object.getPrototypeOf(t);\n    }, _getPrototypeOf(t);\n  }\n  function _inherits(t, e) {\n    if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n    t.prototype = Object.create(e && e.prototype, {\n      constructor: {\n        value: t,\n        writable: !0,\n        configurable: !0\n      }\n    }), Object.defineProperty(t, \"prototype\", {\n      writable: !1\n    }), e && _setPrototypeOf(t, e);\n  }\n  function _isNativeReflectConstruct() {\n    try {\n      var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function () {\n      return !!t;\n    })();\n  }\n  function _possibleConstructorReturn(t, e) {\n    if (e && (\"object\" == typeof e || \"function\" == typeof e)) return e;\n    if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n    return _assertThisInitialized(t);\n  }\n  function _setPrototypeOf(t, e) {\n    return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n      return t.__proto__ = e, t;\n    }, _setPrototypeOf(t, e);\n  }\n  function _superPropBase(t, o) {\n    for (; !{}.hasOwnProperty.call(t, o) && null !== (t = _getPrototypeOf(t)););\n    return t;\n  }\n  function _superPropGet(t, o, e, r) {\n    var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e);\n    return 2 & r && \"function\" == typeof p ? function (t) {\n      return p.apply(e, t);\n    } : p;\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _unsupportedIterableToArray(r, a) {\n    if (r) {\n      if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n      var t = {}.toString.call(r).slice(8, -1);\n      return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n    }\n  }\n\n  (function (self) {\n      return {\n        NativeAbortSignal: self.AbortSignal,\n        NativeAbortController: self.AbortController\n      };\n    })(typeof self !== 'undefined' ? self : global);\n\n  /**\n   * @param {any} reason abort reason\n   */\n  function createAbortEvent(reason) {\n    var event;\n    try {\n      event = new Event('abort');\n    } catch (e) {\n      if (typeof document !== 'undefined') {\n        if (!document.createEvent) {\n          // For Internet Explorer 8:\n          event = document.createEventObject();\n          event.type = 'abort';\n        } else {\n          // For Internet Explorer 11:\n          event = document.createEvent('Event');\n          event.initEvent('abort', false, false);\n        }\n      } else {\n        // Fallback where document isn't available:\n        event = {\n          type: 'abort',\n          bubbles: false,\n          cancelable: false\n        };\n      }\n    }\n    event.reason = reason;\n    return event;\n  }\n\n  /**\n   * @param {any} reason abort reason\n   */\n  function normalizeAbortReason(reason) {\n    if (reason === undefined) {\n      if (typeof document === 'undefined') {\n        reason = new Error('This operation was aborted');\n        reason.name = 'AbortError';\n      } else {\n        try {\n          reason = new DOMException('signal is aborted without reason');\n          // The DOMException does not support setting the name property directly.\n          Object.defineProperty(reason, 'name', {\n            value: 'AbortError'\n          });\n        } catch (err) {\n          // IE 11 does not support calling the DOMException constructor, use a\n          // regular error object on it instead.\n          reason = new Error('This operation was aborted');\n          reason.name = 'AbortError';\n        }\n      }\n    }\n    return reason;\n  }\n\n  var Emitter = /*#__PURE__*/function () {\n    function Emitter() {\n      _classCallCheck(this, Emitter);\n      Object.defineProperty(this, 'listeners', {\n        value: {},\n        writable: true,\n        configurable: true\n      });\n    }\n    return _createClass(Emitter, [{\n      key: \"addEventListener\",\n      value: function addEventListener(type, callback, options) {\n        if (!(type in this.listeners)) {\n          this.listeners[type] = [];\n        }\n        this.listeners[type].push({\n          callback: callback,\n          options: options\n        });\n      }\n    }, {\n      key: \"removeEventListener\",\n      value: function removeEventListener(type, callback) {\n        if (!(type in this.listeners)) {\n          return;\n        }\n        var stack = this.listeners[type];\n        for (var i = 0, l = stack.length; i < l; i++) {\n          if (stack[i].callback === callback) {\n            stack.splice(i, 1);\n            return;\n          }\n        }\n      }\n    }, {\n      key: \"dispatchEvent\",\n      value: function dispatchEvent(event) {\n        var _this = this;\n        if (!(event.type in this.listeners)) {\n          return;\n        }\n        var stack = this.listeners[event.type];\n        var stackToCall = stack.slice();\n        var _loop = function _loop() {\n          var listener = stackToCall[i];\n          try {\n            listener.callback.call(_this, event);\n          } catch (e) {\n            Promise.resolve().then(function () {\n              throw e;\n            });\n          }\n          if (listener.options && listener.options.once) {\n            _this.removeEventListener(event.type, listener.callback);\n          }\n        };\n        for (var i = 0, l = stackToCall.length; i < l; i++) {\n          _loop();\n        }\n        return !event.defaultPrevented;\n      }\n    }]);\n  }();\n  var AbortSignal = /*#__PURE__*/function (_Emitter) {\n    function AbortSignal() {\n      var _this2;\n      _classCallCheck(this, AbortSignal);\n      _this2 = _callSuper(this, AbortSignal);\n      // Some versions of babel does not transpile super() correctly for IE <= 10, if the parent\n      // constructor has failed to run, then \"this.listeners\" will still be undefined and then we call\n      // the parent constructor directly instead as a workaround. For general details, see babel bug:\n      // https://github.com/babel/babel/issues/3041\n      // This hack was added as a fix for the issue described here:\n      // https://github.com/Financial-Times/polyfill-library/pull/59#issuecomment-477558042\n      if (!_this2.listeners) {\n        Emitter.call(_this2);\n      }\n\n      // Compared to assignment, Object.defineProperty makes properties non-enumerable by default and\n      // we want Object.keys(new AbortController().signal) to be [] for compat with the native impl\n      Object.defineProperty(_this2, 'aborted', {\n        value: false,\n        writable: true,\n        configurable: true\n      });\n      Object.defineProperty(_this2, 'onabort', {\n        value: null,\n        writable: true,\n        configurable: true\n      });\n      Object.defineProperty(_this2, 'reason', {\n        value: undefined,\n        writable: true,\n        configurable: true\n      });\n      return _this2;\n    }\n    _inherits(AbortSignal, _Emitter);\n    return _createClass(AbortSignal, [{\n      key: \"toString\",\n      value: function toString() {\n        return '[object AbortSignal]';\n      }\n    }, {\n      key: \"dispatchEvent\",\n      value: function dispatchEvent(event) {\n        if (event.type === 'abort') {\n          this.aborted = true;\n          if (typeof this.onabort === 'function') {\n            this.onabort.call(this, event);\n          }\n        }\n        _superPropGet(AbortSignal, \"dispatchEvent\", this, 3)([event]);\n      }\n\n      /**\n       * @see {@link https://developer.mozilla.org/zh-CN/docs/Web/API/AbortSignal/throwIfAborted}\n       */\n    }, {\n      key: \"throwIfAborted\",\n      value: function throwIfAborted() {\n        var aborted = this.aborted,\n          _this$reason = this.reason,\n          reason = _this$reason === void 0 ? 'Aborted' : _this$reason;\n        if (!aborted) return;\n        throw reason;\n      }\n\n      /**\n       * @see {@link https://developer.mozilla.org/zh-CN/docs/Web/API/AbortSignal/timeout_static}\n       * @param {number} time The \"active\" time in milliseconds before the returned {@link AbortSignal} will abort.\n       *                      The value must be within range of 0 and {@link Number.MAX_SAFE_INTEGER}.\n       * @returns {AbortSignal} The signal will abort with its {@link AbortSignal.reason} property set to a `TimeoutError` {@link DOMException} on timeout,\n       *                        or an `AbortError` {@link DOMException} if the operation was user-triggered.\n       */\n    }], [{\n      key: \"timeout\",\n      value: function timeout(time) {\n        var controller = new AbortController();\n        setTimeout(function () {\n          return controller.abort(new DOMException(\"This signal is timeout in \".concat(time, \"ms\"), 'TimeoutError'));\n        }, time);\n        return controller.signal;\n      }\n\n      /**\n       * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static}\n       * @param {Iterable<AbortSignal>} iterable An {@link Iterable} (such as an {@link Array}) of abort signals.\n       * @returns {AbortSignal} - **Already aborted**, if any of the abort signals given is already aborted.\n       *                          The returned {@link AbortSignal}'s reason will be already set to the `reason` of the first abort signal that was already aborted.\n       *                        - **Asynchronously aborted**, when any abort signal in `iterable` aborts.\n       *                          The `reason` will be set to the reason of the first abort signal that is aborted.\n       */\n    }, {\n      key: \"any\",\n      value: function any(iterable) {\n        var controller = new AbortController();\n        /**\n         * @this AbortSignal\n         */\n        function abort() {\n          controller.abort(this.reason);\n          clean();\n        }\n        function clean() {\n          var _iterator = _createForOfIteratorHelper(iterable),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var signal = _step.value;\n              signal.removeEventListener('abort', abort);\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n        }\n        var _iterator2 = _createForOfIteratorHelper(iterable),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var signal = _step2.value;\n            if (signal.aborted) {\n              controller.abort(signal.reason);\n              break;\n            } else signal.addEventListener('abort', abort);\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n        return controller.signal;\n      }\n    }]);\n  }(Emitter);\n  var AbortController = /*#__PURE__*/function () {\n    function AbortController() {\n      _classCallCheck(this, AbortController);\n      // Compared to assignment, Object.defineProperty makes properties non-enumerable by default and\n      // we want Object.keys(new AbortController()) to be [] for compat with the native impl\n      Object.defineProperty(this, 'signal', {\n        value: new AbortSignal(),\n        writable: true,\n        configurable: true\n      });\n    }\n    return _createClass(AbortController, [{\n      key: \"abort\",\n      value: function abort(reason) {\n        var signalReason = normalizeAbortReason(reason);\n        var event = createAbortEvent(signalReason);\n        this.signal.reason = signalReason;\n        this.signal.dispatchEvent(event);\n      }\n    }, {\n      key: \"toString\",\n      value: function toString() {\n        return '[object AbortController]';\n      }\n    }]);\n  }();\n  if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n    // These are necessary to make sure that we get correct output for:\n    // Object.prototype.toString.call(new AbortController())\n    AbortController.prototype[Symbol.toStringTag] = 'AbortController';\n    AbortSignal.prototype[Symbol.toStringTag] = 'AbortSignal';\n  }\n\n  function polyfillNeeded(self) {\n    if (self.__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL) {\n      console.log('__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL=true is set, will force install polyfill');\n      return true;\n    }\n\n    // Note that the \"unfetch\" minimal fetch polyfill defines fetch() without\n    // defining window.Request, and this polyfill need to work on top of unfetch\n    // so the below feature detection needs the !self.AbortController part.\n    // The Request.prototype check is also needed because Safari versions 11.1.2\n    // up to and including 12.1.x has a window.AbortController present but still\n    // does NOT correctly implement abortable fetch:\n    // https://bugs.webkit.org/show_bug.cgi?id=174980#c2\n    return typeof self.Request === 'function' && !self.Request.prototype.hasOwnProperty('signal') || !self.AbortController;\n  }\n\n  (function (self) {\n\n    if (!polyfillNeeded(self)) {\n      return;\n    }\n    self.AbortController = AbortController;\n    self.AbortSignal = AbortSignal;\n  })(typeof self !== 'undefined' ? self : global);\n\n}));\n"], "mappings": ";CAAC,SAAU,SAAS;AAClB,SAAO,WAAW,cAAc,OAAO,MAAM,OAAO,OAAO,IAC3D,QAAQ;AACV,GAAI,WAAY;AAAE;AAEhB,WAAS,kBAAkB,GAAG,GAAG;AAC/B,KAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,aAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AAAK,QAAE,CAAC,IAAI,EAAE,CAAC;AACpD,WAAO;AAAA,EACT;AACA,WAAS,uBAAuB,GAAG;AACjC,QAAI,WAAW;AAAG,YAAM,IAAI,eAAe,2DAA2D;AACtG,WAAO;AAAA,EACT;AACA,WAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,WAAO,IAAI,gBAAgB,CAAC,GAAG,2BAA2B,GAAG,0BAA0B,IAAI,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,EAAE,WAAW,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC;AAAA,EAC1K;AACA,WAAS,gBAAgB,GAAG,GAAG;AAC7B,QAAI,EAAE,aAAa;AAAI,YAAM,IAAI,UAAU,mCAAmC;AAAA,EAChF;AACA,WAAS,kBAAkB,GAAG,GAAG;AAC/B,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,IAAI,EAAE,CAAC;AACX,QAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,eAAe,EAAE,GAAG,GAAG,CAAC;AAAA,IAC9I;AAAA,EACF;AACA,WAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,WAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,MACjH,UAAU;AAAA,IACZ,CAAC,GAAG;AAAA,EACN;AACA,WAAS,2BAA2B,GAAG,GAAG;AACxC,QAAI,IAAI,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAC5E,QAAI,CAAC,GAAG;AACN,UAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,KAAK,KAAK,YAAY,OAAO,EAAE,QAAQ;AACrG,cAAM,IAAI;AACV,YAAI,IAAI,GACN,IAAI,WAAY;AAAA,QAAC;AACnB,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG,WAAY;AACb,mBAAO,KAAK,EAAE,SAAS;AAAA,cACrB,MAAM;AAAA,YACR,IAAI;AAAA,cACF,MAAM;AAAA,cACN,OAAO,EAAE,GAAG;AAAA,YACd;AAAA,UACF;AAAA,UACA,GAAG,SAAUA,IAAG;AACd,kBAAMA;AAAA,UACR;AAAA,UACA,GAAG;AAAA,QACL;AAAA,MACF;AACA,YAAM,IAAI,UAAU,uIAAuI;AAAA,IAC7J;AACA,QAAI,GACF,IAAI,MACJ,IAAI;AACN,WAAO;AAAA,MACL,GAAG,WAAY;AACb,YAAI,EAAE,KAAK,CAAC;AAAA,MACd;AAAA,MACA,GAAG,WAAY;AACb,YAAIA,KAAI,EAAE,KAAK;AACf,eAAO,IAAIA,GAAE,MAAMA;AAAA,MACrB;AAAA,MACA,GAAG,SAAUA,IAAG;AACd,YAAI,MAAI,IAAIA;AAAA,MACd;AAAA,MACA,GAAG,WAAY;AACb,YAAI;AACF,eAAK,QAAQ,EAAE,UAAU,EAAE,OAAO;AAAA,QACpC,UAAE;AACA,cAAI;AAAG,kBAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,OAAO;AACd,WAAO,OAAO,eAAe,OAAO,WAAW,QAAQ,MAAM,QAAQ,IAAI,KAAK,IAAI,SAAU,GAAG,GAAG,GAAG;AACnG,UAAI,IAAI,eAAe,GAAG,CAAC;AAC3B,UAAI,GAAG;AACL,YAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,eAAO,EAAE,MAAM,EAAE,IAAI,KAAK,UAAU,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE;AAAA,MAC9D;AAAA,IACF,GAAG,KAAK,MAAM,MAAM,SAAS;AAAA,EAC/B;AACA,WAAS,gBAAgB,GAAG;AAC1B,WAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUC,IAAG;AAC3F,aAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,IAC/C,GAAG,gBAAgB,CAAC;AAAA,EACtB;AACA,WAAS,UAAU,GAAG,GAAG;AACvB,QAAI,cAAc,OAAO,KAAK,SAAS;AAAG,YAAM,IAAI,UAAU,oDAAoD;AAClH,MAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,MAC5C,aAAa;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,MACxC,UAAU;AAAA,IACZ,CAAC,GAAG,KAAK,gBAAgB,GAAG,CAAC;AAAA,EAC/B;AACA,WAAS,4BAA4B;AACnC,QAAI;AACF,UAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,MAAC,CAAC,CAAC;AAAA,IACxF,SAASA,IAAG;AAAA,IAAC;AACb,YAAQ,4BAA4B,WAAY;AAC9C,aAAO,CAAC,CAAC;AAAA,IACX,GAAG;AAAA,EACL;AACA,WAAS,2BAA2B,GAAG,GAAG;AACxC,QAAI,MAAM,YAAY,OAAO,KAAK,cAAc,OAAO;AAAI,aAAO;AAClE,QAAI,WAAW;AAAG,YAAM,IAAI,UAAU,0DAA0D;AAChG,WAAO,uBAAuB,CAAC;AAAA,EACjC;AACA,WAAS,gBAAgB,GAAG,GAAG;AAC7B,WAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGC,IAAG;AAC9F,aAAOD,GAAE,YAAYC,IAAGD;AAAA,IAC1B,GAAG,gBAAgB,GAAG,CAAC;AAAA,EACzB;AACA,WAAS,eAAe,GAAG,GAAG;AAC5B,WAAO,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,KAAK,UAAU,IAAI,gBAAgB,CAAC;AAAI;AAC3E,WAAO;AAAA,EACT;AACA,WAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,QAAI,IAAI,KAAK,gBAAgB,IAAI,IAAI,EAAE,YAAY,CAAC,GAAG,GAAG,CAAC;AAC3D,WAAO,IAAI,KAAK,cAAc,OAAO,IAAI,SAAUA,IAAG;AACpD,aAAO,EAAE,MAAM,GAAGA,EAAC;AAAA,IACrB,IAAI;AAAA,EACN;AACA,WAAS,aAAa,GAAG,GAAG;AAC1B,QAAI,YAAY,OAAO,KAAK,CAAC;AAAG,aAAO;AACvC,QAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,QAAI,WAAW,GAAG;AAChB,UAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,UAAI,YAAY,OAAO;AAAG,eAAO;AACjC,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACpE;AACA,YAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,EAC7C;AACA,WAAS,eAAe,GAAG;AACzB,QAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,WAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,EACxC;AACA,WAAS,4BAA4B,GAAG,GAAG;AACzC,QAAI,GAAG;AACL,UAAI,YAAY,OAAO;AAAG,eAAO,kBAAkB,GAAG,CAAC;AACvD,UAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACvC,aAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,IACvN;AAAA,EACF;AAEA,GAAC,SAAUE,OAAM;AACb,WAAO;AAAA,MACL,mBAAmBA,MAAK;AAAA,MACxB,uBAAuBA,MAAK;AAAA,IAC9B;AAAA,EACF,GAAG,OAAO,SAAS,cAAc,OAAO,MAAM;AAKhD,WAAS,iBAAiB,QAAQ;AAChC,QAAI;AACJ,QAAI;AACF,cAAQ,IAAI,MAAM,OAAO;AAAA,IAC3B,SAAS,GAAG;AACV,UAAI,OAAO,aAAa,aAAa;AACnC,YAAI,CAAC,SAAS,aAAa;AAEzB,kBAAQ,SAAS,kBAAkB;AACnC,gBAAM,OAAO;AAAA,QACf,OAAO;AAEL,kBAAQ,SAAS,YAAY,OAAO;AACpC,gBAAM,UAAU,SAAS,OAAO,KAAK;AAAA,QACvC;AAAA,MACF,OAAO;AAEL,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AAKA,WAAS,qBAAqB,QAAQ;AACpC,QAAI,WAAW,QAAW;AACxB,UAAI,OAAO,aAAa,aAAa;AACnC,iBAAS,IAAI,MAAM,4BAA4B;AAC/C,eAAO,OAAO;AAAA,MAChB,OAAO;AACL,YAAI;AACF,mBAAS,IAAI,aAAa,kCAAkC;AAE5D,iBAAO,eAAe,QAAQ,QAAQ;AAAA,YACpC,OAAO;AAAA,UACT,CAAC;AAAA,QACH,SAAS,KAAK;AAGZ,mBAAS,IAAI,MAAM,4BAA4B;AAC/C,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,UAAuB,WAAY;AACrC,aAASC,WAAU;AACjB,sBAAgB,MAAMA,QAAO;AAC7B,aAAO,eAAe,MAAM,aAAa;AAAA,QACvC,OAAO,CAAC;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,WAAO,aAAaA,UAAS,CAAC;AAAA,MAC5B,KAAK;AAAA,MACL,OAAO,SAAS,iBAAiB,MAAM,UAAU,SAAS;AACxD,YAAI,EAAE,QAAQ,KAAK,YAAY;AAC7B,eAAK,UAAU,IAAI,IAAI,CAAC;AAAA,QAC1B;AACA,aAAK,UAAU,IAAI,EAAE,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,oBAAoB,MAAM,UAAU;AAClD,YAAI,EAAE,QAAQ,KAAK,YAAY;AAC7B;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,UAAU,IAAI;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAI,MAAM,CAAC,EAAE,aAAa,UAAU;AAClC,kBAAM,OAAO,GAAG,CAAC;AACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,cAAc,OAAO;AACnC,YAAI,QAAQ;AACZ,YAAI,EAAE,MAAM,QAAQ,KAAK,YAAY;AACnC;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,UAAU,MAAM,IAAI;AACrC,YAAI,cAAc,MAAM,MAAM;AAC9B,YAAI,QAAQ,SAASC,SAAQ;AAC3B,cAAI,WAAW,YAAY,CAAC;AAC5B,cAAI;AACF,qBAAS,SAAS,KAAK,OAAO,KAAK;AAAA,UACrC,SAAS,GAAG;AACV,oBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA,cAAI,SAAS,WAAW,SAAS,QAAQ,MAAM;AAC7C,kBAAM,oBAAoB,MAAM,MAAM,SAAS,QAAQ;AAAA,UACzD;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,gBAAM;AAAA,QACR;AACA,eAAO,CAAC,MAAM;AAAA,MAChB;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,EAAE;AACF,MAAI,cAA2B,SAAU,UAAU;AACjD,aAASC,eAAc;AACrB,UAAI;AACJ,sBAAgB,MAAMA,YAAW;AACjC,eAAS,WAAW,MAAMA,YAAW;AAOrC,UAAI,CAAC,OAAO,WAAW;AACrB,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAIA,aAAO,eAAe,QAAQ,WAAW;AAAA,QACvC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AACD,aAAO,eAAe,QAAQ,WAAW;AAAA,QACvC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AACD,aAAO,eAAe,QAAQ,UAAU;AAAA,QACtC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AACD,aAAO;AAAA,IACT;AACA,cAAUA,cAAa,QAAQ;AAC/B,WAAO,aAAaA,cAAa,CAAC;AAAA,MAChC,KAAK;AAAA,MACL,OAAO,SAAS,WAAW;AACzB,eAAO;AAAA,MACT;AAAA,IACF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,cAAc,OAAO;AACnC,YAAI,MAAM,SAAS,SAAS;AAC1B,eAAK,UAAU;AACf,cAAI,OAAO,KAAK,YAAY,YAAY;AACtC,iBAAK,QAAQ,KAAK,MAAM,KAAK;AAAA,UAC/B;AAAA,QACF;AACA,sBAAcA,cAAa,iBAAiB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;AAAA,MAC9D;AAAA;AAAA;AAAA;AAAA,IAKF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,iBAAiB;AAC/B,YAAI,UAAU,KAAK,SACjB,eAAe,KAAK,QACpB,SAAS,iBAAiB,SAAS,YAAY;AACjD,YAAI,CAAC;AAAS;AACd,cAAM;AAAA,MACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASF,CAAC,GAAG,CAAC;AAAA,MACH,KAAK;AAAA,MACL,OAAO,SAAS,QAAQ,MAAM;AAC5B,YAAI,aAAa,IAAI,gBAAgB;AACrC,mBAAW,WAAY;AACrB,iBAAO,WAAW,MAAM,IAAI,aAAa,6BAA6B,OAAO,MAAM,IAAI,GAAG,cAAc,CAAC;AAAA,QAC3G,GAAG,IAAI;AACP,eAAO,WAAW;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,IAAI,UAAU;AAC5B,YAAI,aAAa,IAAI,gBAAgB;AAIrC,iBAAS,QAAQ;AACf,qBAAW,MAAM,KAAK,MAAM;AAC5B,gBAAM;AAAA,QACR;AACA,iBAAS,QAAQ;AACf,cAAI,YAAY,2BAA2B,QAAQ,GACjD;AACF,cAAI;AACF,iBAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,kBAAIC,UAAS,MAAM;AACnB,cAAAA,QAAO,oBAAoB,SAAS,KAAK;AAAA,YAC3C;AAAA,UACF,SAAS,KAAK;AACZ,sBAAU,EAAE,GAAG;AAAA,UACjB,UAAE;AACA,sBAAU,EAAE;AAAA,UACd;AAAA,QACF;AACA,YAAI,aAAa,2BAA2B,QAAQ,GAClD;AACF,YAAI;AACF,eAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,gBAAI,SAAS,OAAO;AACpB,gBAAI,OAAO,SAAS;AAClB,yBAAW,MAAM,OAAO,MAAM;AAC9B;AAAA,YACF;AAAO,qBAAO,iBAAiB,SAAS,KAAK;AAAA,UAC/C;AAAA,QACF,SAAS,KAAK;AACZ,qBAAW,EAAE,GAAG;AAAA,QAClB,UAAE;AACA,qBAAW,EAAE;AAAA,QACf;AACA,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,EAAE,OAAO;AACT,MAAI,kBAA+B,WAAY;AAC7C,aAASC,mBAAkB;AACzB,sBAAgB,MAAMA,gBAAe;AAGrC,aAAO,eAAe,MAAM,UAAU;AAAA,QACpC,OAAO,IAAI,YAAY;AAAA,QACvB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,WAAO,aAAaA,kBAAiB,CAAC;AAAA,MACpC,KAAK;AAAA,MACL,OAAO,SAAS,MAAM,QAAQ;AAC5B,YAAI,eAAe,qBAAqB,MAAM;AAC9C,YAAI,QAAQ,iBAAiB,YAAY;AACzC,aAAK,OAAO,SAAS;AACrB,aAAK,OAAO,cAAc,KAAK;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,MACD,KAAK;AAAA,MACL,OAAO,SAAS,WAAW;AACzB,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,EAAE;AACF,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AAGvD,oBAAgB,UAAU,OAAO,WAAW,IAAI;AAChD,gBAAY,UAAU,OAAO,WAAW,IAAI;AAAA,EAC9C;AAEA,WAAS,eAAeL,OAAM;AAC5B,QAAIA,MAAK,0CAA0C;AACjD,cAAQ,IAAI,mFAAmF;AAC/F,aAAO;AAAA,IACT;AASA,WAAO,OAAOA,MAAK,YAAY,cAAc,CAACA,MAAK,QAAQ,UAAU,eAAe,QAAQ,KAAK,CAACA,MAAK;AAAA,EACzG;AAEA,GAAC,SAAUA,OAAM;AAEf,QAAI,CAAC,eAAeA,KAAI,GAAG;AACzB;AAAA,IACF;AACA,IAAAA,MAAK,kBAAkB;AACvB,IAAAA,MAAK,cAAc;AAAA,EACrB,GAAG,OAAO,SAAS,cAAc,OAAO,MAAM;AAEhD,CAAE;", "names": ["r", "t", "e", "self", "Emitter", "_loop", "AbortSignal", "signal", "AbortController"]}