"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
if (!Array) {
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _component_safe_bottom_zone = common_vendor.resolveComponent("safe-bottom-zone");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_loading2 + _easycom_fui_empty2 + _component_safe_bottom_zone + _easycom_z_paging2 + _component_layout_default_uni)();
}
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_z_paging = () => "../../node-modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_fui_loading + _easycom_fui_empty + myVoteItem + _easycom_z_paging)();
}
const myVoteItem = () => "./components/myVoteItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    common_vendor.ref(false);
    const paging = common_vendor.ref(null);
    const dataList = common_vendor.ref([]);
    common_vendor.ref([]);
    const queryList = (pageNo, pageSize) => __async(this, null, function* () {
      var _a;
      const params = {
        pageNo,
        pageSize
      };
      console.log("params", params);
      try {
        const res = yield service_app_vote.getMyVote({ params });
        console.log("getMyVote", res);
        paging.value.complete(((_a = res == null ? void 0 : res.result) == null ? void 0 : _a.records) || [], true);
      } catch (error) {
        paging.value.complete(false);
      }
    });
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          isMask: true
        }),
        b: common_vendor.p({
          src: "/static/images/img_data_3x.png",
          width: 386,
          height: 280,
          isFixed: true,
          title: "暂无数据"
        }),
        c: common_vendor.f(common_vendor.unref(dataList), (item, k0, i0) => {
          return {
            a: "65d08cba-4-" + i0 + ",65d08cba-1",
            b: common_vendor.p({
              item
            }),
            c: item.id
          };
        }),
        d: common_vendor.sr(paging, "65d08cba-1,65d08cba-0", {
          "k": "paging"
        }),
        e: common_vendor.o(queryList),
        f: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        g: common_vendor.p({
          ["auto-hide-loading-after-first-loaded"]: false,
          ["loading-full-fixed"]: true,
          ["paging-style"]: {
            "background-color": "#f7f7f7"
          },
          ["auto-show-back-to-top"]: true,
          ["back-to-top-bottom"]: 200,
          auto: true,
          ["lower-threshold"]: "150rpx",
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-65d08cba"]]);
wx.createPage(MiniProgramPage);
