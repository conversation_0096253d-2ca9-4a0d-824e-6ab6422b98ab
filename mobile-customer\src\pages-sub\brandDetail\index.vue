<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] overflow-y-auto">
    <template v-if="productDetail.id">
      <!-- 商品图片轮播 -->
      <view class="bg-white">
        <swiper class="h-60" indicator-dots autoplay circular>
          <swiper-item v-for="(img, index) in productImages" :key="index">
            <view class="w100% h100% flex justify-center">
              <image :src="getImageUrl(img)" class="w-full h-full" mode="scaleToFill" />
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 商品基本信息 -->
      <view class="bg-white mb-2">
        <view class="price-info px3">
          <view class="text-[#ffffff]">
            <view class="">
              <text class="text-28rpx font-bold"></text>
              您需要承担
              <text
                class="ml20rpx h42rpx leading-42rpx px10rpx py5rpx bg-[#ffffff] rounded-[8rpx] text-24rpx text-[#FF5C23]"
              >
                {{ elevatorCount || 0 }}台电梯
              </text>
            </view>
            <view class="flex items-end">
              <text class="text-28rpx font-600">￥</text>
              <text class="text-48rpx font-600 leading-48rpx">{{ youNeedToPay || 0 }}</text>
              <text
                class="ml20rpx h40rpx leading-40rpx px10rpx bg-[#ffffff] rounded-[8rpx] text-24rpx text-[#FF5C23]"
              >
                已售{{ productDetail.sold || 0 }}
              </text>
            </view>
          </view>
          <view class="text-[#ffffff] text-right">
            <view class="text-28rpx font-600">电梯回收残值补贴采购</view>
            <view>
              <text class="text-28rpx font-600">￥</text>
              <text class="text-48rpx font-600">{{ productDetail.recycleValue || 0 }}</text>
            </view>
          </view>
        </view>
        <view class="px-3 mt3 flex items-center gap10rpx">
          <view class="bg-[#FBE5E8] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#F86E3E]">
            电梯总价￥{{ selectedSku.price }}
          </view>
          <view class="bg-[#FBE5E8] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#F86E3E]">
            国补每台电梯￥{{ guobuConfig.subsidiesPrice }}
          </view>
          <view class="bg-[#E6EEFF] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#006DC5]">
            本小区{{ productVoteCnt }}户选择
          </view>
        </view>

        <view class="px-3 mt2">
          <view class="content-wrapper">
            <view class="tag-wrapper" v-if="guobuConfig.remainCnt > 0">
              <view class="tag">国补</view>
            </view>
            <view class="text-content">{{ productDetail.name }}</view>
          </view>
        </view>
        <!-- <view class="flex items-center justify-between mb-3">
        <view class="text-2xl font-bold text-red-500">¥{{ selectedSku.price }}</view>
        <view class="text-sm text-gray-500">库存: {{ productDetail.totalStocks }}</view>
      </view> -->

        <view class="px-3 text-24rpx text-[#728190] pb10rpx">{{ productDetail.description }}</view>
      </view>

      <!-- 规格选择 -->
      <view class="bg-white p-4 mb-2" v-if="false">
        <view class="text-lg font-bold mb-3">规格选择</view>
        <view class="flex flex-wrap gap-2">
          <view
            v-for="sku in productDetail.skus"
            :key="sku.id"
            class="px-4 py-2 border rounded-lg text-sm"
            :class="
              selectedSku?.id === sku.id
                ? 'border-red-500 bg-red-50 text-red-500'
                : 'border-gray-300 text-gray-700'
            "
            @click="selectSkuChange(sku)"
          >
            {{ sku.name }}
          </view>
        </view>
      </view>

      <!-- 商品参数 -->
      <view class="bg-white p-4 mb-2">
        <view class="text-lg font-bold mb-3">商品参数</view>
        <view class="space-y-2">
          <view
            v-for="param in productDetail.parameters"
            :key="param.id"
            class="flex justify-between py-1 border-b border-gray-100"
          >
            <text class="text-gray-600">{{ param.parameterName }}</text>
            <text class="text-gray-800">
              {{
                param.parameterName === '楼层'
                  ? productDetail.floors
                  : param.parameterName === '型号'
                    ? productDetail.model
                    : param.parameterValue
              }}
            </text>
          </view>
        </view>
      </view>

      <!-- 商品详情 -->
      <view class="bg-white mb-2 w100% mb180rpx">
        <view class="text-lg mb-1 p-4 font-bold">商品详情</view>
        <view class="px-4">
          <rich-text :nodes="productDetail.content"></rich-text>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="w100% fixed bottom-0 left-0 z-[100]">
        <view class="bottom-zone px33rpx flex justify-end items-center h124rpx bg-[#ffffff]">
          <!-- <view class="text-28rpx font-bold">预计您需要承担</view>
        <text class="text-28rpx font-bold">￥{{ minSkuPrice }} ~ ￥{{ maxSkuPrice }}</text> -->
          <view class="w100%">
            <fui-button
              type="primary"
              height="84rpx"
              :size="32"
              radius="10rpx"
              text="选中投票"
              @click="openPopup"
            ></fui-button>
          </view>
        </view>
      </view>
    </template>

    <template v-else>
      <fui-empty
        :width="386"
        :height="280"
        src="/static/images/img_data_3x.png"
        isFixed
        title="暂无数据"
      ></fui-empty>
    </template>

    <fui-loading v-if="loading" isMask />
    <fui-bottom-popup :show="showPopup" @close="closePopup">
      <view class="fui-custom__wrap">
        <view class="py20rpx">
          <view class="relative mb20rpx">
            <view class="text-32rpx font-500 text-center">请选择规格</view>
            <view class="absolute top-0 right-43rpx" @click="closePopup">
              <fui-icon name="close" :size="42" color="#B2B2B2" />
            </view>
          </view>
          <view class="h2rpx bg-[#eee] w100%"></view>
          <view class="px30rpx mt20rpx">
            <view class="flex items-center">
              <view v-if="productDetail.brandLogo">
                <image
                  class="w180rpx h180rpx rounded-[12rpx]"
                  :src="getImageUrl(productDetail.brandLogo)"
                  mode="scaleToFill"
                />
              </view>
              <view class="flex-1 ml20rpx">
                <view class="text-26rpx">
                  <text class="text-28rpx font-bold">您需要承担</text>
                  <text class="text-28rpx text-[#FF5C23]">￥</text>
                  <text class="text-48rpx text-[#FF5C23]">{{ youNeedToPay }}</text>
                </view>
                <view class="mt10rpx">
                  <view class="flex items-center mt10rpx gap10rpx flex-wrap">
                    <view
                      class="bg-[#FBE5E8] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#F86E3E]"
                    >
                      电梯总价￥{{ selectedSku.price }}
                    </view>
                    <view
                      class="bg-[#FBE5E8] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#F86E3E]"
                    >
                      国补每台电梯￥{{ guobuConfig.subsidiesPrice }}
                    </view>
                    <view
                      class="bg-[#E6EEFF] text-24rpx rounded-[12rpx] px10rpx py10rpx text-[#006DC5]"
                    >
                      本小区{{ productVoteCnt }}户选择
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <view class="mt-2">
              <view class="text-lg font-bold mb-2">规格选择</view>
              <view class="flex flex-wrap gap-2">
                <view
                  v-for="sku in productDetail.skus"
                  :key="sku.id"
                  class="px-4 py-2 rounded-20rpx text-28rpx"
                  :class="
                    selectedSku?.id === sku.id
                      ? ' border  border-solid border-[#DB393D] bg-[#FFF1F4] text-[#DB393D]'
                      : 'bg-[#f5f5f5] text-[#666666]'
                  "
                  @click="selectSkuChange(sku)"
                >
                  {{ sku.name }}
                </view>
              </view>
            </view>
          </view>

          <view class="w100% flex justify-center fixed bottom-0 left-0 z-[100]">
            <view
              class="bottom-zone w100% px33rpx flex justify-end items-center h124rpx bg-[#ffffff]"
            >
              <view class="w100%">
                <fui-button
                  type="primary"
                  height="84rpx"
                  :size="32"
                  radius="10rpx"
                  text="选中投票"
                  @click="handleSelectVote"
                ></fui-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </fui-bottom-popup>
  </view>
</template>

<script lang="ts" setup>
import {
  getPrePrice,
  getProductDetail,
  queryElevatorCountByUnit,
  queryProductVoteCnt,
  querySubsidyPrice,
} from '@/service/app'
import { EventName } from '@/utils/const'
import { calculatePrePrice } from '@/utils/util'
import { onMounted, getCurrentInstance } from 'vue'
// 响应式数据
const loading = ref(false)
const productDetail = ref<any>({
  name: '',
  price: 0,
  description: '',
  content: '',
  pic: '',
  imgs: '',
  totalStocks: 0,
  skus: [],
  parameters: [],
})
const selectedSku = ref<any>({})
const showPopup = ref(false)

// 计算属性
const productImages = computed(() => {
  if (!productDetail.value.imgs) return []
  return productDetail.value.imgs.split(',').map((img: string) => img.trim())
})

const previewUrl = import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL

// 方法
const getImageUrl = (imgPath: string) => {
  if (imgPath?.startsWith('http')) return imgPath
  return `${import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL}${imgPath}`
}

const firstSku = computed(() => {
  return productDetail.value?.skus[0] || {}
})

// 您需要承担的费用
const youNeedToPay = computed(() => {
  return calculatePrePrice(
    selectedSku.value.price,
    formulaParams.value,
    productDetail.value.recycleValue,
  )
})

const selectSkuChange = (sku: any) => {
  console.log('selectSkuChange', sku)
  selectedSku.value = sku
}

// 打开底部弹窗
const openPopup = () => {
  showPopup.value = true
}

// 关闭底部弹窗
const closePopup = () => {
  showPopup.value = false
}

const handleSelectVote = async () => {
  if (!selectedSku.value) {
    uni.showToast({
      title: '请选择商品规格',
      icon: 'none',
    })
    return
  }

  await nextTick()
  // 这里返回上一页
  uni.navigateBack({
    delta: 1,
    success: () => {
      uni.$emit(EventName.VOTE_BRAND_SELECTED, {
        skuId: selectedSku.value.id,
        code: selectedSku.value.code,
        productId: productDetail.value.id,
        skuName: selectedSku.value.name,
      })
    },
    fail: (err) => {
      console.log('err', err)
    },
  })
}
const formatRichText = (html) => {
  let newContent = html
    .replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"')
    .replace(/\<div style=\"/gi, '<div style="width:100%;word-break:break-all;word-wrap:normal;')
    .replace(/\s{2,}/g, '')
  return newContent
}

const fetchProductDetail = async (id: string) => {
  try {
    loading.value = true
    const res = await getProductDetail({
      params: {
        id,
      },
    })

    if (res.code === 200 && res.result) {
      const content = formatRichText(res.result.content)
      productDetail.value = {
        ...res.result,
        content,
      }
      // 默认选择第一个规格
      if (res.result.skus && res.result.skus.length > 0) {
        selectedSku.value = res.result.skus[0]
      }
    } else {
      uni.showToast({
        title: res.message || '获取商品详情失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 获取商品得票数
const productVoteCnt = ref(0)
const fetchProductVoteCnt = async () => {
  try {
    const res = await queryProductVoteCnt({
      params: { supplierId: supplierId.value, voteId: voteId.value },
    })
    console.log('fetchProductVoteCnt', res)
    productVoteCnt.value = res.result
  } catch (error) {}
}
// 获取国补数据
const guobuConfig = ref({})
const fetchSubsidyPrice = async () => {
  try {
    const res = await querySubsidyPrice({})
    console.log('fetchSubsidyPrice', res)
    guobuConfig.value = res.result
  } catch (error) {}
}

// 获取预估价格
const formulaParams = ref({ reduction: 0, rate: 1 })
const fetchPrePrice = async () => {
  try {
    loading.value = true
    const res = await getPrePrice({
      params: {
        buildingId: buildingIds.value,
        voteId: voteId.value,
      },
    })
    formulaParams.value = res.result
    console.log('res', res)
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}
// 查询电梯户数
const elevatorCount = ref(0)
const fetchElevatorCount = async () => {
  try {
    const res = await queryElevatorCountByUnit({
      params: {
        buildingId: buildingIds.value,
      },
    })
    elevatorCount.value = res.result
  } catch (error) {}
}

// 生命周期
onLoad(
  (options: {
    id: string
    communityId: string
    buildingId: string
    supplierId: string
    voteId: string
  }) => {
    console.log('options', options)
    if (options.id) {
      communityId.value = options.communityId
      buildingIds.value = options.buildingId
      supplierId.value = options.supplierId
      voteId.value = options.voteId
      fetchProductDetail(options.id)
      fetchProductVoteCnt()
      fetchSubsidyPrice()
      fetchPrePrice()
      fetchElevatorCount()
    } else {
      // uni.showToast({
      //   title: '商品ID不能为空',
      //   icon: 'none',
      // })
    }
  },
)

const communityId = ref('')
const buildingIds = ref('')
const supplierId = ref('')
const voteId = ref('')

onMounted(() => {
  const instance = getCurrentInstance().proxy
  const eventChannel = instance?.getOpenerEventChannel()

  // eventChannel.on(EventName.PRODUCT_DETAIL_PAGE, function (data) {
  //   console.log('acceptDataFromOpenerPage', data)
  //   buildingIds.value = data
  // })
})
</script>

<style lang="scss" scoped>
// 自定义样式
.rich-text {
  line-height: 1.6;
}

// 底部安全区域
.bottom-zone {
  background-color: #ffffff;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.fui-custom__wrap {
  width: 100%;
  height: 720rpx;
}
.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 128rpx;
  background: linear-gradient(113deg, #ff5c23 53%, #ff9c1c 50%);
}

.content-wrapper {
  position: relative;
  overflow: hidden;
}

.tag-wrapper {
  float: left;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.tag {
  display: inline-block;
  padding: 0 12rpx;
  font-size: 24rpx;
  color: #ffffff;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background-color: #22b85e;
  border-radius: 8rpx;
}

.text-content {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
  text-align: justify;
}
</style>
