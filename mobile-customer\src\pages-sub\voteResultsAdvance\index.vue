<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '投票信息',
  },
}
</route>

<template>
  <view class="vote-results-advance">
    <view class="flex h100% items-center">
      <view class="mx30rpx p-20rpx bg-[#F9F4E1] rounded-20rpx">
        <view class="border-1 border-[#FACD91] border-solid rounded-20rpx p-20rpx">
          <view class="header">
            <text class="title">恭喜您</text>
          </view>

          <!-- 投票成功提示 -->
          <view class="success-tip">
            <text class="tip-text">
              您的楼栋已经有3/4的业主且3/4有效面积的业主参与了投票，同时投票内的业主超过2/3达成了共识，符合法定条件，投票结果已经揭晓。
            </text>
          </view>
          <view>
            <view class="text-28rpx text-[#333333]">本次投票获选的品牌是：</view>
            <view>
              <vote-notice-item :isShowIndex="false" :showBottomBorder="false" :brand="voteData" />
            </view>
          </view>

          <!-- 价格信息 -->
          <view class="price-info">
            <view class="price-item">
              <text class="price-desc">
                该电梯公司报价¥300000，国补报价¥150000，
                电梯回收执行报价¥3000，原定您需要承担¥3000，平台补贴报价¥311， 您最终承担金额：
                <text class="text-32rpx text-[#e74c3c]">￥2689。</text>
              </text>
            </view>
            <view class="price-item">
              <text class="price-desc">已经从您的维修基金账户中扣除</text>
              <text class="text-32rpx text-[#e74c3c]">¥2000</text>
              <text class="price-desc">
                ，您还需支付
                <text class="text-32rpx text-[#e74c3c]">¥689。</text>
              </text>
            </view>
            <view>
              <view class="flex gap-[20rpx] py20rpx">
                <fui-button
                  class="w50%"
                  height="84rpx"
                  radius="42rpx"
                  background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
                  border-width="0"
                  text="查看合约"
                ></fui-button>
                <fui-button
                  class="w50%"
                  height="84rpx"
                  radius="42rpx"
                  background="linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)"
                  border-width="0"
                  text="查看投票详情"
                  @click="handleVoteDetail"
                ></fui-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <fui-loading v-if="loading" isMask />
  </view>
</template>

<script lang="ts" setup>
import VoteNoticeItem from '@/pages-sub/voteNoticeDetail/components/voteNoticeItem.vue'
import { queryBizVoteInfoById } from '@/service/app'
const loading = ref(false)

const voteData = ref({})

// 获取投票结果
const fetchVoteResult = async (id) => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id,
        status: 2,
      },
    })
    voteData.value = {
      ...res.result,
      vote_rate: res.result.voteRate,
      vote_count: res.result.voteCount,
      brand_name: res.result.resultBrandName,
      brandLogo: res.result.resultBrandPic,
      skuName: res.result.skuName,
      product_name: res.result.resultProductName,
      pic_url: res.result.resultBrandPic,
    }
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

const handleVoteDetail = () => {
  uni.navigateTo({
    url: `/pages-sub/votehasEndResultDetail/index?id=${voteData.value.id}`,
  })
}


onLoad((options: { id: string }) => {
  fetchVoteResult(options.id)
})
</script>

<style lang="scss" scoped>
.vote-results-advance {
  height: 100vh;
  overflow-y: auto;
  background: linear-gradient(to bottom, #ec644d 0%, #f1a546 100%);
}
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;

  .celebrate-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 20rpx;
  }

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #e74c3c;
  }
}

.success-tip {
  text-indent: 2em;
  .tip-text {
    font-size: 28rpx;
    line-height: 1.6;
    display: block;
    margin-bottom: 20rpx;
    color: #e74c3c;
  }

  .question {
    font-size: 28rpx;
    color: #333333;
    font-weight: bold;
  }
}

.price-info {
  margin-bottom: 30rpx;

  .price-item {
    margin-bottom: 16rpx;
    text-indent: 2em;
    .price-desc {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.5;
    }

    .price-highlight {
      font-size: 28rpx;
      color: #e74c3c;
      font-weight: bold;
    }
  }
}
</style>
