<template>
  <view class="brand-item" :style="{ borderBottom: showBottomBorder ? '1px solid #eee' : 'none' }">
    <view class="flex">
      <view class="flex items-center w-[200rpx]">
        <!-- 排名数字 -->
        <view v-if="isShowIndex" class="rank-number" :class="'rank-' + (index + 1)">
          {{ index + 1 }}
        </view>

        <!-- 品牌LOGO -->
        <view class="ml-10rpx">
          <image class="brand-logo" :src="picUrl" mode="scaleToFill" />
        </view>
      </view>
      <view class="flex-1">
        <view class="flex items-center">
          <!-- 品牌标签 -->
          <view class="brand-tag orange-tag" :class="brand.tagType">{{ brand.brand_name }}</view>
          <!-- 品牌名称 -->
          <view class="brand-name">{{ brand.product_name }}</view>
        </view>
        <view class="flex gap-20rpx mb-20rpx" v-if="brand.skuName">
          <view class="check-item check-item-active">
            {{ brand.skuName }}
          </view>
        </view>
        <view class="flex" v-if="showSkuRate">
          <view class="orange-tag mb10rpx px8rpx rounded-10rpx py12rpx justify-center text-24rpx">
            {{ brand.skuCode_dictText || '5年维保' }}得票率：{{ brand.skuRate }}
          </view>
        </view>
        <!-- 投票进度条 -->
        <progress
          :percent="voteRateNumber"
          :stroke-width="12"
          :border-radius="15"
          activeColor="#0073FF"
        />
        <!-- 投票信息 -->
        <view class="vote-info">
          <text>本小区得票数: {{ brand.vote_count }}</text>
          <text>得票率: {{ brand.vote_rate }}</text>
        </view>
        <view class="my10rpx text-24rpx text-[#f69f2a]" v-if="showPrice">
          <text>您需要承担</text>
          <text>￥{{ brand.price }}</text>
        </view>
      </view>
    </view>
    <view class="" v-if="showMoreSku">
      <view class="flex gap-x-10rpx">
        <view
          v-show="itemShowMoreSku"
          class="flex-1 flex items-center orange-tag px8rpx rounded-10rpx py12rpx justify-center text-24rpx"
          v-for="item in brand.sku_list"
          :key="item.id"
        >
          <view>{{ item.sku_name || item.sku_Name || '5年维保' }}得票率：</view>
          <view>{{ item.rate }}</view>
        </view>
      </view>
      <view
        class="text-24rpx text-[#0073FF] flex items-center justify-center"
        :class="[itemShowMoreSku ? 'image-active' : 'image-no-active']"
        @click="handleShowMoreSku"
      >
        <image class="w-[48rpx] h-[48rpx]" src="/static/images/dt_007.png" mode="scaleToFill" />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { getImageUrl } from '@/utils/util'
import { storeToRefs } from 'pinia'

const props = defineProps({
  brand: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
  isShowIndex: {
    type: Boolean,
    default: true,
  },
  showBottomBorder: {
    type: Boolean,
    default: true,
  },
  showPrice: {
    type: Boolean,
    default: false,
  },
  showMoreSku: {
    type: Boolean,
    default: false,
  },
  // 是否显示sku投票率(投票结果页面)
  showSkuRate: {
    type: Boolean,
    default: false,
  },
})
// 当前品牌是否展开
const itemShowMoreSku = ref(props.brand?.showMoreSku || false)
const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const picUrl = computed(() => {
  console.log('props?.brand?.pic_url', props?.brand)
  // return getImageUrl(userInfo.value.token, props?.brand?.pic_url)
  return `${import.meta.env.VITE_FILE_PREVIEW_PUBLIC_URL}/${props?.brand?.pic_url}`
})

const voteRateNumber = computed(() => {
  // 字符串转换成数字
  // 如果vote_rate为- 则返回0
  if (props?.brand?.vote_rate === '-' || !props?.brand?.vote_rate) {
    return 0
  }
  console.log('props?.brand?.vote_rate', props?.brand?.vote_rate)
  return parseFloat(props?.brand?.vote_rate)
})

const handleShowMoreSku = () => {
  itemShowMoreSku.value = !itemShowMoreSku.value
}
</script>

<style lang="scss" scoped>
.brand-item {
  background-color: inherit;
  position: relative;
  padding: 20rpx 0 0 0;
  position: relative;
  // display: flex;
}

.rank-number {
  width: 46rpx;
  height: 46rpx;
  background-color: #ccc;
  color: #fff;
  text-align: center;
  border-radius: 50%;
}

.rank-1 {
  background-color: #e53935;
}

.rank-2 {
  background-color: #fb8c00;
}

.rank-3 {
  background-color: #43a047;
}

.brand-logo {
  width: 120rpx;
  height: 80rpx;
}

.brand-name {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  margin-left: 10rpx;
}

.brand-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.blue-tag {
  background-color: #e3f2fd;
  color: #1976d2;
}

.orange-tag {
  background-color: #fff3e0;
  color: #be3c00;
}

.vote-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0;
}
.check-item {
  background: #eee;
  color: #000;
  // width: 180rpx;
  padding: 12rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
  &-active {
    background: #fde8cd;
    color: #f69f2a;
  }
}
.image-no-active {
  transform: rotate(0deg);
  transition: transform 0.2s ease-in-out;
}
.image-active {
  transform: rotate(180deg);
  transition: transform 0.2s ease-in-out;
}
</style>
