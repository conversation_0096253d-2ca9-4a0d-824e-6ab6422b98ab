<layout-default-uni class="data-v-c7632889" u-s="{{['d']}}" u-i="c7632889-0" bind:__l="__l"><view class="height-no-tabbar data-v-c7632889"><view class=" data-v-c7632889"><image class="w100_a_ h600rpx data-v-c7632889" src="{{a}}" mode="aspectFill"/></view><view wx:if="{{false}}" class="w100_a_ mt60rpx data-v-c7632889"><view class="text-center text-32rpx text-32rpx text-bold data-v-c7632889">欢迎使用</view><view class="text-center text-_a__a_7F7F7F_a_ text-24rpx text-bold mt38rpx data-v-c7632889"> 立即登录，寻找优质房源 </view><view class="text-center text-_a__a_7F7F7F_a_ text-24rpx text-bold mt12rpx data-v-c7632889"> 信息真实可靠、找房看房入住快人一步 </view></view><view class="w100_a_ mt20rpx data-v-c7632889"><view wx:if="{{b}}" class="px50rpx data-v-c7632889"><fui-button wx:if="{{c}}" u-s="{{['d']}}" class="block data-v-c7632889" bindgetphonenumber="{{e}}" u-i="c7632889-1,c7632889-0" bind:__l="__l" u-p="{{f}}"><view class="flex items-center data-v-c7632889"><fui-icon wx:if="{{d}}" class="mr12rpx data-v-c7632889" u-i="c7632889-2,c7632889-1" bind:__l="__l" u-p="{{d}}"></fui-icon><text class="text-30rpx text-bold data-v-c7632889">一键登录</text></view></fui-button><fui-button wx:else u-s="{{['d']}}" class="block data-v-c7632889" bindclick="{{h}}" u-i="c7632889-3,c7632889-0" bind:__l="__l" u-p="{{i||''}}"><view class="flex items-center data-v-c7632889"><fui-icon wx:if="{{g}}" class="mr12rpx data-v-c7632889" u-i="c7632889-4,c7632889-3" bind:__l="__l" u-p="{{g}}"></fui-icon><text class="text-30rpx text-bold data-v-c7632889">一键登录</text></view></fui-button></view><view wx:if="{{j}}" class="data-v-c7632889"><fui-form wx:if="{{w}}" class="r data-v-c7632889" u-s="{{['d']}}" u-r="formRef" u-i="c7632889-5,c7632889-0" bind:__l="__l" u-p="{{w}}"><fui-form-item wx:if="{{m}}" class="data-v-c7632889" u-s="{{['d']}}" u-i="c7632889-6,c7632889-5" bind:__l="__l" u-p="{{m}}"><fui-input wx:if="{{l}}" class="data-v-c7632889" u-i="c7632889-7,c7632889-6" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"></fui-input></fui-form-item><fui-form-item wx:if="{{t}}" class="data-v-c7632889" u-s="{{['right','d']}}" u-i="c7632889-8,c7632889-5" bind:__l="__l" u-p="{{t}}"><fui-input wx:if="{{o}}" class="data-v-c7632889" u-i="c7632889-9,c7632889-8" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"></fui-input><fui-countdown-verify class="r data-v-c7632889" u-r="countdownRef" bindsend="{{q}}" bindend="{{r}}" u-i="c7632889-10,c7632889-8" bind:__l="__l" u-p="{{s}}" slot="right"></fui-countdown-verify></fui-form-item></fui-form><view class="px50rpx data-v-c7632889"><fui-button wx:if="{{z}}" u-s="{{['d']}}" class="block mt20rpx data-v-c7632889" bindclick="{{y}}" u-i="c7632889-11,c7632889-0" bind:__l="__l" u-p="{{z}}"><view class="flex items-center data-v-c7632889"><fui-icon wx:if="{{x}}" class="mr12rpx data-v-c7632889" u-i="c7632889-12,c7632889-11" bind:__l="__l" u-p="{{x}}"></fui-icon><text class="text-30rpx text-bold data-v-c7632889">登录</text></view></fui-button></view></view></view><view class="w100_a_ mt20rpx data-v-c7632889"><view class="flex justify-center items-center flex-wrap gap-y-2 px20rpx data-v-c7632889"><fui-checkbox-group wx:if="{{C}}" class="data-v-c7632889" u-s="{{['d']}}" u-i="c7632889-13,c7632889-0" bind:__l="__l" u-p="{{C}}"><view class="fui-list__item data-v-c7632889"><fui-label class="data-v-c7632889" u-s="{{['d']}}" u-i="c7632889-14,c7632889-13" bind:__l="__l"><view class="flex items-center data-v-c7632889"><fui-checkbox wx:if="{{B}}" class="flex items-center data-v-c7632889" bindchange="{{A}}" u-i="c7632889-15,c7632889-14" bind:__l="__l" u-p="{{B}}"></fui-checkbox><view class="pl16rpx text-24rpx text-_a__a_7F7F7F_a_ data-v-c7632889">登录代表您已同意</view></view></fui-label></view></fui-checkbox-group><view class="text-24rpx color-_a__a_ff9000_a_ data-v-c7632889" bindtap="{{D}}"> 《用户服务协议》 </view><view class="text-24rpx color-_a__a_ff9000_a_ data-v-c7632889" bindtap="{{E}}"> 《隐私权政策》 </view><view class="text-24rpx color-_a__a_ff9000_a_ data-v-c7632889" bindtap="{{F}}">《投票规则》</view></view><view wx:if="{{G}}" class="flex flex-col mt20rpx items-center data-v-c7632889"><view class="text-_a_666 text-26rpx data-v-c7632889">其它登录方式</view><view class="mt10rpx data-v-c7632889"><fui-icon wx:if="{{H}}" class="data-v-c7632889" bindclick="{{I}}" u-i="c7632889-16,c7632889-0" bind:__l="__l" u-p="{{J}}"></fui-icon><fui-icon wx:if="{{K}}" class="data-v-c7632889" bindclick="{{L}}" u-i="c7632889-17,c7632889-0" bind:__l="__l" u-p="{{M}}"></fui-icon></view></view></view><fui-loading wx:if="{{N}}" class="data-v-c7632889" u-i="c7632889-18,c7632889-0" bind:__l="__l" u-p="{{O}}"></fui-loading></view></layout-default-uni>