"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
require("../../store/index.js");
const utils_util = require("../../utils/util.js");
const utils_index = require("../../utils/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  const _easycom_fui_nav_bar2 = common_vendor.resolveComponent("fui-nav-bar");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  const _easycom_fui_tabs2 = common_vendor.resolveComponent("fui-tabs");
  const _easycom_fui_count_down2 = common_vendor.resolveComponent("fui-count-down");
  const _easycom_fui_swipe_action2 = common_vendor.resolveComponent("fui-swipe-action");
  const _easycom_fui_swipeaction_group2 = common_vendor.resolveComponent("fui-swipeaction-group");
  const _easycom_fui_landscape2 = common_vendor.resolveComponent("fui-landscape");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_icon2 + _easycom_fui_nav_bar2 + _easycom_fui_button2 + _easycom_fui_tag2 + _easycom_fui_tabs2 + _easycom_fui_count_down2 + _easycom_fui_swipe_action2 + _easycom_fui_swipeaction_group2 + _easycom_fui_landscape2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_icon = () => "../../components/firstui/fui-icon/fui-icon.js";
const _easycom_fui_nav_bar = () => "../../components/firstui/fui-nav-bar/fui-nav-bar.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_tag = () => "../../components/firstui/fui-tag/fui-tag.js";
const _easycom_fui_tabs = () => "../../components/firstui/fui-tabs/fui-tabs.js";
const _easycom_fui_count_down = () => "../../components/firstui/fui-count-down/fui-count-down.js";
const _easycom_fui_swipe_action = () => "../../components/firstui/fui-swipe-action/fui-swipe-action.js";
const _easycom_fui_swipeaction_group = () => "../../components/firstui/fui-swipeaction-group/fui-swipeaction-group.js";
const _easycom_fui_landscape = () => "../../components/firstui/fui-landscape/fui-landscape.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_icon + _easycom_fui_nav_bar + VoteHouseSelect + _easycom_fui_button + resultTitle + _easycom_fui_tag + _easycom_fui_tabs + _easycom_fui_count_down + TitleHeader + VoteBrandCheckItem + _easycom_fui_swipe_action + _easycom_fui_swipeaction_group + VoteNotice + _easycom_fui_landscape + _easycom_fui_loading)();
}
const VoteHouseSelect = () => "../../pages/vote/components/voteHouseSelect.js";
const VoteNotice = () => "../../pages/vote/components/voteNotice.js";
const resultTitle = () => "../../pages/vote/components/resultTip.js";
const VoteBrandCheckItem = () => "../vote/components/voteBrandCheckItem.js";
const TitleHeader = () => "../../components/common/titleHeader.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const loading = common_vendor.ref(false);
    const voteData = common_vendor.ref(null);
    const brands = common_vendor.ref([]);
    const isListenSort = common_vendor.ref(0);
    const selectedCount = common_vendor.computed(() => {
      return brands.value.filter((item) => item.isChecked).length || 1;
    });
    const id = common_vendor.ref("");
    const buildingId = common_vendor.ref("");
    const communityId = common_vendor.ref("");
    const voteHouseSelectRef = common_vendor.ref(null);
    const disableBtn = common_vendor.ref(true);
    const buildingListNew = common_vendor.ref([]);
    const showModal = common_vendor.ref(false);
    const closeModal = () => {
      disableBtn.value = true;
      showModal.value = false;
      clearTimeout();
    };
    const handleJumpMyVote = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/myVote/index"
      });
    };
    const handleJumpMessage = () => {
      common_vendor.index.switchTab({
        url: "/pages/message/index"
      });
    };
    const handleVoteNoticeOperation = () => {
      voteNoticeOperation(1);
      closeModal();
    };
    const handleExitVote = () => {
      voteNoticeOperation(0);
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    };
    const voteNoticeOperation = (_operationType) => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.addVoteNoticeOperation({
          body: {
            operationType: _operationType,
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
      } catch (error) {
        console.log("error", error);
      }
    });
    const fetchHasClickVoteNote = () => __async(this, null, function* () {
      var _a;
      try {
        const res = yield service_app_vote.getHasClickVoteNote({
          params: {
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
        if (((_a = res.result) == null ? void 0 : _a.length) === 0) {
          showModal.value = true;
          yield common_vendor.nextTick$1();
          startTimeInterval();
        } else {
          showModal.value = false;
        }
      } catch (error) {
        console.log("error", error);
      }
    });
    const fetchUserInfo = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      const res = yield userStore.checkUserInfo();
      console.log("fetchUserInfo", (_a = res == null ? void 0 : res.result) == null ? void 0 : _a.isRealAuth);
      yield common_vendor.nextTick$1();
      (_b = voteHouseSelectRef.value) == null ? void 0 : _b.closeDropdown();
      if (((_c = res.result) == null ? void 0 : _c.isRealAuth) === 1) {
        (_d = voteHouseSelectRef.value) == null ? void 0 : _d.fetchCommunityList();
      } else {
        common_vendor.index.navigateTo({
          url: "/pages-sub/realname/index"
        });
      }
    });
    let timeInterval = null;
    const timeCount = "1";
    const timeNum = common_vendor.ref(0);
    const startTimeInterval = (time = timeCount) => {
      if (timeInterval) {
        clearTimeout();
      }
      timeNum.value = time;
      timeInterval = setInterval(() => {
        timeNum.value = timeNum.value - 1;
        if (timeNum.value <= 0) {
          clearTimeout();
          disableBtn.value = false;
        }
      }, 1e3);
    };
    const clearTimeout = () => {
      if (timeInterval) {
        clearInterval(timeInterval);
        timeInterval = null;
        timeNum.value = 0;
      }
    };
    const allAddressList = common_vendor.ref([]);
    const fetchAddressList = () => __async(this, null, function* () {
      const res = yield service_app_vote.getAllGrantedAndSelfList({
        params: {
          communityId: communityId.value,
          buildingIds: buildingId.value
        }
      });
      allAddressList.value = res.result;
      if (res.result.length > 0)
        ;
    });
    const fetchVoteInfo = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value,
            buildingId: buildingId.value
          }
        });
        console.log("queryBizVoteInfoById", res);
        const voteEndSeconds = utils_util.calculateVoteEndSeconds(res.result.voteEndDate);
        voteData.value = __spreadProps(__spreadValues({}, res.result), {
          voteEndSeconds,
          voteRateValue: parseFloat(res.result.voteRate),
          voteAreaRateValue: parseFloat(res.result.voteAreaRate)
        });
        if (res.result.communityId) {
          communityId.value = res.result.communityId;
          fetchAddressList();
          yield fetchPrePrice();
        }
        if (launchId.value) {
          yield fetchBrandById(launchId.value);
          launchId.value = "";
        }
        fetchHasClickVoteNote();
      } catch (error) {
        console.log("error", error);
        voteData.value = null;
      } finally {
        loading.value = false;
      }
    });
    const handleHouseVoteSelectClick = (select) => __async(this, null, function* () {
      buildingListNew.value = [];
      brands.value = [];
      console.log("handleHouseVoteSelectClick", select);
      id.value = select.value;
      buildingId.value = select.buildingId;
      buildingListNew.value.push({
        name: `${(select == null ? void 0 : select.buildingNo) || "-"}幢`,
        value: select == null ? void 0 : select.buildingId
      });
      yield fetchVoteInfo();
    });
    const houseVoteResult = common_vendor.ref(0);
    const handleHouseVoteResult = (result) => {
      console.log("handleHouseVoteResult", result);
      houseVoteResult.value = result;
    };
    const formulaParams = common_vendor.ref({ reduction: 0, rate: 1 });
    const fetchPrePrice = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getPrePrice({
          params: {
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
        formulaParams.value = res.result;
        console.log("res", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchBrandById = (_supplierId) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.querySupplierByVoteId({
          params: {
            voteId: id.value,
            buildingId: buildingId.value,
            supplierId: _supplierId
          }
        });
        console.log("querySupplierByVoteId", res);
        if (!res.result || res.result.length === 0) {
          utils_util.showTextToast("没有获取到商品数据");
          return;
        }
        const hasExist = brands.value.find((item) => item.id === res.result[0].id);
        if (hasExist) {
          utils_util.showTextToast("已经添加过该商品");
          return;
        }
        const _currentLength = brands.value.length;
        isListenSort.value = 0;
        const _brands = res.result.map((item, index) => {
          var _a, _b, _c;
          return __spreadProps(__spreadValues({}, item), {
            voteCount: (_a = item.voteCount) != null ? _a : 0,
            voteRate: (_b = item.voteRate) != null ? _b : "0%",
            communityId: voteData.value.communityId,
            sortOrder: _currentLength + 1,
            isChecked: true,
            skus: ((_c = item == null ? void 0 : item.bizSkus) == null ? void 0 : _c.map((sku, index2) => {
              return __spreadProps(__spreadValues({}, sku), {
                checked: index2 === 0,
                skuPrice: sku.price,
                price: utils_util.calculatePrePrice(sku.price, formulaParams.value)
              });
            })) || []
          });
        }) || [];
        brands.value = [...brands.value, ..._brands];
        yield common_vendor.nextTick$1();
        isListenSort.value = 1;
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const allChecked = common_vendor.computed(() => {
      return brands.value.filter((item) => item.isChecked);
    });
    const allCheckedSkus = common_vendor.computed(() => {
      console.log("allChecked", allChecked.value);
      return allChecked.value.map((item) => {
        var _a;
        return ((_a = item == null ? void 0 : item.skus) == null ? void 0 : _a.find((sku) => sku.checked)) || {};
      });
    });
    const maxSkuPrice = common_vendor.computed(() => {
      if (allCheckedSkus.value.length <= 0) {
        return 0;
      }
      return Math.max(...allCheckedSkus.value.map((item) => item.price || 0));
    });
    const minSkuPrice = common_vendor.computed(() => {
      if (allCheckedSkus.value.length <= 0) {
        return 0;
      }
      return Math.min(...allCheckedSkus.value.map((item) => item.price || 0));
    });
    const changeVoteSort = (brand, currentSortOrder) => {
      console.log("changeVoteSort2222", brand, currentSortOrder);
      const targetItem = brands.value.find((item) => item.id === brand.id);
      if (!targetItem)
        return;
      const oldSort = targetItem.sortOrder;
      if (oldSort === currentSortOrder)
        return;
      if (oldSort < currentSortOrder) {
        brands.value.forEach((item) => {
          if (item.id !== targetItem.id && item.sortOrder > oldSort && item.sortOrder <= currentSortOrder) {
            console.log("item.sortOrder--", item.sortOrder);
            item.sortOrder--;
          }
        });
      } else {
        brands.value.forEach((item) => {
          if (item.id !== targetItem.id && item.sortOrder >= currentSortOrder && item.sortOrder < oldSort) {
            item.sortOrder++;
          }
        });
      }
      targetItem.sortOrder = currentSortOrder;
    };
    common_vendor.ref({});
    const handleImmediateVote = () => __async(this, null, function* () {
      if (brands.value.length < 3) {
        utils_util.showTextToast("请至少选择3个品牌");
        return;
      }
      const _voteParams = {
        voteId: id.value,
        buildingId: buildingId.value,
        method: 0,
        supplierList: allChecked.value.map((item) => {
          var _a;
          const checkedSkus = (_a = item == null ? void 0 : item.skus) == null ? void 0 : _a.find((sku) => sku.checked);
          console.log("checkedSkus", checkedSkus);
          console.log("item", item);
          return {
            supplierId: item.supplierId,
            sort: item.sortOrder,
            skuCode: checkedSkus == null ? void 0 : checkedSkus.code,
            productId: item.productId,
            productName: item.productName
          };
        })
      };
      console.log("_voteParams", _voteParams);
      console.log("allChecked.value", allChecked.value);
      try {
        loading.value = true;
        const res = yield service_app_vote.voteByUser({
          body: _voteParams
        });
        console.log("res", res);
        utils_util.showTextToast(res.message || "投票成功");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/welcome/index"
          });
        }, 1e3);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const onSwipeClick = (e, item) => {
      console.log("onSwipeClick", e);
      console.log("onSwipeClick", item);
      common_vendor.index.showModal({
        title: "提示",
        content: "确认删除？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            brands.value.splice(e.index, 1);
          } else if (res.cancel) {
            console.log("用户点击了取消");
          }
        })
      });
    };
    common_vendor.onHide(() => __async(this, null, function* () {
      isScanCode.value = true;
    }));
    common_vendor.onShow(() => __async(this, null, function* () {
      if (isScanCode.value) {
        isScanCode.value = false;
        return;
      }
      yield userStore.checkUserInfo();
      yield common_vendor.nextTick$1();
      id.value = "";
      voteData.value = null;
      buildingId.value = "";
      communityId.value = "";
      buildingListNew.value = [];
      brands.value = [];
      clearTimeout();
      console.log("isLogined", isLogined.value);
      console.log("isScanCode", isScanCode.value);
      if (!isLogined.value) {
        common_vendor.index.navigateTo({
          url: `/pages-sub/mine/login/index`
        });
      } else {
        yield fetchUserInfo();
      }
    }));
    const launchId = common_vendor.ref(null);
    common_vendor.onLoad(() => __async(this, null, function* () {
      var _a, _b;
      const launchOptions = yield common_vendor.index.getLaunchOptionsSync();
      console.log("getLaunchOptions", launchOptions);
      console.log("getLaunchOptions--scene", (_a = launchOptions == null ? void 0 : launchOptions.query) == null ? void 0 : _a.scene);
      launchId.value = (_b = launchOptions == null ? void 0 : launchOptions.query) == null ? void 0 : _b.scene;
      console.log("launchId", launchId.value);
    }));
    const isScanCode = common_vendor.ref(false);
    const handleScanCode = () => {
      isScanCode.value = true;
      common_vendor.index.scanCode({
        success: function(res) {
          console.log("res", res);
          const path = res.path;
          const { query } = utils_index.getUrlObj(path);
          console.log("query", query);
          if (query.scene) {
            fetchBrandById(query.scene);
          } else {
            utils_util.showTextToast("没有获取到正确的商品条码");
          }
        },
        fail: function(err) {
          console.log("err", err);
          if (err.errMsg === "scanCode:fail cancel")
            return;
          utils_util.showTextToast("扫码失败");
        },
        complete: function(res) {
          console.log("isScanCode", isScanCode.value);
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          name: "scan",
          size: 40,
          color: "#145DB2"
        }),
        b: common_vendor.o(handleScanCode),
        c: common_vendor.p({
          title: "扫码投票",
          splitLine: true,
          size: 15,
          isFixed: true,
          isOccupy: true
        }),
        d: common_vendor.sr(voteHouseSelectRef, "8bbc5b06-3,8bbc5b06-0", {
          "k": "voteHouseSelectRef"
        }),
        e: common_vendor.o(handleHouseVoteSelectClick),
        f: common_vendor.o(handleHouseVoteResult),
        g: !common_vendor.unref(voteData)
      }, !common_vendor.unref(voteData) ? common_vendor.e({
        h: common_vendor.unref(houseVoteResult) === 1
      }, common_vendor.unref(houseVoteResult) === 1 ? {
        i: common_vendor.o(handleJumpMyVote),
        j: common_vendor.p({
          ["btn-size"]: "medium",
          text: "查看投票",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        }),
        k: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : common_vendor.unref(houseVoteResult) === 2 ? {
        m: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : common_vendor.unref(houseVoteResult) === 3 ? {
        o: common_vendor.o(handleJumpMessage),
        p: common_vendor.p({
          ["btn-size"]: "medium",
          text: "查看消息",
          type: "primary",
          bold: true,
          margin: ["48rpx", "0", "24rpx"]
        }),
        q: common_vendor.p({
          imgUrl: "/static/images/img_data_3x.png"
        })
      } : {}, {
        l: common_vendor.unref(houseVoteResult) === 2,
        n: common_vendor.unref(houseVoteResult) === 3
      }) : common_vendor.e({
        r: common_vendor.t(common_vendor.unref(userInfo).realname),
        s: common_vendor.t(common_vendor.unref(voteData).communityName),
        t: common_vendor.t(common_vendor.unref(voteData).buildingNo),
        v: common_vendor.f(common_vendor.unref(allAddressList), (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.location),
            b: item.myself == 1
          }, item.myself == 1 ? {
            c: "8bbc5b06-9-" + i0 + ",8bbc5b06-0",
            d: common_vendor.p({
              text: "被授权",
              type: "success",
              padding: ["6rpx", "6rpx"]
            })
          } : {}, {
            e: index
          });
        }),
        w: common_vendor.t(common_vendor.unref(voteData).voteEndDate),
        x: common_vendor.p({
          tabs: common_vendor.unref(buildingListNew),
          center: true,
          selectedColor: "#005ED1",
          sliderBackground: "#005ED1"
        }),
        y: common_vendor.unref(voteData).voteRateValue,
        z: common_vendor.t(common_vendor.unref(voteData).voteCount),
        A: common_vendor.t(common_vendor.unref(voteData).voteRate),
        B: common_vendor.p({
          isDays: true,
          isColon: false,
          size: "42",
          width: "50",
          height: "50",
          borderColor: "transparent",
          background: "transparent",
          color: "#005ED1",
          colonColor: "#005ED1",
          value: common_vendor.unref(voteData).voteEndSeconds
        }),
        C: common_vendor.p({
          title: "请扫码添加3个品牌进行投票"
        }),
        D: common_vendor.unref(brands).length > 0
      }, common_vendor.unref(brands).length > 0 ? {
        E: common_vendor.f(common_vendor.unref(brands), (brand, index, i0) => {
          return {
            a: common_vendor.o(changeVoteSort, index),
            b: "8bbc5b06-15-" + i0 + "," + ("8bbc5b06-14-" + i0),
            c: common_vendor.p({
              showCheckBox: false,
              brand,
              index,
              selectedCount: common_vendor.unref(selectedCount),
              isListenSort: common_vendor.unref(isListenSort),
              buildingIds: common_vendor.unref(buildingId),
              voteId: common_vendor.unref(id)
            }),
            d: index,
            e: common_vendor.o(($event) => onSwipeClick($event, brand), index),
            f: index,
            g: "8bbc5b06-14-" + i0 + ",8bbc5b06-13"
          };
        })
      } : {}), {
        F: common_vendor.t(common_vendor.unref(minSkuPrice)),
        G: common_vendor.t(common_vendor.unref(maxSkuPrice)),
        H: common_vendor.o(handleImmediateVote),
        I: common_vendor.p({
          type: "primary",
          width: "184rpx",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "立即投票"
        }),
        J: common_vendor.unref(brands).length > 0,
        K: common_vendor.unref(userInfo).isRealAuth,
        L: common_assets._imports_0$4,
        M: common_vendor.p({
          height: "50vh"
        }),
        N: common_vendor.unref(timeNum) > 0
      }, common_vendor.unref(timeNum) > 0 ? {
        O: common_vendor.t(common_vendor.unref(timeNum))
      } : {}, {
        P: common_vendor.o(handleVoteNoticeOperation),
        Q: common_vendor.p({
          radius: "100rpx",
          width: "100%",
          disabled: common_vendor.unref(disableBtn),
          ["disabled-background"]: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          borderColor: "rgba(0,0,0,0)",
          ["border-width"]: "0"
        }),
        R: common_vendor.o(handleExitVote),
        S: common_vendor.p({
          radius: "100rpx",
          background: "#fff",
          color: "#000000",
          borderColor: "#ffffff",
          text: "退出投票"
        }),
        T: common_vendor.o(closeModal),
        U: common_vendor.p({
          show: common_vendor.unref(showModal),
          closable: false
        }),
        V: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        W: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8bbc5b06"]]);
wx.createPage(MiniProgramPage);
