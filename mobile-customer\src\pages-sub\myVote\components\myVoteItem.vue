<template>
  <view class="bg-[#ffffff]" @click="handleClick">
    <view class="flex justify-between items-center h100%">
      <view class="w-260rpx self-stretch">
        <image class="w100% h100%" src="/static/images/dt_001.jpg" mode="scaleToFill" />
      </view>

      <view class="flex-1 flex flex-col justify-between p20rpx gap-y-10rpx">
        <view class="text-28rpx">
          {{ item.communityName }}{{ item.buildingNo ? `${item.buildingNo}幢` : '' }}
        </view>
        <view class="row-zone flex items-center justify-between">
          <view>可投票户数：{{ item.totalCount }}</view>
          <view>已投票户数：{{ item.voteCount }}</view>
        </view>
        <view class="row-zone text-[#1296db]">
          <view>投票截止日期：{{ item.voteEndDate }}</view>
        </view>
        <view class="row-zone">
          <view>投票时间：{{ item.voteTime }}</view>
        </view>
        <view class="row-zone">
          <view>投票人：{{ item.voteUserName }}</view>
        </view>
        <view class="flex">
          <fui-tag
            type="success"
            background="#aaaaaa"
            :padding="['8rpx', '16rpx']"
            color="#fff"
            text="已结束"
            v-if="item.status > 1"
          />
          <fui-tag v-else type="success" text="已投票" :padding="['8rpx', '16rpx']" />
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const handleClick = () => {
  // todo 根据投票状态 跳转到投票结果详情页
  if (props.item.status === 1) {
    uni.navigateTo({
      url: `/pages-sub/voteResultDetail/index?id=${props.item.id}&buildingId=${props.item.buildingId}`,
    })
  }
  // if (props.item.status === 2) {
  //   uni.navigateTo({
  //     url: `/pages-sub/voteResultsAdvance/index?id=${props.item.id}`,
  //   })
  // }
  if (props.item.status >= 2) {
    uni.navigateTo({
      url: `/pages-sub/votehasEndResultDetail/index?id=${props.item.id}`,
    })
  }

  // 已结束投票
  //   uni.navigateTo({
  //     url: `/pages-sub/votehasEndResultDetail/index?id=${props.item.id}`,
  //   })
}
</script>

<style lang="scss" scoped>
.row-zone {
  font-size: 24rpx;
  .label-zone: {
  }
  .content-zone: {
  }
}
</style>
