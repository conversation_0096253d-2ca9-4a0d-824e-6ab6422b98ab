<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '公告详情',
  },
}
</route>

<template>
  <view class="container">
    <view v-if="voteInfo.id">
      <!-- 标题和基本信息 -->
      <view class="header mt20rpx">
        <view class="title">
          {{ voteInfo.communityName }}{{ voteInfo.buildingNo || '' }}幢电梯更新投票正在火热进行中
        </view>
        <view class="address">地址: {{ voteInfo.communityLocation }}</view>
        <view class="deadline">投票截止日期: {{ voteInfo.voteEndDate }}</view>

        <!-- 投票进度条 -->
        <view class="progress-wrapper">
          <progress
            :percent="voteInfo.voteRateValue"
            :stroke-width="15"
            :border-radius="15"
            activeColor="#0ABF4F"
          />
          <view class="progress-info">
            <text>小区投票数: {{ voteInfo.voteCount }}</text>
            <text>投票率: {{ voteInfo.voteRate }}</text>
          </view>
        </view>
      </view>

      <!-- 楼栋投票 -->
      <view class="bg-[#ffffff] px-30rpx pb-10rpx my20rpx">
        <fui-tabs
          :tabs="buildingList"
          center
          selectedColor="#005ED1"
          sliderBackground="#005ED1"
          @change="handleBuildingChange"
        ></fui-tabs>
        <!-- 投票进度条 -->
        <view class="my20rpx">
          <progress
            :percent="buildVoteInfo.voteRateValue"
            :stroke-width="12"
            :border-radius="15"
            activeColor="#05D6A2"
          />
          <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
            <text>本楼栋票数: {{ buildVoteInfo.voteCount }}</text>
            <text>投票率: {{ buildVoteInfo.voteRate }}</text>
          </view>
        </view>

        <view class="my20rpx">
          <progress
            :percent="buildVoteInfo.voteAreaRateValue"
            :stroke-width="12"
            :border-radius="15"
            activeColor="#FFAE00"
          />
          <view class="flex justify-between items-center text-26rpx text-[#666] mt10rpx">
            <text>本楼栋投票面积: {{ buildVoteInfo.voteArea }}m²</text>
            <text>投票率: {{ buildVoteInfo.voteAreaRate }}</text>
          </view>
        </view>

        <view class="text24rpx font-bold alert-info">
          <text class="alert-info-text">
            {{
              !buildVoteInfo.pass
                ? '本楼栋暂未达到有效选票数，请尽快投票。'
                : '本楼栋已达到有效选票数。'
            }}
          </text>
        </view>
      </view>

      <!-- 倒计时 -->
      <view class="countdown">
        <text>结束倒计时:</text>
        <view class="time-blocks">
          <fui-count-down
            isDays
            :isColon="false"
            size="42"
            width="50"
            height="50"
            borderColor="transparent"
            background="transparent"
            color="#005ED1"
            colonColor="#005ED1"
            :value="voteInfo.voteEndSeconds"
          ></fui-count-down>
        </view>
      </view>
      <!-- 小喇叭图标和TOP10品牌标题 -->

      <TitleHeader title="小区投票品牌当前排名" />

      <!-- 投票排名列表 -->
      <view class="brand-list mb180rpx" v-if="brands.length > 0">
        <view class="px30rpx bg-[#ffffff]" v-for="(brand, index) in brands" :key="index">
          <VoteNoticeItem :brand="brand" :index="index" :show-more-sku="true" />
        </view>

        <!-- 展开/收起控制按钮 -->
        <!-- <view class="toggle-btn" @click="toggleList">
          <text>{{ isExpanded ? '收起' : '展开' }}</text>
          <view class="arrow-icon" :class="{ 'rotate': isExpanded }">
            <fui-icon name="arrow-down" size="24rpx" color="#999999"></fui-icon>
          </view>
        </view> -->
      </view>
      <view v-else>
        <fui-empty
          src="/static/images/img_data_3x.png"
          :width="386"
          :height="280"
          title="暂无数据"
        />
      </view>

      <view class="fixed bottom-40rpx left-0 right-0 px80rpx" v-if="canVote">
        <fui-button
          class="w50%"
          height="84rpx"
          radius="42rpx"
          background="linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%);"
          border-width="0"
          @click="handleVote"
          text="我是业主我要投票"
        ></fui-button>
      </view>
    </view>
    <fui-loading v-if="loading" isMask></fui-loading>
  </view>
</template>

<script lang="ts" setup>
import {
  getBuildingRateByVote,
  getCanVote,
  getVoteRankProductTop10,
  queryBizVoteInfoById,
} from '@/service/app'
import VoteNoticeItem from './components/voteNoticeItem.vue'
import TitleHeader from '@/components/common/titleHeader.vue'
import dayjs from 'dayjs'
import { calculateVoteEndSeconds } from '@/utils/util'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

const voteInfo = ref({})

const brands = ref([])

const loading = ref(false)

const handleVote = () => {
  uni.switchTab({
    url: '/pages/vote/index',
  })
}

const userStore = useUserStore()

const { isLogined } = storeToRefs(userStore)

const id = ref('')
const communityId = ref('')
const isExpanded = ref(false)

const buildingList = ref([])

// 获取投票公告详情
const fetchVoteInfo = async () => {
  try {
    loading.value = true
    const res = await queryBizVoteInfoById({
      params: {
        id: id.value,
      },
    })
    console.log('queryBizVoteInfoById', res)
    // 计算倒计时

    // res.result.voteEndDate转换成带时分秒的 23：59：59
    const voteEndDate = dayjs(res.result.voteEndDate).format('YYYY-MM-DD 23:59:59')
    console.log('dayjs', dayjs(voteEndDate).diff(dayjs(), 'second'))
    const voteEndSeconds = dayjs(voteEndDate).diff(dayjs(), 'second')

    console.log('voteEndSeconds', calculateVoteEndSeconds(res.result.voteEndDate))

    voteInfo.value = {
      ...res.result,
      voteEndSeconds,
      voteRateValue: parseFloat(res.result.voteRate),
    }
    if (res.result.buildingCodes || res.result.buildingNo) {
      const _buildingCodesArray = res.result.buildingCodes.split(',')
      const _buildingNoArray = res.result.buildingNo.split(',')
      buildingList.value = _buildingNoArray.map((item, index) => {
        return {
          name: `${item}幢`,
          buildingId: _buildingCodesArray?.[index] || '',
          value: _buildingCodesArray[index],
        }
      })
      // 默认选中第一个
      handleBuildingChange(buildingList.value[0])
    }
    if (res.result.communityId) {
      communityId.value = res.result.communityId
      // 获取地址列表
      // fetchAddressList(res.result.buildingCodes)
    }
  } catch (error) {
    console.log('error', error)
  } finally {
    loading.value = false
  }
}
// 获取小区top10 品牌
const fetchTop10Brand = async () => {
  try {
    const res = await getVoteRankProductTop10({
      params: {
        voteId: id.value,
      },
    })
    console.log('queryTop10Brand', res)
    brands.value = res.result
  } catch (error) {
    console.log('error', error)
  }
}

// 获取用户是否有投票权限
const canVote = ref(false)
const getVoteAuth = async () => {
  if (!isLogined.value) {
    return
  }
  try {
    const res = await getCanVote({
      params: {
        voteId: id.value,
      },
    })
    canVote.value = res.result || false
  } catch (error) {}
}

// 切换楼栋
const handleBuildingChange = async (e) => {
  const { index, buildingId } = e
  console.log('handleBuildingChange', e)
  // 获取栋投票数及投票率
  fetchBuildingRateByVote(buildingId)
}

// 获取栋投票数及投票率
const buildVoteInfo = ref({})
const fetchBuildingRateByVote = async (buildingId: string) => {
  try {
    loading.value = true
    const res = await getBuildingRateByVote({
      params: {
        voteId: id.value,
        buildingIds: buildingId,
      },
    })
    if (res.result && res.result.length > 0) {
      const _buildVoteInfo = res.result[0]
      buildVoteInfo.value = {
        ..._buildVoteInfo,
        voteRateValue: parseFloat(_buildVoteInfo.voteRate),
        voteAreaRateValue: parseFloat(_buildVoteInfo.voteAreaRate),
      }
    } else {
      buildVoteInfo.value = {}
    }
  } catch (error) {
    console.log('error', error)
    buildVoteInfo.value = {}
  } finally {
    loading.value = false
  }
}

onLoad((option: { id: string; communityId: string }) => {
  id.value = option.id
  communityId.value = option.communityId
  fetchVoteInfo()
  fetchTop10Brand()
  getVoteAuth()
})

// 可以设置默认显示的数量
const defaultShowCount = ref(1)

// 计算当前可见的品牌列表
const visibleBrands = computed(() => {
  if (isExpanded.value) {
    return brands.value
  }
  return brands.value.slice(0, defaultShowCount.value)
})

// 切换展开/收起状态
const toggleList = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f7f7f7;
  height: 100vh;
  overflow-y: auto;
}

.header {
  background-color: #ffffff;
  padding: 30rpx 30rpx 10rpx 30rpx;
}

.title {
  // text-indent: 2em;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 32rpx;
  color: #000000;
  margin: 0rpx 0 10rpx 0;
}

.address,
.deadline {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.deadline {
  color: #1296db;
}

.progress-wrapper {
  margin: 20rpx 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin: 10rpx 0;
}

.countdown {
  margin: 30rpx 0 0 0;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  padding: 30rpx;
  background-color: #ffffff;
}

.time-blocks {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  color: #005ed1;
}

.time-block {
  font-size: 38rpx;
  margin: 0 10rpx;
  font-weight: bold;
}

.bottom-zone {
  position: fixed;
  background-color: #ffffff;
  bottom: 0;
  width: 100%;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.alert-info {
  width: 689rpx;
  height: 54rpx;
  background: #fdf4f4;
  border-radius: 10rpx;
  margin-bottom: 20rpx;

  .alert-info-text {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #ff2b2b;
    line-height: 24rpx;
    margin-left: 20rpx;
  }
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #ffffff;
  color: #666666;
  font-size: 26rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  .arrow-icon {
    margin-left: 10rpx;
    transition: transform 0.3s ease;
  }

  // 箭头旋转效果
  .rotate {
    transform: rotate(180deg);
  }
}
</style>
