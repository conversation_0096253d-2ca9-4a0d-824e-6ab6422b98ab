"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "fui-table",
  emits: ["click", "rowClick", "tdClick", "selectionChange", "select", "selectAll", "sortChange", "scrolltolower"],
  // components:{
  // 	fuiIcon
  // },
  props: {
    header: {
      type: Array,
      default() {
        return [];
      }
    },
    show: {
      type: Boolean,
      default: true
    },
    size: {
      type: [Number, String],
      default: 28
    },
    color: {
      type: String,
      default: "#7F7F7F"
    },
    fontWeight: {
      type: [Number, String],
      default: 600
    },
    headerBgColor: {
      type: String,
      default: "#fff"
    },
    fixed: {
      type: Boolean,
      default: false
    },
    //数据集合
    itemList: {
      type: Array,
      default() {
        return [];
      }
    },
    //总宽度 < 屏幕宽度- gap*2时，是否铺满
    full: {
      type: Boolean,
      default: false
    },
    //Table 的高度，默认为自动高度，单位rpx。
    height: {
      type: [Number, String],
      default: 0
    },
    //组件外层设置的左右padding值（距离屏幕左右侧距离），rpx
    gap: {
      type: [Number, String],
      default: 0
    },
    //是否带有纵向边框
    border: {
      type: Boolean,
      default: true
    },
    //是否带有横向边框
    horBorder: {
      type: Boolean,
      default: true
    },
    //边框颜色
    borderColor: {
      type: String,
      default: "#eee"
    },
    //如果有固定项，不可设置透明
    background: {
      type: String,
      default: "#fff"
    },
    // 是否为斑马纹table
    stripe: {
      type: Boolean,
      default: false
    },
    //斑马纹颜色
    stripeColor: {
      type: String,
      default: "#F8F8F8"
    },
    textSize: {
      type: [Number, String],
      default: 28
    },
    textColor: {
      type: String,
      default: "#333"
    },
    //单元格对齐方式:left/center/right
    align: {
      type: String,
      default: "center"
    },
    //文字超出是否省略，默认换行
    ellipsis: {
      type: Boolean,
      default: false
    },
    //单元格上下padding值，单位rpx
    padding: {
      type: [Number, String],
      default: 20
    },
    //是否添加多选框
    selection: {
      type: Boolean,
      default: false
    },
    initEmitChange: {
      type: Boolean,
      default: false
    },
    //选择框选中后颜色
    checkboxColor: {
      type: String,
      default: ""
    },
    checkboxBorderColor: {
      type: String,
      default: "#eee"
    },
    checkmarkColor: {
      type: String,
      default: "#fff"
    },
    //V2.1.0+
    emptyText: {
      type: String,
      default: "暂无数据"
    },
    emptySize: {
      type: [String, Number],
      default: 24
    },
    emptyColor: {
      type: String,
      default: "#B2B2B2"
    },
    bounces: {
      type: Boolean,
      default: false
    },
    showScrollbar: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    header(vals) {
      this.handleHeader(vals);
    },
    itemList(vals) {
      this.handleData(vals);
    },
    selection(vals) {
      this.handleData(this.itemList);
    }
  },
  computed: {
    getCheckboxColor() {
      let color = this.checkboxColor;
      return color;
    }
  },
  data() {
    let isNvue = false;
    return {
      width: 0,
      //列宽度需要加上此值
      divideW: 0,
      hData: [],
      tableData: [],
      initTableData: [],
      totalW: 0,
      isNvue,
      scrollH: 0,
      chkAll: false
    };
  },
  created() {
    this.handleHeader(this.header);
    this.handleData(this.itemList);
  },
  methods: {
    getPx(value) {
      let val = parseInt(common_vendor.index.upx2px(Number(value)));
      return val % 2 === 0 ? val : val + 1;
    },
    getId(index) {
      return `${index}_tr_${Math.ceil(Math.random() * 1e6).toString(36)}`;
    },
    handleHeader(header) {
      if (!header || header.length === 0)
        return;
      let vals = JSON.parse(JSON.stringify(header));
      if (this.selection) {
        vals.unshift({
          fixed: true,
          width: 100,
          type: "selection"
        });
      }
      let winWidth = common_vendor.index.getSystemInfoSync().windowWidth;
      let width = 0, left = 0, right = 0;
      let len = vals.length;
      vals.map((item, index) => {
        item.tdId = this.getId(index);
        item.width = this.getPx(item.width || 200);
        width += item.width;
        if (item.fixed) {
          item.left = item.fixed !== "right" ? left : 0;
          left += item.width;
        }
        if (item.type === 3 && item.buttons) {
          item.buttons.map((btn, idx) => {
            btn.bId = this.getId(index);
          });
        }
        if (!item.sort) {
          item.sort = "";
        }
      });
      for (let i = 0; i < len; i++) {
        let item = vals[len - i - 1];
        if (item && item.fixed) {
          item.right = item.fixed === "right" ? right : 0;
          right += item.width;
        }
      }
      let gap = this.gap == 0 ? 0 : this.getPx(Number(this.gap) * 2);
      this.totalW = width;
      let totalWidth = winWidth - gap;
      this.width = width > totalWidth ? totalWidth : width;
      if (this.full && totalWidth > this.width) {
        this.divideW = Math.floor((totalWidth - this.width) / len);
        let lastW = (totalWidth - this.width) % len;
        let item = vals[len - 1];
        item.width += lastW;
        let dw = this.divideW * len + lastW;
        this.width += dw;
        this.totalW += dw;
      }
      this.hData = vals;
    },
    handleData(vals) {
      if (!vals) {
        vals = [];
      }
      let table = JSON.parse(JSON.stringify(vals));
      table.map((item) => {
        item.is_disabled = item.is_disabled || false;
        item.is_selected = item.is_selected || false;
      });
      this.tableData = table;
      this.initTableData = JSON.parse(JSON.stringify(table));
      if (this.initEmitChange) {
        this.emitSelectionChange();
      }
    },
    handleTap(index, j) {
      let item = this.tableData[index];
      this.$emit("click", {
        item,
        index,
        buttonIndex: j
      });
    },
    tdClick(index, idx, prop) {
      let item = this.tableData[index];
      this.$emit("rowClick", {
        item,
        index
      });
      this.$emit("tdClick", {
        rowIndex: index,
        tdIndex: idx,
        prop
      });
    },
    getColColor(model, value, index, idx) {
      let color = "";
      if (model.fn && typeof model.fn === "function") {
        color = model.fn(value, index, idx);
      }
      return color || model.textColor || this.textColor;
    },
    columnColorMethod(fn, prop) {
      if (!fn || !prop)
        return;
      const data = this.hData;
      const index = data.findIndex((item) => item.prop === prop);
      if (index !== -1) {
        let item = data[index];
        item.fn = fn;
        this.hData = data;
      }
    },
    selectionAll() {
      if (this.chkAll) {
        this.chkAll = false;
        this.tableData.map((item) => {
          if (!item.is_disabled) {
            item.is_selected = false;
          }
        });
      } else {
        this.chkAll = true;
        this.tableData.map((item) => {
          if (!item.is_disabled) {
            item.is_selected = true;
          }
        });
      }
      this.$emit("selectAll", {
        is_selected: this.chkAll
      });
      setTimeout(() => {
        this.emitSelectionChange();
      }, 0);
    },
    emitSelectionChange() {
      const itemList = this.tableData.filter((item) => item.is_selected === true && item.is_disabled !== true);
      let data = JSON.parse(JSON.stringify(itemList));
      data.forEach((item) => {
        delete item.is_selected;
        delete item.is_disabled;
      });
      this.$emit("selectionChange", data);
    },
    checkSelectionAll() {
      if (!this.tableData || this.tableData.length === 0)
        return;
      const index = this.tableData.findIndex((item) => item.is_selected === false && item.is_disabled !== true);
      if (~index) {
        this.chkAll = false;
      } else {
        this.chkAll = true;
      }
      setTimeout(() => {
        this.emitSelectionChange();
      }, 0);
    },
    selectionChange(index, selected) {
      const item = this.tableData[index];
      if (item.is_disabled)
        return;
      if (selected === void 0 || selected === null) {
        item.is_selected = !item.is_selected;
      } else {
        item.is_selected = selected;
      }
      this.$emit("select", {
        is_selected: item.is_selected,
        item,
        index
      });
      this.checkSelectionAll();
    },
    //用于多选表格，清空用户的选择
    clearSelection() {
      this.chkAll = false;
      this.tableData.map((item) => {
        if (!item.is_disabled) {
          item.is_selected = false;
        }
      });
    },
    getRowIndex(row) {
      if (!row)
        return -1;
      const len = this.itemList.length;
      let index = -1;
      for (let i = 0; i < len; i++) {
        const item = this.itemList[i];
        if (JSON.stringify(item) === JSON.stringify(row)) {
          index = i;
          break;
        }
      }
      return index;
    },
    toggleRowSelection(row, selected) {
      const index = this.getRowIndex(row);
      if (index !== -1) {
        this.selectionChange(index, selected);
      }
    },
    toggleRowDisabled(row, disabled) {
      const index = this.getRowIndex(row);
      if (index !== -1) {
        const item = this.tableData[index];
        if (disabled === void 0 || disabled === null) {
          item.is_disabled = !item.is_disabled;
        } else {
          item.is_disabled = disabled;
        }
      }
    },
    //用于多选表格，切换所有行的选中状态（全选/取消）
    toggleAllSelection() {
      this.selectionAll();
    },
    tableSort(index, sortOrder) {
      if (!this.tableData || this.tableData.length === 0)
        return;
      const item = this.hData[index];
      if (item.sortable) {
        let asc = false;
        if (sortOrder) {
          asc = sortOrder === "ascending";
        } else {
          asc = !item.sort || item.sort === "descending";
        }
        if (asc) {
          item.sort = "ascending";
          if (item.sortType === "number") {
            this.tableData.sort((a, b) => {
              return a[item.prop] - b[item.prop];
            });
          } else if (item.sortType === "date") {
            this.tableData.sort((a, b) => {
              return new Date(a[item.prop].replace(/\-/g, "/")).getTime() - new Date(b[item.prop].replace(/\-/g, "/")).getTime();
            });
          } else {
            this.tableData.sort((a, b) => {
              return a[item.prop].localeCompare(b[item.prop], "zh-Hans-CN");
            });
          }
        } else {
          item.sort = "descending";
          if (item.sortType === "number") {
            this.tableData.sort((a, b) => {
              return b[item.prop] - a[item.prop];
            });
          } else if (item.sortType === "date") {
            this.tableData.sort((a, b) => {
              return new Date(b[item.prop].replace(/\-/g, "/")).getTime() - new Date(a[item.prop].replace(/\-/g, "/")).getTime();
            });
          } else {
            this.tableData.sort((a, b) => {
              return b[item.prop].localeCompare(a[item.prop], "zh-Hans-CN");
            });
          }
        }
        this.hData.forEach((d, idx) => {
          if (index !== idx) {
            d.sort = "";
          }
        });
        this.$emit("sortChange", {
          itemList: this.tableData,
          sort: item.sort,
          prop: item.prop
        });
      }
    },
    //重置所有排序
    resetSort() {
      this.hData.forEach((item) => {
        item.sort = "";
      });
      this.tableData = JSON.parse(JSON.stringify(this.initTableData));
    },
    //ascending-升序 descending-降序
    setSort(prop, sortOrder = "ascending") {
      const index = this.hData.findIndex((item) => item.prop === prop);
      if (index !== -1) {
        this.tableSort(index, sortOrder);
      }
    },
    scrolltolower(e) {
      const direction = e.detail.direction;
      if (direction == "bottom") {
        this.$emit("scrolltolower", e.detail);
      }
    }
  }
};
if (!Array) {
  const _easycom_fui_icon2 = common_vendor.resolveComponent("fui-icon");
  _easycom_fui_icon2();
}
const _easycom_fui_icon = () => "../fui-icon/fui-icon.js";
if (!Math) {
  _easycom_fui_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.show
  }, $props.show ? {
    b: common_vendor.f($data.hData, (item, index, i0) => {
      return common_vendor.e({
        a: item.type === "selection"
      }, item.type === "selection" ? {
        b: $props.checkmarkColor,
        c: $props.checkmarkColor,
        d: (!$props.checkboxColor || $props.checkboxColor === true) && $data.chkAll ? 1 : "",
        e: $data.chkAll ? $options.getCheckboxColor : "transparent",
        f: $data.chkAll ? $options.getCheckboxColor : $props.checkboxBorderColor,
        g: common_vendor.o((...args) => $options.selectionAll && $options.selectionAll(...args), index)
      } : {
        h: common_vendor.t(item.label || item.prop),
        i: (item.align || $props.align) === "center" ? 1 : "",
        j: (item.align || $props.align) === "right" ? 1 : "",
        k: $props.ellipsis ? 1 : "",
        l: item.width + $data.divideW + "px",
        m: item.color || $props.color,
        n: (item.size || $props.size) + "rpx",
        o: $props.fontWeight
      }, {
        p: item.sortable
      }, item.sortable ? {
        q: "312d7e5c-0-" + i0,
        r: common_vendor.p({
          name: item.sort === "descending" ? "turningdown" : "turningup",
          size: item.sortSize || 28,
          color: item.sort ? item.sortColor || "#333" : item.color || $props.color
        }),
        s: (item.sortRight || 40) + "rpx"
      } : {}, {
        t: $props.border && item.fixed === "right"
      }, $props.border && item.fixed === "right" ? {
        v: $props.borderColor
      } : {}, {
        w: $props.border && index === 0 ? 1 : "",
        x: (item.align || $props.align) === "center" ? 1 : "",
        y: (item.align || $props.align) === "right" ? 1 : "",
        z: item.fixed && !$data.isNvue ? 1 : "",
        A: $props.border && index === 0 ? $props.borderColor : "transparent",
        B: item.background || $props.headerBgColor,
        C: item.width + $data.divideW + "px",
        D: item.fixed && item.fixed !== "right" && !$data.isNvue ? item.left + "px" : "auto",
        E: item.fixed === "right" && !$data.isNvue ? item.right + "px" : "auto",
        F: index,
        G: common_vendor.o(($event) => $options.tableSort(index, false), index)
      });
    }),
    c: $props.border ? 1 : "",
    d: $props.border ? $props.borderColor : "transparent",
    e: $props.padding + "rpx",
    f: $props.padding + "rpx",
    g: $props.horBorder ? 1 : "",
    h: $props.horBorder && $props.show ? 1 : "",
    i: $props.fixed ? 1 : "",
    j: $props.horBorder ? $props.borderColor : "transparent",
    k: $props.horBorder && $props.show ? $props.borderColor : "transparent"
  } : {}, {
    l: common_vendor.f($data.tableData, (item, index, i0) => {
      return {
        a: common_vendor.f($data.hData, (model, idx, i1) => {
          return common_vendor.e({
            a: model.type !== 3
          }, model.type !== 3 ? common_vendor.e({
            b: model.type === "selection"
          }, model.type === "selection" ? {
            c: $props.checkmarkColor,
            d: $props.checkmarkColor,
            e: (!$props.checkboxColor || $props.checkboxColor === true) && item.is_selected ? 1 : "",
            f: item.is_disabled ? 1 : "",
            g: item.is_selected ? $options.getCheckboxColor : "transparent",
            h: item.is_selected ? $options.getCheckboxColor : $props.checkboxBorderColor,
            i: common_vendor.o(($event) => $options.selectionChange(index), model.tdId)
          } : model.type === 2 ? {
            k: item[model.prop] || "",
            l: (model.imgWidth || 120) + "rpx",
            m: model.imgHeight ? model.imgHeight + "rpx" : "auto"
          } : {
            n: common_vendor.t(item[model.prop] == 0 ? item[model.prop] : item[model.prop] || ""),
            o: (model.align || $props.align) === "center" ? 1 : "",
            p: (model.align || $props.align) === "right" ? 1 : "",
            q: $props.ellipsis ? 1 : "",
            r: !$props.ellipsis ? 1 : "",
            s: $options.getColColor(model, item[model.prop], index, idx),
            t: (model.textSize || $props.textSize) + "rpx",
            v: model.width + $data.divideW + "px"
          }, {
            j: model.type === 2
          }) : {
            w: common_vendor.f(model.buttons, (btn, j, i2) => {
              return {
                a: common_vendor.t(btn.text),
                b: j > 0 ? 1 : "",
                c: (btn.size || $props.textSize) + "rpx",
                d: btn.color,
                e: btn.fontWeight || "normal",
                f: btn.bId,
                g: common_vendor.o(($event) => $options.handleTap(index, j), btn.bId)
              };
            })
          }, {
            x: $props.border && model.fixed === "right"
          }, $props.border && model.fixed === "right" ? {
            y: $props.borderColor
          } : {}, {
            z: $props.border && idx === 0 ? 1 : "",
            A: (model.align || $props.align) === "center" ? 1 : "",
            B: (model.align || $props.align) === "right" ? 1 : "",
            C: model.type === 3 ? 1 : "",
            D: model.fixed && !$data.isNvue ? 1 : "",
            E: model.tdId,
            F: $props.border && idx === 0 ? $props.borderColor : "transparent",
            G: model.width + $data.divideW + "px",
            H: model.fixed && model.fixed !== "right" && !$data.isNvue ? model.left + "px" : "auto",
            I: model.fixed === "right" && !$data.isNvue ? model.right + "px" : "auto",
            J: common_vendor.o(($event) => $options.tdClick(index, idx, model.prop), model.tdId)
          });
        }),
        b: item.background || ((index + 1) % 2 === 0 && $props.stripe ? $props.stripeColor : $props.background),
        c: $props.horBorder && !$props.show && index === 0 ? 1 : "",
        d: $props.horBorder && !$props.show && index === 0 ? $props.borderColor : "transparent",
        e: index
      };
    }),
    m: $props.border ? 1 : "",
    n: $props.border ? $props.borderColor : "transparent",
    o: $props.padding + "rpx",
    p: $props.padding + "rpx",
    q: $props.horBorder ? 1 : "",
    r: $props.horBorder ? $props.borderColor : "transparent",
    s: $data.totalW + "px",
    t: $props.bounces,
    v: $props.showScrollbar,
    w: $props.height > 0 && $props.height != 0,
    x: $data.width + "px",
    y: $props.height > 0 || $props.height != 0 ? $props.height + "rpx" : "auto",
    z: !$props.height || $props.height == 0 ? 1 : "",
    A: common_vendor.o((...args) => $options.scrolltolower && $options.scrolltolower(...args)),
    B: $props.itemList.length === 0 && $props.emptyText !== true && $props.emptyText !== ""
  }, $props.itemList.length === 0 && $props.emptyText !== true && $props.emptyText !== "" ? {
    C: common_vendor.t($props.emptyText),
    D: $props.emptySize + "rpx",
    E: $props.emptyColor,
    F: $data.width + "px",
    G: $data.totalW > $data.width && $props.height > 0 && $props.height != 0 ? 1 : ""
  } : {}, {
    H: $props.height > 0 || $props.height != 0 ? $props.height + "rpx" : "auto",
    I: $data.width + "px"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-312d7e5c"]]);
wx.createComponent(Component);
