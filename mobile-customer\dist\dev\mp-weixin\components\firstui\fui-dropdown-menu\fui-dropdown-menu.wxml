<view class="fui-dropdown__menu data-v-5d194ea8" ref="fui_dm_wrap" bindtap="{{t}}"><slot></slot><view wx:if="{{a}}" class="fui-ddm__mask data-v-5d194ea8" style="{{'background:' + b}}" bindtap="{{c}}"></view><view wx:if="{{d}}" class="{{['fui-dropdown__menu-list', 'data-v-5d194ea8', o && 'fui-ddm__down', p && 'fui-ddm__up', q && 'fui-ddm__down-show', r && 'fui-ddm__up-show']}}" style="{{s}}" ref="fui_ddm_list"><scroll-view show-scrollbar="{{false}}" class="fui-ddm__scroll data-v-5d194ea8" scroll-y style="{{'height:' + l + ';' + ('max-height:' + m) + ';' + ('min-width:' + n)}}"><view class="data-v-5d194ea8"><slot name="item"></slot><view wx:for="{{e}}" wx:for-item="model" wx:key="v" style="{{'background:' + h + ';' + ('padding:' + i) + ';' + ('border-bottom-color:' + j)}}" class="{{['fui-dropdown__menu-item', 'data-v-5d194ea8', k && 'fui-ddm__reverse', model.t && 'fui-ddm__item-line']}}" catchtap="{{model.w}}"><view wx:if="{{f}}" class="{{['fui-ddm__checkbox', 'data-v-5d194ea8', model.d && 'fui-is__checkmark', model.e && 'fui-ddm__checkbox-color']}}" style="{{'background:' + model.f + ';' + ('border-color:' + model.g)}}"><view wx:if="{{model.a}}" class="fui-ddm__checkmark data-v-5d194ea8" style="{{'border-bottom-color:' + model.b + ';' + ('border-right-color:' + model.c)}}"></view></view><view class="fui-ddm__flex data-v-5d194ea8"><view wx:if="{{model.h}}" class="{{['fui-ddm__icon-box', 'data-v-5d194ea8', model.l && 'fui-ddm__icon-ml', model.m && 'fui-ddm__icon-mr']}}" style="{{'width:' + model.n + ';' + ('height:' + model.o)}}"><image class="data-v-5d194ea8" src="{{model.i}}" style="{{'width:' + model.j + ';' + ('height:' + model.k)}}"></image></view><text class="{{['fui-ddm__item-text', 'data-v-5d194ea8', model.q && 'fui-ddm__text-pl', model.r && 'fui-ddm__text-pr']}}" style="{{'font-size:' + g + ';' + ('color:' + model.s)}}">{{model.p}}</text></view></view></view></scroll-view></view></view>