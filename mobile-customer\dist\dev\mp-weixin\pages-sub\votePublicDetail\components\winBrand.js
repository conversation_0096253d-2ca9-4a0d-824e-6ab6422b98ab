"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
const store_user = require("../../../store/user.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "winBrand",
  props: {
    brand: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      default: 0
    }
  },
  setup(__props) {
    const props = __props;
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const picUrl = common_vendor.computed(() => {
      var _a, _b;
      console.log("props?.brand?.pic_url", props == null ? void 0 : props.brand);
      console.log("props", props == null ? void 0 : props.brand);
      if ((_a = props == null ? void 0 : props.brand) == null ? void 0 : _a.pic_url) {
        return `${"http://10.0.9.193:8091/dfloor/s3/getPublicObjectByStream?fileName="}${(_b = props == null ? void 0 : props.brand) == null ? void 0 : _b.pic_url}`;
      } else {
        return null;
      }
    });
    const voteRateNumber = common_vendor.computed(() => {
      var _a, _b, _c, _d;
      if (((_a = props == null ? void 0 : props.brand) == null ? void 0 : _a.vote_rate) === "-" || !((_b = props == null ? void 0 : props.brand) == null ? void 0 : _b.vote_rate)) {
        return 0;
      }
      console.log("props?.brand?.vote_rate", (_c = props == null ? void 0 : props.brand) == null ? void 0 : _c.vote_rate);
      return parseFloat((_d = props == null ? void 0 : props.brand) == null ? void 0 : _d.vote_rate);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.brand.brand_name
      }, __props.brand.brand_name ? common_vendor.e({
        b: common_vendor.unref(picUrl)
      }, common_vendor.unref(picUrl) ? {
        c: common_vendor.unref(picUrl)
      } : {}, {
        d: common_vendor.t(__props.brand.brand_name),
        e: common_vendor.n(__props.brand.tagType),
        f: common_vendor.t(__props.brand.product_name),
        g: __props.brand.skuCode_dictText
      }, __props.brand.skuCode_dictText ? common_vendor.e({
        h: common_vendor.t(__props.brand.skuCode_dictText),
        i: __props.brand.price
      }, __props.brand.price ? {
        j: common_vendor.t(__props.brand.price)
      } : {}) : {}, {
        k: common_vendor.unref(voteRateNumber),
        l: common_vendor.t(__props.brand.vote_count),
        m: common_vendor.t(__props.brand.vote_rate)
      }) : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4fd8dbd0"]]);
wx.createComponent(Component);
