"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const utils_http = require("../../utils/http.js");
function personalAuth(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/auth/user/personalAuth", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function getMyAuthList(_0) {
  return __async(this, arguments, function* ({
    options,
    params
  }) {
    return utils_http.request("/auth/user/queryAllAuth", __spreadValues({
      method: "GET",
      params
    }, options || {}));
  });
}
function grantAuth(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/auth/user/grant", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function applyAuth(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/auth/user/access", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function cancelAuth(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request(`/auth/user/cancelAuth/${params.id}`, __spreadValues({
      method: "DELETE",
      headers: {
        "Content-Type": "application/json"
      }
    }, options || {}));
  });
}
exports.applyAuth = applyAuth;
exports.cancelAuth = cancelAuth;
exports.getMyAuthList = getMyAuthList;
exports.grantAuth = grantAuth;
exports.personalAuth = personalAuth;
