"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "buildingsVote",
  props: {
    voteBuildingsList: {
      type: Array,
      default: () => []
    }
  },
  setup(__props) {
    const props = __props;
    const allValidVoteBuildingsStr = common_vendor.computed(() => {
      var _a, _b;
      return (_b = (_a = props.voteBuildingsList) == null ? void 0 : _a.map((item) => `${item.buildingNo}幢`)) == null ? void 0 : _b.join(";");
    });
    const validVoteBuildingsStr = common_vendor.computed(() => {
      var _a, _b, _c;
      return (_c = (_b = (_a = props.voteBuildingsList) == null ? void 0 : _a.filter((item) => item.valid)) == null ? void 0 : _b.map((item) => `${item.buildingNo}幢`)) == null ? void 0 : _c.join(";");
    });
    const invalidVoteBuildingsStr = common_vendor.computed(() => {
      var _a, _b, _c;
      return ((_c = (_b = (_a = props.voteBuildingsList) == null ? void 0 : _a.filter((item) => !item.valid)) == null ? void 0 : _b.map((item) => `${item.buildingNo}幢`)) == null ? void 0 : _c.join(";")) || "";
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(allValidVoteBuildingsStr)),
        b: common_vendor.t(common_vendor.unref(validVoteBuildingsStr)),
        c: common_vendor.unref(invalidVoteBuildingsStr)
      }, common_vendor.unref(invalidVoteBuildingsStr) ? {
        d: common_vendor.t(common_vendor.unref(invalidVoteBuildingsStr)),
        e: common_vendor.t(common_vendor.unref(invalidVoteBuildingsStr)),
        f: common_vendor.t(common_vendor.unref(invalidVoteBuildingsStr)),
        g: common_vendor.t(common_vendor.unref(invalidVoteBuildingsStr))
      } : {}, {
        h: common_vendor.f(__props.voteBuildingsList, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.buildingNo),
            b: common_vendor.t(item.valid ? "有效票" : "无效票"),
            c: item.voteRateValue,
            d: common_vendor.t(item.voteCount),
            e: common_vendor.t(item.voteRateValue),
            f: item.voteRateValue,
            g: common_vendor.t(item.voteArea),
            h: common_vendor.t(item.voteAreaRateValue),
            i: item.id
          };
        })
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-70007f5c"]]);
wx.createComponent(Component);
