"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
require("../../store/index.js");
const utils_index = require("../../utils/index.js");
const utils_util = require("../../utils/util.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_list_cell2 = common_vendor.resolveComponent("fui-list-cell");
  const _easycom_fui_textarea2 = common_vendor.resolveComponent("fui-textarea");
  const _easycom_fui_upload2 = common_vendor.resolveComponent("fui-upload");
  const _easycom_fui_form_item2 = common_vendor.resolveComponent("fui-form-item");
  const _easycom_fui_form2 = common_vendor.resolveComponent("fui-form");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_landscape2 = common_vendor.resolveComponent("fui-landscape");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_list_cell2 + _easycom_fui_textarea2 + _easycom_fui_upload2 + _easycom_fui_form_item2 + _easycom_fui_form2 + _easycom_fui_button2 + _easycom_fui_loading2 + _easycom_fui_landscape2 + _component_layout_default_uni)();
}
const _easycom_fui_list_cell = () => "../../components/firstui/fui-list-cell/fui-list-cell.js";
const _easycom_fui_textarea = () => "../../components/firstui/fui-textarea/fui-textarea.js";
const _easycom_fui_upload = () => "../../components/firstui/fui-upload/fui-upload.js";
const _easycom_fui_form_item = () => "../../components/firstui/fui-form-item/fui-form-item.js";
const _easycom_fui_form = () => "../../components/firstui/fui-form/fui-form.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_landscape = () => "../../components/firstui/fui-landscape/fui-landscape.js";
if (!Math) {
  (_easycom_fui_list_cell + _easycom_fui_textarea + _easycom_fui_upload + _easycom_fui_form_item + _easycom_fui_form + _easycom_fui_button + _easycom_fui_loading + _easycom_fui_landscape)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "errorCorrection",
  setup(__props) {
    const listRowPadding = ["28rpx", "0", "28rpx", "0"];
    const isLoading = common_vendor.ref(false);
    const userStore = store_user.useUserStore();
    const { userInfo } = common_vendor.storeToRefs(userStore);
    const formRef = common_vendor.ref();
    const uploadRef = common_vendor.ref();
    const uploadHeader = common_vendor.ref({
      "x-access-token": userInfo.value.token
    });
    const baseUrl = utils_index.getEnvBaseUrl();
    const uploadUrl = common_vendor.ref(`${baseUrl}/s3/upload`);
    const formData = common_vendor.ref({
      description: "",
      files: []
    });
    const successFile = (e) => {
      console.log("successFile", e);
      const res = e.res.data;
      if (res) {
        const file = JSON.parse(res);
        console.log("file", file);
        if (file.code === 200) {
          utils_util.showTextToast("上传成功");
          formData.value.files.push(file.result);
        }
      }
    };
    const errorFile = (e) => {
      console.log("errorFile", e);
      utils_util.showTextToast("上传失败");
    };
    const completeFile = (e) => {
      console.log("completeFile", e);
    };
    const deleteFile = (e) => __async(this, null, function* () {
      console.log("deleteFile", e);
      formData.value.files.splice(e.index, 1);
    });
    const handleSubmit = () => __async(this, null, function* () {
      console.log("formData", formData.value);
      if (!formData.value.description) {
        utils_util.showTextToast("请输入您的问题");
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "是否提交？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            try {
              isLoading.value = true;
              const res2 = yield service_app_vote.addErrorCorrectionHouse({
                body: {
                  remark: formData.value.description,
                  imgUrl: formData.value.files.join(","),
                  userHouseId: houseId.value
                }
              });
              console.log("res", res2);
              if (res2.code === 200) {
                showModal.value = true;
              }
            } catch (error) {
              console.log("error", error);
            } finally {
              isLoading.value = false;
            }
          } else if (res.cancel) {
            console.log("用户点击了取消");
          }
        })
      });
    });
    const showModal = common_vendor.ref(false);
    const handleCloseModal = () => {
      showModal.value = false;
      common_vendor.index.navigateBack();
    };
    const houseId = common_vendor.ref("");
    const location = common_vendor.ref("");
    const communityName = common_vendor.ref("");
    common_vendor.onLoad((options) => {
      console.log("options", options);
      if (options) {
        houseId.value = options.id;
        location.value = options.location;
        communityName.value = options.communityName;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(userInfo).phone),
        b: common_vendor.p({
          padding: listRowPadding
        }),
        c: common_vendor.t(common_vendor.unref(userInfo).realname),
        d: common_vendor.p({
          padding: listRowPadding
        }),
        e: common_vendor.t(common_vendor.unref(userInfo).idCard),
        f: common_vendor.p({
          padding: listRowPadding
        }),
        g: common_vendor.unref(houseId)
      }, common_vendor.unref(houseId) ? {
        h: common_vendor.t(common_vendor.unref(communityName)),
        i: common_vendor.p({
          padding: listRowPadding
        })
      } : {}, {
        j: common_vendor.unref(houseId)
      }, common_vendor.unref(houseId) ? {
        k: common_vendor.t(common_vendor.unref(location)),
        l: common_vendor.p({
          padding: listRowPadding,
          bottomBorder: false
        })
      } : {}, {
        m: common_vendor.o(($event) => common_vendor.unref(formData).description = $event),
        n: common_vendor.p({
          height: "160rpx",
          maxlength: "-1",
          padding: ["0", "0", "24rpx", "0"],
          ["border-bottom"]: true,
          ["border-top"]: false,
          size: 26,
          placeholder: "请输入您的问题",
          modelValue: common_vendor.unref(formData).description
        }),
        o: common_vendor.sr(uploadRef, "66abbd67-10,66abbd67-9", {
          "k": "uploadRef"
        }),
        p: common_vendor.o(successFile),
        q: common_vendor.o(errorFile),
        r: common_vendor.o(completeFile),
        s: common_vendor.o(deleteFile),
        t: common_vendor.p({
          max: 9,
          immediate: true,
          url: common_vendor.unref(uploadUrl),
          radius: "20",
          width: "160",
          height: "160",
          header: common_vendor.unref(uploadHeader)
        }),
        v: common_vendor.p({
          prop: "files",
          bottomBorder: false,
          ["error-align"]: "left",
          label: "上传图片",
          padding: listRowPadding
        }),
        w: common_vendor.p({
          label: "请输入您的问题",
          bottomBorder: false,
          prop: "description",
          ["error-align"]: "left",
          labelWidth: 220,
          padding: listRowPadding
        }),
        x: common_vendor.sr(formRef, "66abbd67-6,66abbd67-0", {
          "k": "formRef"
        }),
        y: common_vendor.p({
          show: false,
          labelSize: 24
        }),
        z: common_vendor.o(handleSubmit),
        A: common_vendor.p({
          text: "立即提交"
        }),
        B: common_vendor.unref(isLoading)
      }, common_vendor.unref(isLoading) ? {
        C: common_vendor.p({
          isMask: true
        })
      } : {}, {
        D: common_assets._imports_0$4,
        E: common_vendor.o(handleCloseModal),
        F: common_vendor.p({
          radius: "100rpx",
          width: "100%",
          ["disabled-background"]: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          borderColor: "rgba(0,0,0,0)",
          ["border-width"]: "0"
        }),
        G: common_vendor.p({
          show: common_vendor.unref(showModal),
          closable: false
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-66abbd67"]]);
wx.createPage(MiniProgramPage);
