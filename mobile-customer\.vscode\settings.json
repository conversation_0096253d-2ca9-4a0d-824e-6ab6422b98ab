{
  // 默认格式化工具选择prettier
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // 保存的时候自动格式化
  "editor.formatOnSave": false,

  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dotenv]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 配置语言的文件关联
  "files.associations": {
    "pages.json": "jsonc", // pages.json 可以写注释
    "manifest.json": "jsonc" // manifest.json 可以写注释
  },
  "cSpell.words": [
    "Aplipay",
    "climblee",
    "commitlint",
    "dcloudio",
    "iconfont",
    "qrcode",
    "refresherrefresh",
    "scrolltolower",
    "tabbar",
    "Toutiao",
    "unibest",
    "uvui",
    "vitepress",
    "Wechat",
    "WechatMiniprogram",
    "Weixin"
  ],
  "typescript.tsdk": "node_modules\\typescript\\lib",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "$(capture).test.ts, $(capture).test.tsx",
    "*.tsx": "$(capture).test.ts, $(capture).test.tsx",
    "*.env": "$(capture).env.*",
    "CHANGELOG.md": "CHANGELOG*",
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,.npmrc,.browserslistrc",
    ".eslintrc.cjs": ".eslintignore,.prettierignore,.stylelintignore,.commitlintrc.*,.prettierrc.*,.stylelintrc.*,.eslintrc-auto-import.json,.editorconfig,.commitlint.cjs",
    "vite.config.ts": "tsconfig.*.json,uno.config.ts,tsconfig.json,uni-pages.d.ts",
    "manifest.config.ts": "manifest.config.ts,pages.config.ts",
    "project.config.json": "project.private.config.json"
  },
  "i18n-ally.localesPaths": [
    "src/components/firstui-i18n/fui-actionsheet/i18n",
    "src/components/firstui-i18n/fui-autograph/i18n",
    "src/components/firstui-i18n/fui-calendar/i18n",
    "src/components/firstui-i18n/fui-cascader/i18n",
    "src/components/firstui-i18n/fui-copy-text/i18n",
    "src/components/firstui-i18n/fui-countdown-verify/i18n",
    "src/components/firstui-i18n/fui-data-tag/i18n",
    "src/components/firstui-i18n/fui-date-picker/i18n",
    "src/components/firstui-i18n/fui-dialog/i18n",
    "src/components/firstui-i18n/fui-digital-keyboard/i18n",
    "src/components/firstui-i18n/fui-form-item/i18n",
    "src/components/firstui-i18n/fui-image-cropper/i18n",
    "src/components/firstui-i18n/fui-link/i18n",
    "src/components/firstui-i18n/fui-loading/i18n",
    "src/components/firstui-i18n/fui-loadmore/i18n",
    "src/components/firstui-i18n/fui-modal/i18n",
    "src/components/firstui-i18n/fui-pagination/i18n",
    "src/components/firstui-i18n/fui-picker/i18n",
    "src/components/firstui-i18n/fui-poster/i18n",
    "src/components/firstui-i18n/fui-poster-weex/i18n",
    "src/components/firstui-i18n/fui-puzzle-verify/i18n",
    "src/components/firstui-i18n/fui-rotate-verify/i18n",
    "src/components/firstui-i18n/fui-search-bar/i18n",
    "src/components/firstui-i18n/fui-select/i18n",
    "src/components/firstui-i18n/fui-share-sheet/i18n",
    "src/components/firstui-i18n/fui-slide-verify/i18n",
    "src/components/firstui-i18n/fui-slider-captcha/i18n",
    "src/components/firstui-i18n/fui-swipe-action/i18n",
    "src/components/firstui-i18n/fui-upload/i18n",
    "src/components/firstui-i18n/fui-upload-video/i18n",
    "src/components/firstui/fui-parse/high-light/languages",
    "src/components/firstui-i18n/fui-parse/high-light/languages"
  ]
}
