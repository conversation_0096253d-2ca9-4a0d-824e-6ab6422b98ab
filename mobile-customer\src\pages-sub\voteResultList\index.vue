<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '公示',
  },
}
</route>

<template>
  <z-paging
    ref="paging"
    :auto-hide-loading-after-first-loaded="false"
    loading-full-fixed
    :paging-style="{ 'background-color': '#f7f7f7' }"
    v-model="dataList"
    :auto-show-back-to-top="true"
    :back-to-top-bottom="200"
    @query="queryList"
    :auto="true"
    lower-threshold="150rpx"
  >
    <template #loading>
      <fui-loading isMask></fui-loading>
    </template>
    <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
    <template #empty>
      <fui-empty
        :width="386"
        :height="280"
        src="/static/images/img_data_3x.png"
        isFixed
        title="暂无数据"
      ></fui-empty>
    </template>
    <template #top>
      <view class="mt20rpx">
        <fui-search-bar
          placeholder="请输入小区名称进行搜索"
          v-model="searchVal"
          background="#fff"
          radius="36"
          inputBackground="#fafafa"
          @search="search"
          @clear="handleClear"
        ></fui-search-bar>
      </view>
    </template>

    <view class="mt20rpx">
      <view class="" v-for="item in dataList" :key="item.id">
        <VoteResult :item="item" />
        <view class="ml32rpx h1px bg-[#eee]"></view>
      </view>
    </view>
    <template #bottom>
      <safe-bottom-zone></safe-bottom-zone>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import VoteResult from '@/components/common/voteResult.vue'
import { queryBizVoteInfoList } from '@/service/app'
import safeBottomZone from '@/components/common/safeBottomZone.vue'

const searchVal = ref('')

// 投票公告
const voteNoticeList = ref([])

const paging = ref(null)
// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
const dataList = ref([])

const queryList = async (pageNo, pageSize) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
    communityName: searchVal.value ? `*${searchVal.value}*` : '',
    minStatus: 2,
    order: 'desc',
    column: 'voteResultTime',
  }

  console.log('params', params)
  // 此处请求仅为演示，请替换为自己项目中的请求
  try {
    const res = await queryBizVoteInfoList({ params })
    console.log('queryBizVoteInfoList', res)

    paging.value.complete(res?.result?.records || [], true)
  } catch (error) {
    paging.value.complete(false)
  }
}

const search = () => {
  console.log('searchVal', searchVal.value)
  console.log('queryBizVoteInfoList', dataList.value)
  paging.value?.reload()
}

const handleClear = () => {
  console.log('清除')
  paging.value?.reload()
}
</script>

<style lang="scss" scoped>
//
</style>
