"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../utils/http.js");
const service_app_myAuth = require("../../service/app/myAuth.js");
require("../../store/index.js");
const utils_index = require("../../utils/index.js");
const utils_util = require("../../utils/util.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_input2 = common_vendor.resolveComponent("fui-input");
  const _easycom_fui_form_item2 = common_vendor.resolveComponent("fui-form-item");
  const _easycom_fui_form2 = common_vendor.resolveComponent("fui-form");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_checkbox2 = common_vendor.resolveComponent("fui-checkbox");
  const _easycom_fui_label2 = common_vendor.resolveComponent("fui-label");
  const _easycom_fui_checkbox_group2 = common_vendor.resolveComponent("fui-checkbox-group");
  const _easycom_fui_picker2 = common_vendor.resolveComponent("fui-picker");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_input2 + _easycom_fui_form_item2 + _easycom_fui_form2 + _easycom_fui_button2 + _easycom_fui_checkbox2 + _easycom_fui_label2 + _easycom_fui_checkbox_group2 + _easycom_fui_picker2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_input = () => "../../components/firstui/fui-input/fui-input.js";
const _easycom_fui_form_item = () => "../../components/firstui/fui-form-item/fui-form-item.js";
const _easycom_fui_form = () => "../../components/firstui/fui-form/fui-form.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_checkbox = () => "../../components/firstui/fui-checkbox/fui-checkbox.js";
const _easycom_fui_label = () => "../../components/firstui/fui-label/fui-label.js";
const _easycom_fui_checkbox_group = () => "../../components/firstui/fui-checkbox-group/fui-checkbox-group.js";
const _easycom_fui_picker = () => "../../components/firstui/fui-picker/fui-picker.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_input + _easycom_fui_form_item + _easycom_fui_form + _easycom_fui_button + _easycom_fui_checkbox + _easycom_fui_label + _easycom_fui_checkbox_group + _easycom_fui_picker + _easycom_fui_loading)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo } = common_vendor.storeToRefs(userStore);
    const baseUrl = utils_index.getEnvBaseUrl();
    common_vendor.ref(`${baseUrl}/file/uploadFile`);
    common_vendor.ref({
      token: userInfo.value.token
    });
    const formRef = common_vendor.ref();
    const formData = common_vendor.ref({
      name: "",
      phone: "",
      cardCode: "",
      phoneCode: "86",
      /**
       * 证件类型
       *0 二代身份证
       *1 护照
       *2港澳通行证
       *3 台胞证
       */
      cardType: { value: 0, text: "二代身份证" },
      cardPersonImg: "",
      cardInfoImg: ""
    });
    const rules = [
      {
        name: "name",
        rule: ["required"],
        msg: ["请输入姓名"]
      },
      // {
      //   name: 'phone',
      //   rule: ['required', 'isMobile'],
      //   msg: ['请输入手机号', '请输入正确的手机号'],
      // },
      // {
      //   name: 'phoneCode',
      //   rule: ['required'],
      //   msg: ['请输入手机区号'],
      // },
      {
        name: "cardCode",
        rule: ["required", "isIdCard"],
        msg: ["请输入证件号", "请输入正确的证件号"]
      }
    ];
    const pickerKey = common_vendor.ref("");
    const pickerValue = common_vendor.ref("二代身份证");
    const pickerOptions = common_vendor.ref([
      { value: 0, text: "二代身份证" },
      { value: 1, text: "护照" },
      { value: 2, text: "港澳通行证" },
      { value: 3, text: "台胞证" }
    ]);
    const showPick = common_vendor.ref(false);
    const pickerChange = (e) => {
      console.log("pickerChange", e);
      formData.value.cardType.value = e.value;
      formData.value.cardType.text = e.text;
      pickerCancel();
    };
    const pickerCancel = () => {
      showPick.value = false;
      pickerKey.value = "";
    };
    const handleCertification = () => {
      var _a, _b;
      console.log("handleCertification");
      if (!isAgree.value) {
        utils_util.showTextToast("请先同意用户服务协议和隐私权政策");
        return;
      }
      const _rules = [...rules];
      console.log("_rules", _rules);
      console.log(formData.value.cardType);
      console.log((_b = (_a = formData.value.cardType) == null ? void 0 : _a.text) == null ? void 0 : _b.includes("身份证"));
      formRef.value.validator(formData.value, _rules).then((res) => __async(this, null, function* () {
        var _a2, _b2;
        console.log("res", res);
        if (res.isPassed) {
          const { name, phone, phoneCode, cardType, cardCode, cardPersonImg, cardInfoImg } = formData.value;
          try {
            loading.value = true;
            const res2 = yield service_app_myAuth.personalAuth({
              body: {
                name,
                phone,
                // phoneCode,
                cardType: cardType.value,
                cardCode
                // cardPersonImg,
                // cardInfoImg,
              }
            });
            console.log("res", res2);
            if ((_a2 = res2.result) == null ? void 0 : _a2.result) {
              const encodeUrl = encodeURIComponent((_b2 = res2.result) == null ? void 0 : _b2.result);
              common_vendor.index.redirectTo({
                url: `/pages-sub/realname/personalAuth?url=${encodeUrl}`
              });
            } else {
              common_vendor.index.navigateBack();
            }
          } catch (e) {
            console.log("e", e);
          } finally {
            loading.value = false;
          }
          console.log("cardPersonImg", cardPersonImg);
          console.log("cardInfoImg", cardInfoImg);
        } else {
          utils_util.showTextToast(res.errorMsg);
        }
      }));
    };
    const loading = common_vendor.ref(false);
    const isAgree = common_vendor.ref(false);
    const handleAgree = (e) => {
      isAgree.value = e.checked;
    };
    const handleShowAgreement = () => {
      console.log("用户服务协议");
      common_vendor.index.navigateTo({ url: "/pages-sub/userAgreement/index" });
    };
    const handleShowPrivacyPolicy = () => {
      console.log("隐私权政策");
      common_vendor.index.navigateTo({ url: "/pages-sub/privacyPolicy/index" });
    };
    const fetchUserInfo = () => __async(this, null, function* () {
      var _a, _b;
      try {
        const res = yield userStore.checkUserInfo();
        console.log("fetchUserInfo", (_a = res.result) == null ? void 0 : _a.isRealAuth);
        yield common_vendor.nextTick$1();
        if (((_b = res.result) == null ? void 0 : _b.isRealAuth) === 1) {
          utils_util.showTextToast("用户已实名认证");
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1e3);
        }
      } catch (e) {
        console.log("e", e);
      } finally {
      }
    });
    common_vendor.onShow(() => {
      console.log("onShow===实名认证页面");
      fetchUserInfo();
    });
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(($event) => common_vendor.unref(formData).name = $event),
        c: common_vendor.p({
          ["text-align"]: "right",
          padding: [0],
          borderBottom: false,
          backgroundColor: "transparent",
          placeholder: "请输入姓名",
          modelValue: common_vendor.unref(formData).name
        }),
        d: common_vendor.p({
          label: "姓名",
          asterisk: true,
          prop: "name",
          ["error-align"]: "left"
        }),
        e: common_vendor.o(($event) => common_vendor.unref(formData).cardCode = $event),
        f: common_vendor.p({
          maxlength: 18,
          type: "cardCode",
          ["text-align"]: "right",
          padding: [0],
          borderBottom: false,
          backgroundColor: "transparent",
          placeholder: "请输入证件号",
          modelValue: common_vendor.unref(formData).cardCode
        }),
        g: common_vendor.p({
          label: "证件号",
          asterisk: true,
          prop: "cardCode",
          ["error-align"]: "left"
        }),
        h: common_vendor.sr(formRef, "ef7443e6-1,ef7443e6-0", {
          "k": "formRef"
        }),
        i: common_vendor.p({
          show: false
        }),
        j: common_vendor.o(handleCertification),
        k: common_vendor.p({
          text: "刷脸认证"
        }),
        l: common_vendor.o(handleAgree),
        m: common_vendor.p({
          checked: common_vendor.unref(isAgree),
          scaleRatio: 0.9
        }),
        n: common_vendor.p({
          name: "checkbox"
        }),
        o: common_vendor.o(handleShowAgreement),
        p: common_vendor.p({
          color: "#FF9900",
          type: "link",
          size: 24
        }),
        q: common_vendor.o(handleShowPrivacyPolicy),
        r: common_vendor.p({
          color: "#FF9900",
          type: "link",
          size: 24
        }),
        s: common_vendor.o(pickerChange),
        t: common_vendor.o(pickerCancel),
        v: common_vendor.p({
          linkage: true,
          value: common_vendor.unref(pickerValue),
          options: common_vendor.unref(pickerOptions),
          show: common_vendor.unref(showPick)
        }),
        w: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        x: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ef7443e6"]]);
wx.createPage(MiniProgramPage);
