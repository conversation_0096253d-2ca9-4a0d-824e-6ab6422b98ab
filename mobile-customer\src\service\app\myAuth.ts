/* eslint-disable */
// @ts-ignore
import { request } from '@/utils/http';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';


/** 个人实名认证 */
export async function personalAuth({
    body,
    options,
}: {
    body: {
        name: string,
        cardCode: string,
        cardType: string,
        successPage?: string,
    };
    options?: CustomRequestOptions;
}) {
    return request<API.R>('/auth/user/personalAuth', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: body,
        ...(options || {}),
    });
}


/** 获取当前用户所有授权列表 */
export async function getMyAuthList({
    options,
    params,
}: {
    // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
    options?: CustomRequestOptions;
    params?: {
        status?: number,
    }
}) {
    return request<API.R>('/auth/user/queryAllAuth', {
        method: 'GET',
        params,
        ...(options || {}),
    });
}

/** 正向授权接口(由授权人向被授权人授权)
 * accessRelationType 关系 0 伴侣 1 子女 2 父母
 */

export async function grantAuth({
    body,
    options,
}: {
    body: {
        name: string,
        cardCode: string,
        accessRelationType: number,
    };
    options?: CustomRequestOptions;
}) {
    return request<API.R>('/auth/user/grant', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: body,
        ...(options || {}),
    });
}

/** 反向授权(由被授权人向授权人申请授权)
 * accessRelationType 关系 0 伴侣 1 子女 2 父母
 */

export async function applyAuth({
    body,
    options,
}: {
    body: {
        name: string,
        cardCode: string,
        accessRelationType: number,
        successPage?:string,
    };
    options?: CustomRequestOptions;
}) {
    return request<API.R>('/auth/user/access', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        data: body,
        ...(options || {}),
    });
}


/** 取消授权(正向或者反向) */
export async function cancelAuth({
    params,
    options,
  }: {
    params: {
        id: string,
    };
    options?: CustomRequestOptions;
  }) {
    return request<API.R>(`/auth/user/cancelAuth/${params.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      ...(options || {}),
    });
  }