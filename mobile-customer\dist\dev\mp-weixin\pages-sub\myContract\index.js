"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_index = require("../../utils/index.js");
require("../../store/index.js");
const utils_util = require("../../utils/util.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_loading2 + _easycom_fui_empty2 + _easycom_z_paging2 + _component_layout_default_uni)();
}
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_z_paging = () => "../../node-modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_fui_loading + _easycom_fui_empty + myContractItem + safeBottomZone + _easycom_z_paging)();
}
const myContractItem = () => "./components/myContractItem.js";
const safeBottomZone = () => "../../components/common/safeBottomZone.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo } = common_vendor.storeToRefs(userStore);
    const baseUrl = utils_index.getEnvBaseUrl();
    const fileContractStreamPath = "/s3/getObjectByStream";
    const dataList = common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const queryList = (pageNo, pageSize) => __async(this, null, function* () {
      const params = {
        pageNo,
        pageSize
      };
      console.log("params", params);
      try {
        const res = yield service_app_vote.queryContractList({ params });
        console.log("getMyVote", res);
        paging.value.completeByNoMore((res == null ? void 0 : res.result) || [], true);
      } catch (error) {
        paging.value.complete(false);
      }
    });
    const loading = common_vendor.ref(false);
    const handleView = (item) => {
      var _a, _b;
      if (loading.value) {
        return;
      }
      loading.value = true;
      const fileSuffix = ((_b = (_a = item == null ? void 0 : item.contractUrl) == null ? void 0 : _a.split(".")) == null ? void 0 : _b.pop()) || "docx";
      const _url = `${baseUrl}${fileContractStreamPath}?fileName=${item.contractUrl}&token=${userInfo.value.token}`;
      common_vendor.index.downloadFile({
        url: _url,
        success: function(res) {
          console.log("contractDetail-getSignDocumentRelId-res-success", res);
          const _filePath = res.tempFilePath;
          common_vendor.index.openDocument({
            filePath: _filePath,
            fileType: fileSuffix,
            success: function(res2) {
              console.log("打开文档成功");
            },
            fail: function(res2) {
              console.log("打开文档失败", res2);
              utils_util.showTextToast("打开文档失败");
            }
          });
        },
        fail: function(res) {
          console.log("打开文档失败222", res);
          utils_util.showTextToast("打开文档失败");
        },
        complete: () => {
          loading.value = false;
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isMask: true
        }),
        b: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          isFixed: true,
          title: "暂无数据"
        }),
        c: common_vendor.f(common_vendor.unref(dataList), (item, k0, i0) => {
          return {
            a: common_vendor.o(($event) => handleView(item), item.id),
            b: "0f435270-4-" + i0 + ",0f435270-1",
            c: common_vendor.p({
              item
            }),
            d: item.id
          };
        }),
        d: common_vendor.sr(paging, "0f435270-1,0f435270-0", {
          "k": "paging"
        }),
        e: common_vendor.o(queryList),
        f: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        g: common_vendor.p({
          ["auto-hide-loading-after-first-loaded"]: false,
          ["loading-full-fixed"]: true,
          ["paging-style"]: {
            "background-color": "#f7f7f7"
          },
          ["auto-show-back-to-top"]: true,
          ["back-to-top-bottom"]: 200,
          auto: true,
          ["lower-threshold"]: "150rpx",
          modelValue: common_vendor.unref(dataList)
        }),
        h: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        i: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0f435270"]]);
wx.createPage(MiniProgramPage);
