"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "otherMessageItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const props = __props;
    const content = common_vendor.computed(() => {
      if (props.item.communityName) {
        return `关于${props.item.communityName}${props.item.buildingNo}幢${props.item.unitNo}${props.item.houseNo}的纠错反馈`;
      } else {
        return `关于${props.item.remark}的纠错反馈`;
      }
    });
    const handleClick = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/messageDetail/index?id=" + props.item.id
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(common_vendor.unref(content)),
        b: common_vendor.t(__props.item.createTime),
        c: common_vendor.o(handleClick)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7f7834f2"]]);
wx.createComponent(Component);
