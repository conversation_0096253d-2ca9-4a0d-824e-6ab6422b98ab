
.fui-bottom__popup-wrap.data-v-7a724ac3 {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 1001;

		display: flex;

		flex-direction: row;
		align-items: flex-end;
		justify-content: center;

		transition: all ease-in-out .2s;
		visibility: hidden;
		border-bottom-width: 0;
		overflow: hidden;
		opacity: 0;
}
.fui-bottom__popwrap-show.data-v-7a724ac3 {
		opacity: 1;
		visibility: visible;
}
.fui-bottom__popup.data-v-7a724ac3 {

		width: 100%;
		transform: translate3d(0, 100%, 0);
		transition: all 0.3s ease-in-out;
		min-height: 20rpx;
		overflow: hidden;



		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		flex: 1;
}
.fui-bottom__popup-show.data-v-7a724ac3 {
		transform: translate3d(0, 0, 0);
}









