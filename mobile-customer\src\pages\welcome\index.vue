<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="h100dvh welcome overflow-y-auto">
    <view class="mb-[200rpx]">
      <view class="flex items-center flex-col relative welcome-bg">
        <image class="w100% h900rpx" src="/static/images/dt_003.jpg" mode="scaleToFill" />
        <image
          class="w-[48rpx] h-[48rpx]"
          src="/static/images/dt_005.png"
          :style="{ position: 'absolute', top: imageTop + 'px', left: '56rpx' }"
          @click="handleNotice"
        />
        <view
          class="flex flex-col items-center w-100%"
          :style="{ position: 'absolute', top: imageTop + 46 + 'px' }"
        >
          <view class="flex items-center px-[56rpx]">
            <view class="w-[120rpx]">
              <image
                class="w-[120rpx] h-[120rpx] rounded-[50%]"
                src="/static/images/dt_004.png"
                mode="scaleToFill"
              />
            </view>
            <view class="top-title">老旧住宅电梯更新补贴（公告）</view>
          </view>

          <view class="top-nanjing">
            <view class="rigion">江苏省·南京市</view>
          </view>
        </view>
      </view>
      <view class="common-page">
        <view>
          <view class="header header-1">
            <view class="number">01</view>
            <view class="divide-line" />
            <view class="title">活动介绍</view>
          </view>

          <view class="content-card">
            <!-- 活动介绍部分 -->
            <view class="intro-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_1.png"></image>
                <text class="section-title">活动介绍</text>
              </view>
              <view class="section-content">
                为深入贯彻落实国家、省、市关于推动大型模设备更新的决策部署，高效推进我市住宅老旧电梯更新改造工作，根据《关于做好电梯设备更新特别国债支持住宅老旧电梯更新改造有关工作的要求》《向市市推进大型模设备更新和消费品以旧换新实施方案》等文件规定，结合国家政策，制定我市对用超长期特别国债支持住宅老旧电梯更新改造百姓方案。
              </view>
            </view>

            <!-- 补贴时间部分 -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_2.png"></image>
                <text class="section-title">补贴时间</text>
              </view>
              <view class="section-content">
                <text class="highlight">2025.1.1-2025.12.31(先到先得，用完即止)</text>
                <text class="regular-text">
                  2025年1月1日（含）至2025年12月31日（含），面向在我市有房产且登记在册上（2009年1月1日前办理使用登记）的住宅老旧电梯更新；凡无变更使用20年以上（2004年1月1日前办理使用登记）的住宅老旧电梯均可更新。
                </text>
              </view>
            </view>

            <!-- 申请时间部分 -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_3.png"></image>
                <text class="section-title">申请时间</text>
              </view>
              <view class="section-content">
                <text class="">申请小程序上线至2025.12.31（先申请先得，用完即止）</text>
              </view>
            </view>
          </view>
        </view>
        <view class="">
          <view class="header header-2">
            <view class="number">02</view>
            <view class="divide-line" />
            <view class="title">补贴范围和标准</view>
          </view>

          <view class="content-card">
            <!-- 补贴标准 -->
            <view class="intro-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_4.png"></image>
                <text class="section-title">补贴标准</text>
              </view>
              <view class="section-content">补助标准为更新电梯每台定额补贴15万元。</view>
            </view>

            <!-- 补贴范围  -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_5.png"></image>
                <text class="section-title">补贴范围</text>
              </view>
              <view class="section-content">
                重点支持使用15年以上（2009年1月1日前办理使用登记）的住宅老旧电梯更新，优先支持使用20年以上（2004年1月1日前办理使用登记）的住宅老旧电梯更新；已列入房屋征收范围和计划的住宅，不适用本专项行动方案。
              </view>
            </view>

            <!-- 补贴原则 -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_6.png"></image>
                <text class="section-title">补贴原则</text>
              </view>
              <view class="section-content">
                对使用20年以上的住宅老旧电梯，在尊重居民意愿基础上，鼓励实施更新，根据安全评估合理使用具有较高质量和较长寿命的零部件。对使用15年以上、不满20年的住宅老旧电梯，要结合居民意愿、安全评估结果，科学评估更新改造的必要性、确定更新改造具体方式。
              </view>
            </view>
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_7.png"></image>
                <text class="section-title">房屋要求</text>
              </view>
              <view class="section-content">
                包括城镇住宅（含65年产权公寓）不包括酒店式公寓（需要核实）、商铺、办公用房等非住宅。
              </view>
            </view>
          </view>
        </view>
        <view>
          <view class="header header-3">
            <view class="number">03</view>
            <view class="divide-line" />
            <view class="title">更新流程</view>
          </view>

          <view class="content-card">
            <!-- 投票规则 -->
            <view class="intro-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_8.png"></image>
                <text class="section-title">投票规则</text>
              </view>
              <view class="section-content">
                参与投票的业主户数及其专有面积（套内面积）之和需要达到业主总数和楼栋专有面积2/3时为有效投票；参与投票的每位业主在平台投下赞同票时同步选择3个以上意向电梯品牌（按排名分获3分、2分、1分），投下赞同票户数及其专有面积（套内面积）之和达到业主总数和楼栋专有面积的3/4时，该楼栋电梯品牌投票数据纳入小区电梯品牌选票库，得票最高的品牌确定为本小区更新品牌；若排名出现并列情况，按计分进行二次排名；若计分也相同，则由平台组织公证机构进行公开摇号，确定最终电梯品牌，不再进行二次投票。
              </view>
            </view>

            <!-- 投票人  -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_9.png"></image>
                <text class="section-title">投票人</text>
              </view>
              <view class="section-content">
                为需要进行电梯更新的楼栋共有产权人，按照其房号及专有面积参与投票。
              </view>
            </view>

            <!-- 补贴原则 -->
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_90.png"></image>
                <text class="section-title">合约及支付</text>
              </view>
              <view class="section-content">
                业主选定电梯后，在平台自动生成合约。超过国债补贴15万元以外部分，由业主通过维修基金、住房公积金（平台开具提取单）等线上线下渠道支付。
              </view>
            </view>
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_91.png"></image>
                <text class="section-title">更新施工</text>
              </view>
              <view class="section-content">
                合约签署后，电梯厂商安排扫楼、方案公示、排产、旧梯拆除、新梯安装等工程，并将进度定时于平台和线下公示更新进度。
              </view>
            </view>
            <view class="time-section">
              <view class="section-header">
                <image class="icon" src="/static/images/dt_002_92.png"></image>
                <text class="section-title">竣工验收及补贴申领</text>
              </view>
              <view class="section-content">
                电梯竣工且经由特检院验收合格后，电梯厂家上传验收合格证等国债申报资料，由平台统一上报申领国债补贴并在平台公示补贴发放状态。
              </view>
            </view>
          </view>
        </view>
        <view>
          <view class="header header-4">
            <view class="number">04</view>
            <view class="divide-line" />
            <view class="title">咨询服务电话</view>
          </view>

          <view class="content-card">
            <view class="intro-section">
              <fui-table full :gap="100" :itemList="tableData" :header="headerData"></fui-table>
            </view>
          </view>
        </view>
      </view>
      <view></view>
    </view>
    <view class="bottom-zone">
      <view class="flex gap-[20rpx] py20rpx px30rpx">
        <fui-button
          class="w50%"
          height="84rpx"
          radius="42rpx"
          background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
          border-width="0"
          text="查看公示"
          @click="handleNotice"
        ></fui-button>
        <fui-button
          class="w50%"
          height="84rpx"
          radius="42rpx"
          background="linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)"
          border-width="0"
          text="立即投票"
          @click="handleVote"
        ></fui-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { showTextToast } from '@/utils/util'

const headerData = ref([
  {
    prop: 'name',
    label: '区属',
  },
  {
    prop: 'phone',
    width: 120,
    label: '电话',
  },
])

const tableData = [
  {
    name: '江北新区',
    phone: '025-58627104',
  },
  {
    name: '玄武区',
    phone: '025-52317206',
  },
  {
    name: '秦淮区',
    phone: '025-87753865',
  },
  {
    name: '建邺区',
    phone: '025-87778417 025-87778035',
  },
  {
    name: '鼓楼区',
    phone: '025-83159703',
  },
  {
    name: '栖霞区',
    phone: '025-85300367',
  },
  {
    name: '雨花台区',
    phone: '025-52883522',
  },
  {
    name: '江宁区',
    phone: '025-52753969',
  },
  {
    name: '浦口区',
    phone: '025-58211022',
  },
  {
    name: '六合区',
    phone: '025-57121868',
  },
  {
    name: '溧水区',
    phone: '025-57221969',
  },
  {
    name: '高淳区',
    phone: '025-56861693',
  },
]

const handleNotice = () => {
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}

const handleVote = () => {
  uni.switchTab({
    url: '/pages/vote/index',
  })
}

// 获取状态栏高度
const statusBarHeight = ref(0)

const imageTop = computed(() => {
  return statusBarHeight.value + 10
})

const getStatusBarHeight = () => {
  const res = uni.getSystemInfoSync()
  statusBarHeight.value = res.statusBarHeight
  console.log('statusBarHeight', statusBarHeight.value)
}
getStatusBarHeight()

onLoad(() => {
  console.log('getLaunchOptionsSync', uni.getLaunchOptionsSync())
})
</script>

<style lang="scss" scoped>
.welcome {
  // 渐变色背景
  background: #145db2;
}

.welcome-bg {
  // width: 100%;
  // height: 900rpx;
  // background-image: url('@/static/images/dt_003.jpg');
  // background-size: cover;
}

.top-title {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 56rpx;
  color: #ffffff;
  margin-left: 30rpx;
}

.top-nanjing {
  margin-top: 40rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 36rpx;
  padding: 18rpx 40rpx;

  .rigion {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #0b76e1;
  }
}

.common-page {
  padding: 40rpx;

  .header-1 {
    width: 258rpx;
  }

  .header-2 {
    width: 373rpx;
  }

  .header-3 {
    width: 265rpx;
  }

  .header-4 {
    width: 337rpx;
  }

  .header {
    height: 85rpx;
    border-radius: 20rpx 20rpx 0 0;
    display: flex;
    align-items: center;
    background-color: #0c488f;
    margin-top: 20rpx;
    color: #ffffff;
    padding-right: 20rpx;

    .number {
      margin-left: 20rpx;
      width: 35rpx;
      height: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 36rpx;
    }

    .divide-line {
      margin-left: 30rpx;
      margin-top: 5rpx;
      width: 2rpx;
      height: 36rpx;
      background: #86a4c7;
    }

    .title {
      margin-left: 22rpx;
      height: 34rpx;
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 36rpx;
    }
  }

  .content-card {
    background-color: #4a9ae0;
    border-radius: 0 20rpx 20rpx 20rpx;
    overflow: hidden;
    padding: 30rpx;

    .intro-section,
    .time-section {
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 10rpx;
        }

        .section-title {
          height: 31rpx;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 32rpx;
          color: #145db2;
          margin-bottom: 16rpx;
        }
      }

      .section-content {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;

        .highlight {
          height: 27rpx;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 26rpx;
          color: #145db2;
          line-height: 48rpx;
        }

        .regular-text {
          display: block;
        }
      }
    }
  }
}

.bottom-zone {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #145db2;
}
</style>
