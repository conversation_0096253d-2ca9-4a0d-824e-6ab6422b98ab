"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteNotice",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const props = __props;
    const content = common_vendor.computed(() => {
      return `${props.item.voteName}开始投票啦！`;
    });
    const handleClick = () => {
      common_vendor.index.navigateTo({
        url: `/pages-sub/voteNoticeDetail/index?id=${props.item.id}&communityId=${props.item.communityId}`
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.t(common_vendor.unref(content)),
        c: common_vendor.t(__props.item.createTime),
        d: common_vendor.o(handleClick)
      };
    };
  }
});
wx.createComponent(_sfc_main);
