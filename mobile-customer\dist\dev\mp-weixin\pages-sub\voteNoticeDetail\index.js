"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_util = require("../../utils/util.js");
require("../../store/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_tabs2 = common_vendor.resolveComponent("fui-tabs");
  const _easycom_fui_count_down2 = common_vendor.resolveComponent("fui-count-down");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_tabs2 + _easycom_fui_count_down2 + _easycom_fui_empty2 + _easycom_fui_button2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_tabs = () => "../../components/firstui/fui-tabs/fui-tabs.js";
const _easycom_fui_count_down = () => "../../components/firstui/fui-count-down/fui-count-down.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_tabs + _easycom_fui_count_down + TitleHeader + VoteNoticeItem + _easycom_fui_empty + _easycom_fui_button + _easycom_fui_loading)();
}
const VoteNoticeItem = () => "./components/voteNoticeItem.js";
const TitleHeader = () => "../../components/common/titleHeader.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const voteInfo = common_vendor.ref({});
    const brands = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const handleVote = () => {
      common_vendor.index.switchTab({
        url: "/pages/vote/index"
      });
    };
    const userStore = store_user.useUserStore();
    const { isLogined } = common_vendor.storeToRefs(userStore);
    const id = common_vendor.ref("");
    const communityId = common_vendor.ref("");
    const isExpanded = common_vendor.ref(false);
    const buildingList = common_vendor.ref([]);
    const fetchVoteInfo = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value
          }
        });
        console.log("queryBizVoteInfoById", res);
        const voteEndDate = common_vendor.dayjs(res.result.voteEndDate).format("YYYY-MM-DD 23:59:59");
        console.log("dayjs", common_vendor.dayjs(voteEndDate).diff(common_vendor.dayjs(), "second"));
        const voteEndSeconds = common_vendor.dayjs(voteEndDate).diff(common_vendor.dayjs(), "second");
        console.log("voteEndSeconds", utils_util.calculateVoteEndSeconds(res.result.voteEndDate));
        voteInfo.value = __spreadProps(__spreadValues({}, res.result), {
          voteEndSeconds,
          voteRateValue: parseFloat(res.result.voteRate)
        });
        if (res.result.buildingCodes || res.result.buildingNo) {
          const _buildingCodesArray = res.result.buildingCodes.split(",");
          const _buildingNoArray = res.result.buildingNo.split(",");
          buildingList.value = _buildingNoArray.map((item, index) => {
            return {
              name: `${item}幢`,
              buildingId: (_buildingCodesArray == null ? void 0 : _buildingCodesArray[index]) || "",
              value: _buildingCodesArray[index]
            };
          });
          handleBuildingChange(buildingList.value[0]);
        }
        if (res.result.communityId) {
          communityId.value = res.result.communityId;
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchTop10Brand = () => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.getVoteRankProductTop10({
          params: {
            voteId: id.value
          }
        });
        console.log("queryTop10Brand", res);
        brands.value = res.result;
      } catch (error) {
        console.log("error", error);
      }
    });
    const canVote = common_vendor.ref(false);
    const getVoteAuth = () => __async(this, null, function* () {
      if (!isLogined.value) {
        return;
      }
      try {
        const res = yield service_app_vote.getCanVote({
          params: {
            voteId: id.value
          }
        });
        canVote.value = res.result || false;
      } catch (error) {
      }
    });
    const handleBuildingChange = (e) => __async(this, null, function* () {
      const { index, buildingId } = e;
      console.log("handleBuildingChange", e);
      fetchBuildingRateByVote(buildingId);
    });
    const buildVoteInfo = common_vendor.ref({});
    const fetchBuildingRateByVote = (buildingId) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getBuildingRateByVote({
          params: {
            voteId: id.value,
            buildingIds: buildingId
          }
        });
        if (res.result && res.result.length > 0) {
          const _buildVoteInfo = res.result[0];
          buildVoteInfo.value = __spreadProps(__spreadValues({}, _buildVoteInfo), {
            voteRateValue: parseFloat(_buildVoteInfo.voteRate),
            voteAreaRateValue: parseFloat(_buildVoteInfo.voteAreaRate)
          });
        } else {
          buildVoteInfo.value = {};
        }
      } catch (error) {
        console.log("error", error);
        buildVoteInfo.value = {};
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((option) => {
      id.value = option.id;
      communityId.value = option.communityId;
      fetchVoteInfo();
      fetchTop10Brand();
      getVoteAuth();
    });
    const defaultShowCount = common_vendor.ref(1);
    common_vendor.computed(() => {
      if (isExpanded.value) {
        return brands.value;
      }
      return brands.value.slice(0, defaultShowCount.value);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(voteInfo).id
      }, common_vendor.unref(voteInfo).id ? common_vendor.e({
        b: common_vendor.t(common_vendor.unref(voteInfo).communityName),
        c: common_vendor.t(common_vendor.unref(voteInfo).buildingNo || ""),
        d: common_vendor.t(common_vendor.unref(voteInfo).communityLocation),
        e: common_vendor.t(common_vendor.unref(voteInfo).voteEndDate),
        f: common_vendor.unref(voteInfo).voteRateValue,
        g: common_vendor.t(common_vendor.unref(voteInfo).voteCount),
        h: common_vendor.t(common_vendor.unref(voteInfo).voteRate),
        i: common_vendor.o(handleBuildingChange),
        j: common_vendor.p({
          tabs: common_vendor.unref(buildingList),
          center: true,
          selectedColor: "#005ED1",
          sliderBackground: "#005ED1"
        }),
        k: common_vendor.unref(buildVoteInfo).voteRateValue,
        l: common_vendor.t(common_vendor.unref(buildVoteInfo).voteCount),
        m: common_vendor.t(common_vendor.unref(buildVoteInfo).voteRate),
        n: common_vendor.unref(buildVoteInfo).voteAreaRateValue,
        o: common_vendor.t(common_vendor.unref(buildVoteInfo).voteArea),
        p: common_vendor.t(common_vendor.unref(buildVoteInfo).voteAreaRate),
        q: common_vendor.t(!common_vendor.unref(buildVoteInfo).pass ? "本楼栋暂未达到有效选票数，请尽快投票。" : "本楼栋已达到有效选票数。"),
        r: common_vendor.p({
          isDays: true,
          isColon: false,
          size: "42",
          width: "50",
          height: "50",
          borderColor: "transparent",
          background: "transparent",
          color: "#005ED1",
          colonColor: "#005ED1",
          value: common_vendor.unref(voteInfo).voteEndSeconds
        }),
        s: common_vendor.p({
          title: "小区投票品牌当前排名"
        }),
        t: common_vendor.unref(brands).length > 0
      }, common_vendor.unref(brands).length > 0 ? {
        v: common_vendor.f(common_vendor.unref(brands), (brand, index, i0) => {
          return {
            a: "419b10d3-4-" + i0 + ",419b10d3-0",
            b: common_vendor.p({
              brand,
              index,
              ["show-more-sku"]: true
            }),
            c: index
          };
        })
      } : {
        w: common_vendor.p({
          src: "/static/images/img_data_3x.png",
          width: 386,
          height: 280,
          title: "暂无数据"
        })
      }, {
        x: common_vendor.unref(canVote)
      }, common_vendor.unref(canVote) ? {
        y: common_vendor.o(handleVote),
        z: common_vendor.p({
          height: "84rpx",
          radius: "42rpx",
          background: "linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%);",
          ["border-width"]: "0",
          text: "我是业主我要投票"
        })
      } : {}) : {}, {
        A: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        B: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-419b10d3"]]);
wx.createPage(MiniProgramPage);
