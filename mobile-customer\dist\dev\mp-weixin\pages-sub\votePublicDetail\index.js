"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
require("../../store/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (TitleHeader + WinBrand + BuildingsVote + voteBrandRank + _easycom_fui_loading)();
}
const TitleHeader = () => "../../components/common/titleHeader.js";
const voteBrandRank = () => "../votehasEndResultDetail/components/voteBrandRank.js";
const WinBrand = () => "./components/winBrand.js";
const BuildingsVote = () => "./components/buildingsVote.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    common_vendor.ref(["投票结果", "得票排名", "我的投票"]);
    const loading = common_vendor.ref(false);
    const voteData = common_vendor.ref({});
    const voteResultRank = common_vendor.ref([]);
    common_vendor.ref([]);
    common_vendor.ref(0);
    const id = common_vendor.ref("");
    const winBrand = common_vendor.ref({});
    const fetchVoteDetail = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value
          }
        });
        console.log("fetchVoteDetail", res);
        voteData.value = __spreadProps(__spreadValues({}, res.result), {
          voteRateValue: parseFloat(res.result.voteRate.replace("%", ""))
        });
        winBrand.value = {
          vote_rate: res.result.voteRateProduct,
          vote_count: res.result.voteCountProduct,
          brand_name: res.result.resultBrandName,
          brandLogo: res.result.resultBrandPic,
          skuCode_dictText: res.result.skuCode_dictText,
          product_name: res.result.resultProductName,
          pic_url: res.result.resultBrandPic,
          productModel: res.result.productModel,
          productId: res.result.productId,
          price: res.result.price
        };
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchVoteResultRank = () => __async(this, null, function* () {
      var _a;
      try {
        const res = yield service_app_vote.getVoteRankProductTop10({
          params: {
            voteId: id.value,
            unVoted: 1
          }
        });
        console.log("fetchVoteResultRank", res);
        voteResultRank.value = ((_a = res.result) == null ? void 0 : _a.map((item) => __spreadProps(__spreadValues({}, item), {
          skuName: item.win_sku || "5年维保",
          showMoreSku: false
        }))) || [];
      } catch (error) {
        console.log("error", error);
      }
    });
    common_vendor.ref({ reduction: 0, rate: 1 });
    const buildVoteList = common_vendor.ref([]);
    const fetchBuildingsRateByVote = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getBuildingRateByVote({
          params: {
            voteId: id.value
          }
        });
        if (res.result && res.result.length > 0) {
          buildVoteList.value = res.result.map((item) => __spreadProps(__spreadValues({}, item), {
            voteRateValue: parseFloat(item.voteRate),
            voteAreaRateValue: parseFloat(item.voteAreaRate)
          }));
        } else {
          buildVoteList.value = [];
        }
      } catch (error) {
        console.log("error", error);
        buildVoteList.value = [];
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((options) => __async(this, null, function* () {
      id.value = options.id;
      yield fetchVoteDetail();
      fetchBuildingsRateByVote();
      fetchVoteResultRank();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(voteData).communityName),
        b: common_vendor.t(common_vendor.unref(voteData).buildingNo || ""),
        c: common_vendor.t(common_vendor.unref(voteData).communityLocation),
        d: common_vendor.t(common_vendor.unref(voteData).voteEndDate),
        e: common_vendor.unref(voteData).voteRateValue,
        f: common_vendor.t(common_vendor.unref(voteData).voteCount),
        g: common_vendor.t(common_vendor.unref(voteData).voteRate),
        h: common_vendor.p({
          title: "获选品牌"
        }),
        i: common_vendor.p({
          brand: common_vendor.unref(winBrand)
        }),
        j: common_vendor.p({
          title: "投票情况"
        }),
        k: common_vendor.p({
          voteBuildingsList: common_vendor.unref(buildVoteList)
        }),
        l: common_vendor.p({
          brands: common_vendor.unref(voteResultRank),
          ["show-price"]: false,
          ["show-more-sku"]: true
        }),
        m: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        n: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5d744015"]]);
wx.createPage(MiniProgramPage);
