<layout-default-uni class="data-v-e46a49ed" u-s="{{['d']}}" u-i="e46a49ed-0" bind:__l="__l"><view class="bg-_a__a_f7f7f7_a_ h100vh overflow-y-auto data-v-e46a49ed"><view wx:if="{{a}}" class="bg-_a__a_ffffff_a_ rounded-20rpx px30rpx py30rpx m20rpx data-v-e46a49ed"><view class="text-32rpx font-bold text-center data-v-e46a49ed">授权须知</view><view class="text-26rpx mt10rpx data-v-e46a49ed">1、授权人只可以授权给自己的父母、伴侣、子女。</view><view class="text-26rpx mt10rpx data-v-e46a49ed">2、授权后，被授权人将会有权力代替授权人进行投票。</view><view class="text-26rpx mt10rpx data-v-e46a49ed">3、被授权人投票的结果视同授权人本人投票的结果。</view></view><view wx:else class="auth-list data-v-e46a49ed"><view wx:for="{{b}}" wx:for-item="item" wx:key="j" class="auth-item data-v-e46a49ed"><view class="auth-header data-v-e46a49ed"><text class="relation data-v-e46a49ed">{{item.a}}</text><text class="{{['data-v-e46a49ed', 'auth-status', item.c]}}">{{item.b}}</text></view><view class="auth-content data-v-e46a49ed"><view class="auth-info data-v-e46a49ed"><view class="flex justify-between data-v-e46a49ed"><view class="info-row flex-1 data-v-e46a49ed"><text class="info-label data-v-e46a49ed">姓名：</text><text class="info-value data-v-e46a49ed">{{item.d}}</text></view><view class="info-row w-58_a_ justify-end data-v-e46a49ed"><text class="info-label data-v-e46a49ed">身份证号：</text><text class="info-value data-v-e46a49ed">{{item.e}}</text></view></view><view class="info-row data-v-e46a49ed"><text class="info-label data-v-e46a49ed">授权时间：</text><text class="info-value data-v-e46a49ed">{{item.f}}</text></view></view><view class="auth-action data-v-e46a49ed"><view class="data-v-e46a49ed"><button class="{{['data-v-e46a49ed', 'btn-cancel', item.h]}}" bindtap="{{item.i}}">{{item.g}}</button></view></view></view></view></view><view class="bottom-zone data-v-e46a49ed"><view class="flex gap-_a_20rpx_a_ py20rpx px30rpx data-v-e46a49ed"><fui-button wx:if="{{d}}" class="w50_a_ data-v-e46a49ed" bindclick="{{c}}" u-i="e46a49ed-1,e46a49ed-0" bind:__l="__l" u-p="{{d}}"></fui-button><fui-button wx:if="{{f}}" class="w50_a_ data-v-e46a49ed" bindclick="{{e}}" u-i="e46a49ed-2,e46a49ed-0" bind:__l="__l" u-p="{{f}}"></fui-button></view></view><fui-loading wx:if="{{g}}" class="data-v-e46a49ed" u-i="e46a49ed-3,e46a49ed-0" bind:__l="__l" u-p="{{h}}"/></view></layout-default-uni>