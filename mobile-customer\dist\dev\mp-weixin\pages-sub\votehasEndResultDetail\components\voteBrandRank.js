"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  (TitleHeader + VoteNoticeItem)();
}
const TitleHeader = () => "../../../components/common/titleHeader.js";
const VoteNoticeItem = () => "../../voteNoticeDetail/components/voteNoticeItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteBrandRank",
  props: {
    brands: {
      type: Array,
      default: () => []
    },
    showPrice: {
      type: Boolean,
      default: true
    },
    showMoreSku: {
      type: Boolean,
      default: false
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: "品牌得票排名"
        }),
        b: common_vendor.f(__props.brands, (brand, index, i0) => {
          return {
            a: "d394792b-1-" + i0,
            b: common_vendor.p({
              brand,
              index,
              ["show-price"]: __props.showPrice,
              ["show-more-sku"]: __props.showMoreSku
            }),
            c: index
          };
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d394792b"]]);
wx.createComponent(Component);
