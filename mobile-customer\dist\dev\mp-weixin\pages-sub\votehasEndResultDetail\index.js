"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
require("../../store/index.js");
const utils_util = require("../../utils/util.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  const _easycom_fui_tabs2 = common_vendor.resolveComponent("fui-tabs");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_tag2 + _easycom_fui_tabs2 + _easycom_fui_loading2 + _component_layout_default_uni)();
}
const _easycom_fui_tag = () => "../../components/firstui/fui-tag/fui-tag.js";
const _easycom_fui_tabs = () => "../../components/firstui/fui-tabs/fui-tabs.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
if (!Math) {
  (_easycom_fui_tag + _easycom_fui_tabs + voteResult + voteBrandRank + voteMyVoteRank + _easycom_fui_loading)();
}
const voteResult = () => "./components/voteResult.js";
const voteBrandRank = () => "./components/voteBrandRank.js";
const voteMyVoteRank = () => "./components/myVoteRank.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const tabs = common_vendor.ref(["投票结果", "得票排名", "我的投票"]);
    const loading = common_vendor.ref(false);
    const voteData = common_vendor.ref({});
    const voteResultRank = common_vendor.ref([]);
    const myVoteRank = common_vendor.ref([]);
    const currentTab = common_vendor.ref(0);
    const id = common_vendor.ref("");
    const changeTab = (option) => {
      console.log("changeTab", option);
      currentTab.value = option.index;
    };
    const itemShowMore = common_vendor.ref(true);
    const handleShowMore = () => {
      itemShowMore.value = !itemShowMore.value;
    };
    const fetchVoteDetail = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value
          }
        });
        console.log("fetchVoteDetail", res);
        voteData.value = __spreadProps(__spreadValues({}, res.result), {
          voteRateValue: parseFloat(res.result.voteRate.replace("%", ""))
        });
        fetchAddressList(res.result.communityId, res.result.buildingCodes);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const fetchVoteResultRank = () => __async(this, null, function* () {
      var _a;
      try {
        const res = yield service_app_vote.getVoteRankProductTop10({
          params: {
            voteId: id.value
          }
        });
        console.log("fetchVoteResultRank", res);
        voteResultRank.value = ((_a = res.result) == null ? void 0 : _a.map((item) => __spreadProps(__spreadValues({}, item), {
          skuName: item.win_sku || "5年维保",
          showMoreSku: false
          // price: calculatePrePrice(item.price, formulaParams.value),
        }))) || [];
      } catch (error) {
        console.log("error", error);
      }
    });
    const fetchMyVoteRank = (_buildingId) => __async(this, null, function* () {
      var _a;
      try {
        const res = yield service_app_vote.getVoteInfoByCurUser({
          params: {
            voteId: id.value,
            buildingId: _buildingId
          }
        });
        console.log("fetchMyVote", res);
        myVoteRank.value = ((_a = res.result) == null ? void 0 : _a.map((item) => __spreadProps(__spreadValues({}, item), {
          vote_rate: item.voteRate,
          vote_count: item.voteCount,
          brand_name: item.brandName,
          product_name: item.productName,
          pic_url: item.picUrl,
          price: utils_util.calculatePrePrice(item.price, formulaParams.value)
        }))) || [];
      } catch (error) {
        console.log("error", error);
      }
    });
    const formulaParams = common_vendor.ref({ reduction: 0, rate: 1 });
    const fetchPrePrice = () => __async(this, null, function* () {
      try {
        const res = yield service_app_vote.getPrePrice({
          params: {
            communityId: voteData.value.communityId,
            buildingIds: voteData.value.buildingCodes
          }
        });
        formulaParams.value = res.result;
      } catch (error) {
        console.log("error", error);
      }
    });
    const allAddressList = common_vendor.ref([]);
    const buildingList = common_vendor.computed(() => {
      var _a, _b;
      return ((_b = (_a = allAddressList.value) == null ? void 0 : _a.map((c) => {
        return __spreadProps(__spreadValues({}, c), {
          name: `${c.buildingNo}幢`
        });
      })) == null ? void 0 : _b.filter((item, index, self) => {
        return index === self.findIndex((t) => t.buildingId === item.buildingId);
      })) || [];
    });
    const fetchAddressList = (communityId, buildingIds) => __async(this, null, function* () {
      const res = yield service_app_vote.getAllGrantedAndSelfList({
        params: {
          communityId,
          buildingIds
        }
      });
      allAddressList.value = res.result;
      if (res.result.length > 0) {
        handleBuildingChange(res.result[0]);
      }
    });
    const handleBuildingChange = (e) => __async(this, null, function* () {
      const { index, buildingId } = e;
      console.log("handleBuildingChange", e);
      fetchBuildingRateByVote(buildingId);
      fetchMyVoteRank(buildingId);
    });
    const buildVoteInfo = common_vendor.ref({});
    const fetchBuildingRateByVote = (buildingId) => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getBuildingRateByVote({
          params: {
            voteId: id.value,
            buildingIds: buildingId
          }
        });
        if (res.result && res.result.length > 0) {
          const _buildVoteInfo = res.result[0];
          buildVoteInfo.value = __spreadProps(__spreadValues({}, _buildVoteInfo), {
            voteRateValue: parseFloat(_buildVoteInfo.voteRate),
            voteAreaRateValue: parseFloat(_buildVoteInfo.voteAreaRate)
          });
        } else {
          buildVoteInfo.value = {};
        }
      } catch (error) {
        console.log("error", error);
        buildVoteInfo.value = {};
      } finally {
        loading.value = false;
      }
    });
    const fetchSubsidyPrice = () => __async(this, null, function* () {
      var _a;
      const res = yield service_app_vote.querySubsidyPrice({});
      console.log("获取国补每台价格--fetchSubsidyPrice", res);
      voteData.value.subsidyPrice = ((_a = res.result) == null ? void 0 : _a.subsidiesPrice) || 0;
    });
    common_vendor.onLoad((options) => __async(this, null, function* () {
      id.value = options.id;
      yield fetchVoteDetail();
      yield fetchPrePrice();
      fetchVoteResultRank();
      fetchSubsidyPrice();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(userInfo).realname),
        b: common_vendor.t(common_vendor.unref(voteData).communityName),
        c: common_vendor.f(common_vendor.unref(allAddressList), (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.location),
            b: item.myself == 1
          }, item.myself == 1 ? {
            c: "c8c9bce9-1-" + i0 + ",c8c9bce9-0",
            d: common_vendor.p({
              text: "被授权",
              type: "success",
              padding: ["6rpx", "6rpx"]
            })
          } : {}, {
            e: index
          });
        }),
        d: common_vendor.t(common_vendor.unref(voteData).voteEndDate),
        e: common_vendor.unref(voteData).voteRateValue,
        f: common_vendor.t(common_vendor.unref(voteData).voteCount),
        g: common_vendor.t(common_vendor.unref(voteData).voteRate),
        h: common_vendor.o(handleBuildingChange),
        i: common_vendor.p({
          tabs: common_vendor.unref(buildingList),
          center: true,
          selectedColor: "#005ED1",
          sliderBackground: "#005ED1"
        }),
        j: common_vendor.unref(buildVoteInfo).voteRateValue,
        k: common_vendor.t(common_vendor.unref(buildVoteInfo).voteCount),
        l: common_vendor.t(common_vendor.unref(buildVoteInfo).voteRate),
        m: common_vendor.unref(buildVoteInfo).voteAreaRateValue,
        n: common_vendor.t(common_vendor.unref(buildVoteInfo).voteArea),
        o: common_vendor.t(common_vendor.unref(buildVoteInfo).voteAreaRate),
        p: common_vendor.t(!common_vendor.unref(buildVoteInfo).pass ? "本楼栋暂未达到有效选票数，请尽快投票。" : "本楼栋已达到有效选票数。"),
        q: common_vendor.unref(itemShowMore),
        r: common_assets._imports_0$6,
        s: common_vendor.n(common_vendor.unref(itemShowMore) ? "image-active" : "image-no-active"),
        t: common_vendor.o(handleShowMore),
        v: common_vendor.unref(buildingList).length > 0,
        w: common_vendor.o(changeTab),
        x: common_vendor.p({
          tabs: common_vendor.unref(tabs),
          center: true
        }),
        y: common_vendor.p({
          voteData: common_vendor.unref(voteData)
        }),
        z: common_vendor.unref(currentTab) === 0,
        A: common_vendor.p({
          brands: common_vendor.unref(voteResultRank),
          showPrice: false,
          showMoreSku: true
        }),
        B: common_vendor.unref(currentTab) === 1,
        C: common_vendor.p({
          brands: common_vendor.unref(myVoteRank)
        }),
        D: common_vendor.unref(currentTab) === 2,
        E: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        F: common_vendor.p({
          isMask: true
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c8c9bce9"]]);
wx.createPage(MiniProgramPage);
