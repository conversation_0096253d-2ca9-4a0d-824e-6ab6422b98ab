"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  _easycom_fui_tag2();
}
const _easycom_fui_tag = () => "../../../components/firstui/fui-tag/fui-tag.js";
if (!Math) {
  _easycom_fui_tag();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "installRecordItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const handleClick = () => {
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(__props.item.communityName),
        b: common_vendor.t(__props.item.buildingNo ? `栋${__props.item.buildingNo}` : ""),
        c: common_vendor.t(__props.item.totalCount),
        d: common_vendor.t(__props.item.voteTime),
        e: common_vendor.t(__props.item.voteTime),
        f: !__props.item.end
      }, !__props.item.end ? {
        g: common_vendor.p({
          type: "success",
          text: "交付使用",
          padding: ["8rpx", "16rpx"]
        })
      } : {
        h: common_vendor.p({
          type: "success",
          background: "#aaaaaa",
          padding: ["8rpx", "16rpx"],
          color: "#fff",
          text: "电梯出厂"
        })
      }, {
        i: common_vendor.o(handleClick)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-affde53a"]]);
wx.createComponent(Component);
