<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '纠错',
  },
}
</route>

<template>
  <view class="bg-[#f7f7f7] h100vh overflow-y-auto">
    <view class="px3 mx3 my3 rounded-20rpx bg-[#ffffff]">
      <view>
        <fui-list-cell :padding="listRowPadding">
          <text class="fui-text__label">您的联系号码</text>
          <text class="fui-text__explain">{{ userInfo.phone }}</text>
        </fui-list-cell>
        <fui-list-cell :padding="listRowPadding">
          <text class="fui-text__label">您的姓名</text>
          <text class="fui-text__explain">{{ userInfo.realname }}</text>
        </fui-list-cell>
        <fui-list-cell :padding="listRowPadding">
          <text class="fui-text__label">您的身份证号</text>
          <text class="fui-text__explain">{{ userInfo.idCard }}</text>
        </fui-list-cell>
        <fui-list-cell :padding="listRowPadding" v-if="houseId">
          <text class="fui-text__label">小区名称</text>
          <text class="fui-text__explain">{{ communityName }}</text>
        </fui-list-cell>
        <fui-list-cell :padding="listRowPadding" :bottomBorder="false" v-if="houseId">
          <text class="fui-text__label">房屋号</text>
          <text class="fui-text__explain">{{ location }}</text>
        </fui-list-cell>
      </view>
    </view>

    <view class="px3 mx3 rounded-20rpx bg-[#ffffff]">
      <view class="py10rpx">
        <view><text class="fui-text__explain">请描述您的问题并上传相关的图片或文件</text></view>
      </view>
      <view>
        <fui-form ref="formRef" :show="false" :labelSize="24">
          <fui-form-item
            label="请输入您的问题"
            :bottomBorder="false"
            prop="description"
            error-align="left"
            :labelWidth="220"
            :padding="listRowPadding"
          >
            <template v-slot:vertical>
              <fui-textarea
                height="160rpx"
                maxlength="-1"
                :padding="['0', '0', '24rpx', '0']"
                :border-bottom="true"
                :border-top="false"
                :size="26"
                placeholder="请输入您的问题"
                v-model="formData.description"
              ></fui-textarea>
              <fui-form-item
                prop="files"
                :bottomBorder="false"
                error-align="left"
                label="上传图片"
                :padding="listRowPadding"
              >
                <template v-slot:vertical>
                  <fui-upload
                    :max="9"
                    immediate
                    :url="uploadUrl"
                    ref="uploadRef"
                    radius="20"
                    width="160"
                    height="160"
                    :header="uploadHeader"
                    @success="successFile"
                    @error="errorFile"
                    @complete="completeFile"
                    @delete="deleteFile"
                  ></fui-upload>
                </template>
              </fui-form-item>
            </template>
          </fui-form-item>
        </fui-form>
      </view>
    </view>

    <view class="px3 my2">
      <fui-button @click="handleSubmit" text="立即提交" />
    </view>
    <fui-loading v-if="isLoading" isMask></fui-loading>
    <!-- 友情提醒 -->
    <fui-landscape :show="showModal" :closable="false">
      <view class="fui-ani__box">
        <image class="fui-hd__img" src="/static/images/dt_008.jpg" mode="widthFix"></image>
        <view class="fui-flex__center">
          <view class="w100%">
            <view class="my20rpx">
              <view class="text-40rpx font-bold center mb10rpx">提交成功</view>

              <view class="text-24rpx text-[#999] leading-44rpx tip-content">
                我们将尽快核实您的反馈信息，并给您回复，请留意消息中心内的信息，给您带来不变，非常抱歉
              </view>
            </view>
            <fui-button
              class="text-center"
              radius="100rpx"
              width="100%"
              disabled-background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              background="linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)"
              borderColor="rgba(0,0,0,0)"
              border-width="0"
              @click="handleCloseModal"
            >
              关闭
            </fui-button>
          </view>
        </view>
      </view>
    </fui-landscape>
  </view>
</template>

<script lang="ts" setup>
import { addErrorCorrectionHouse } from '@/service/app'
import { useUserStore } from '@/store'
import { getEnvBaseUrl } from '@/utils'
import { showTextToast } from '@/utils/util'
import { storeToRefs } from 'pinia'

const listRowPadding = ['28rpx', '0', '28rpx', '0']

const isLoading = ref(false)

const userStore = useUserStore()

const { userInfo } = storeToRefs(userStore)

// 表单
const formRef = ref()

// 上传
const uploadRef = ref()

// 上传头
const uploadHeader = ref({
  'x-access-token': userInfo.value.token,
})

// 上传url
const baseUrl = getEnvBaseUrl()
const uploadUrl = ref(`${baseUrl}/s3/upload`)

const rules = [
  {
    name: 'description',
    rule: ['required'],
    msg: ['请输入故障描述'],
  },
]

// 表单数据
const formData = ref({
  description: '',
  files: [],
})

// 上传成功
const successFile = (e: any) => {
  console.log('successFile', e)

  const res = e.res.data
  if (res) {
    const file = JSON.parse(res)
    console.log('file', file)
    if (file.code === 200) {
      showTextToast('上传成功')
      formData.value.files.push(file.result)
    }
  }
}

// 上传失败
const errorFile = (e: any) => {
  console.log('errorFile', e)
  showTextToast('上传失败')
}

// 上传完成
const completeFile = (e: any) => {
  console.log('completeFile', e)
}

// 预览
const previewFile = (e: any) => {
  console.log('previewFile', e)
}

// 删除
const deleteFile = async (e: any) => {
  console.log('deleteFile', e)
  //   const file = formData.value.files[e.index]
  //   const res = await removeByRelationFileId({
  //     params: {
  //       relationId: formData.value.picRelationId,
  //       fileId: file.id,
  //     },
  //   })
  //   console.log('res', res)
  //   if (res.code === 0) {
  //     showTextToast('删除成功')
  //     formData.value.files.splice(e.index, 1)
  //   }

  formData.value.files.splice(e.index, 1)
}

// 提交
const handleSubmit = async () => {
  console.log('formData', formData.value)
  if (!formData.value.description) {
    showTextToast('请输入您的问题')
    return
  }

  uni.showModal({
    title: '提示',
    content: '是否提交？',
    success: async (res) => {
      if (res.confirm) {
        try {
          isLoading.value = true
          const res = await addErrorCorrectionHouse({
            body: {
              remark: formData.value.description,
              imgUrl: formData.value.files.join(','),
              userHouseId: houseId.value,
            },
          })
          console.log('res', res)
          if (res.code === 200) {
            // showTextToast('提交成功')
            showModal.value = true
          }
        } catch (error) {
          console.log('error', error)
        } finally {
          isLoading.value = false
        }
      } else if (res.cancel) {
        console.log('用户点击了取消')
      }
    },
  })
}

// 弹窗
const showModal = ref(false)
// 关闭弹窗
const handleCloseModal = () => {
  showModal.value = false
  // 返回上一页
  uni.navigateBack()
}

const houseId = ref('')
const location = ref('')
const communityName = ref('')

onLoad((options: { id: string; location: string; communityName: string }) => {
  console.log('options', options)
  if (options) {
    houseId.value = options.id
    location.value = options.location
    communityName.value = options.communityName
  }
})
</script>

<style lang="scss" scoped>
.fui-text__label {
  font-size: 24rpx;
}

.fui-text__explain {
  color: #666;
  font-size: 24rpx;
}
.fui-ani__box {
  width: 640rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding-bottom: 24rpx;
}
.fui-flex__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}
.fui-hd__img {
  width: 100%;
  height: 80rpx;
  display: block;
  border-radius: 20rpx;
}
</style>
