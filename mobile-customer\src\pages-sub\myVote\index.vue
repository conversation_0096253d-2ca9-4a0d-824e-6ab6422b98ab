<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的投票',
  },
}
</route>

<template>
  <z-paging
    ref="paging"
    :auto-hide-loading-after-first-loaded="false"
    loading-full-fixed
    :paging-style="{ 'background-color': '#f7f7f7' }"
    v-model="dataList"
    :auto-show-back-to-top="true"
    :back-to-top-bottom="200"
    @query="queryList"
    :auto="true"
    lower-threshold="150rpx"
  >
    <template #loading>
      <fui-loading isMask></fui-loading>
    </template>
    <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
    <template #empty>
      <fui-empty
        src="/static/images/img_data_3x.png"
        :width="386"
        :height="280"
        isFixed
        title="暂无数据"
      ></fui-empty>
    </template>

    <view class="mt20rpx">
      <view class="mb-20rpx" v-for="item in dataList" :key="item.id">
        <my-vote-item :item="item" />
      </view>
    </view>
    <template #bottom>
      <safe-bottom-zone></safe-bottom-zone>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import myVoteItem from './components/myVoteItem.vue'
import { getMyVote } from '@/service/app'

const loading = ref(false)

const paging = ref(null)
// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
const dataList = ref([])

// 投票数据
const voteData = ref([])

const queryList = async (pageNo, pageSize) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
  }

  console.log('params', params)
  // 此处请求仅为演示，请替换为自己项目中的请求
  try {
    const res = await getMyVote({ params })
    console.log('getMyVote', res)

    paging.value.complete(res?.result?.records || [], true)
  } catch (error) {
    paging.value.complete(false)
  }
}

onLoad(() => {})
</script>

<style lang="scss" scoped>
//
</style>
