
.fui-table__wrap.data-v-312d7e5c {
		position: relative;

		box-sizing: border-box;
		margin: 0 auto;


		overflow: hidden;

		font-size: 0;
}
.fui-table__border-top.data-v-312d7e5c {
		border-top-width: 1px;
		border-top-style: solid;
}
.fui-table__border-left.data-v-312d7e5c {
		border-left-width: 1px;
		border-left-style: solid;
}
.fui-table__border-right.data-v-312d7e5c {
		border-right-width: 1px;
		border-right-style: solid;
}
.fui-table__border-bottom.data-v-312d7e5c {
		border-bottom-width: 1px;
		border-bottom-style: solid;
}
.fui-table--tr.data-v-312d7e5c {

		display: flex;
		box-sizing: border-box;

		flex-direction: row;
}
.fui-table--empty.data-v-312d7e5c {

		width: 100%;
		display: flex;
		box-sizing: border-box;

		flex-direction: row;
		justify-content: center;
}
.fui-table__empty-ab.data-v-312d7e5c {
		position: absolute;
		left: 0;
		top: 96rpx;

		z-index: 2;
}
.fui-table__empty-text.data-v-312d7e5c {

		width: 100%;




		font-weight: 400;
		text-align: center;
		padding: 48rpx 0;
}
.fui-table--td.data-v-312d7e5c {

		display: flex;
		box-sizing: border-box;
		flex-shrink: 0;

		flex-direction: row;
		align-items: center;
		padding-left: 16rpx;
		padding-right: 16rpx;
		position: relative;
}
.fui-table__sort-icon.data-v-312d7e5c {
		position: absolute;
		right: 40rpx;
		top: 0;
		bottom: 0;

		display: flex;

		flex-direction: row;
		align-items: center;
}
.fui-table__td-sk.data-v-312d7e5c {
		position: absolute;
		left: -1px;
		width: 1px;
		top: 0;
		bottom: 0;
}
.fui-table__td-wrap.data-v-312d7e5c {
		flex-wrap: wrap;
}
.fui-table--td-text.data-v-312d7e5c {
		font-weight: 400;
}
.fui-table--td-img.data-v-312d7e5c {

		display: block;
}
.fui-table--btn.data-v-312d7e5c {

		display: flex;
		flex-shrink: 0;

		align-items: center;
		justify-content: center;



		text-align: center;
		padding: 2px 0;
}
.fui-table--btn.data-v-312d7e5c:active {
		opacity: .5;
}
.fui-td__btn-ml.data-v-312d7e5c {
		margin-left: 24rpx;
}
.fui-table--header-fixed.data-v-312d7e5c {
		position: sticky;
		top: 0;

		z-index: 12;

		left: 0;
		right: 0;
}
.fui-table--col-fixed.data-v-312d7e5c {
		position: sticky;

		z-index: 2;
}
.fui-table__center.data-v-312d7e5c {
		justify-content: center;
		text-align: center;
}
.fui-table__right.data-v-312d7e5c {
		justify-content: flex-end;
		text-align: right;
}
.fui-text__center.data-v-312d7e5c {
		text-align: center;
}
.fui-text__right.data-v-312d7e5c {
		text-align: right;
}
.fui-td__ellipsis.data-v-312d7e5c {

		overflow: hidden;
		white-space: nowrap;




		text-overflow: ellipsis;
}
.fui-td__wrap.data-v-312d7e5c {
		word-break: break-all;
}
.fui-table__scroll-view.data-v-312d7e5c {
		position: relative;
}
.fui-table--inner.data-v-312d7e5c {
		display: inline-block;
		padding-bottom: 40rpx;
}
.fui-table__checkbox.data-v-312d7e5c {
		font-size: 0;
		color: rgba(0, 0, 0, 0);
		width: 36rpx;
		height: 36rpx;
		border-width: 1px;
		border-style: solid;

		display: inline-flex;
		box-sizing: border-box;
		vertical-align: top;
		flex-shrink: 0;

		flex-direction: row;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		position: relative;
		border-radius: 8rpx;
}
.fui-table__checkbox-color.data-v-312d7e5c {
		background: var(--fui-color-primary, #465CFF) !important;
		border-color: var(--fui-color-primary, #465CFF) !important;
}
.fui-table__disabled.data-v-312d7e5c {
		opacity: .5;
}
.fui-table__checkmark.data-v-312d7e5c {
		width: 18rpx;
		height: 36rpx;
		border-bottom-style: solid;
		border-bottom-width: 3px;
		border-bottom-color: #FFFFFF;
		border-right-style: solid;
		border-right-width: 3px;
		border-right-color: #FFFFFF;

		box-sizing: border-box;
		transform: rotate(45deg) scale(0.5) translateZ(0);




		transform-origin: 54% 48%;
}
