<template>
  <view class="container">
    <!-- 房产信息卡片 -->
    <view class="property-card">
      <!-- 房产图片区域 -->
      <!-- <view class="image-section">
        <image
          class="property-image"
          src="https://dummyimage.com/300x200/4CAF50/ffffff?text=仁恒城市星光"
          mode="aspectFill"
        />
      </view> -->

      <!-- 卡片内容区域 -->
      <view class="content-section">
        <!-- 标题和待交付标签 -->
        <view class="title-row">
          <text class="property-title">仁恒城市星光</text>
          <view class="">
            <text class="delivery-text">待交付</text>
          </view>
        </view>

        <view class="flex items-center">
          <!-- 品牌标签 -->
          <view class="brand-tag orange-tag">品牌标签</view>
          <!-- 品牌名称 -->
          <view class="brand-name">品牌名称</view>
        </view>

        <view class="price-row">
          <text class="price-text">5年保修</text>
        </view>

        <!-- 户型建筑面积和均价户型面积 -->
        <view class="area-row">
          <text class="area-text">维修基金支付：￥2000</text>
          <text class="area-text">待支付金额：￥680</text>
        </view>

        <!-- 特色付信息 -->
        <view class="feature-row">
          <text class="feature-label">待支付</text>
          <text class="feature-desc">共计119户需要支付，已经100户支付完成</text>
        </view>

        <!-- 底部操作按钮 -->
        <view class="action-buttons">
          <view class="flex-1">
            <fui-button
              height="56rpx"
              :size="24"
              background="#eee"
              color="#000"
              text="查看支付情况"
              @click="handleViewDetail"
            />
          </view>
          <view class="flex-1">
            <fui-button
              height="56rpx"
              :size="24"
              background="#eee"
              color="#000"
              text="查看投票结果"
              @click="handleViewDetail"
            />
          </view>
          <view class="flex-1">
            <fui-button
              height="56rpx"
              :size="24"
              background="#eee"
              color="#000"
              text="查看合约"
              @click="handleViewHouses"
            />
          </view>
          <view class="flex-1">
            <fui-button
              height="56rpx"
              :size="24"
              background="#FF9800"
              text="立即支付"
              @click="handleCollect"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 查看详情
const handleViewDetail = () => {
  uni.navigateTo({
    url: '/pages-sub/propertyDetail/index',
  })
}

// 查看房源
const handleViewHouses = () => {
  uni.navigateTo({
    url: '/pages-sub/houseList/index',
  })
}

// 收藏
const handleCollect = () => {
  uni.showToast({
    title: '收藏成功',
    icon: 'success',
  })
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
}

.property-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

// 图片区域
.image-section {
  height: 200rpx;
  overflow: hidden;
}

.property-image {
  width: 100%;
  height: 100%;
}

// 内容区域
.content-section {
  padding: 20rpx;
}

// 标题行
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.property-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.delivery-text {
  font-size: 24rpx;
  color: #ff6b35;
}

// 价格行
.price-row {
  margin-bottom: 12rpx;
}

.price-text {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 500;
}

// 面积行
.area-row {
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.area-text {
  font-size: 24rpx;
  color: #666666;
}

// 特色行
.feature-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20rpx;
  background-color: #eee;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}

.feature-label {
  font-size: 26rpx;
  font-weight: 500;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
  margin-left: 20rpx;
}

// 操作按钮
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12rpx;
  padding-top: 16rpx;
}

.brand-name {
  font-size: 26rpx;
  margin: 0 0 0 10rpx;
}

.brand-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.orange-tag {
  background-color: #fff3e0;
  color: #e65100;
}
</style>
