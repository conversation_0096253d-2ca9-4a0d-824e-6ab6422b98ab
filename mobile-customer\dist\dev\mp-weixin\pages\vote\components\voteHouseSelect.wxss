/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fui-filter__bar.data-v-c5509dc0 {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-bottom: 40rpx;
}
.fui-filter__item.data-v-c5509dc0 {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}
.fui-filter__icon.data-v-c5509dc0 {
  transition: all 0.15s linear;
}
.fui-icon__ani.data-v-c5509dc0 {
  transform: rotate(180deg);
}
.fui-list__cell.data-v-c5509dc0 {
  width: 100%;
}
.fui-select.data-v-c5509dc0 {
  flex: 1;
  height: 80rpx;
  padding: 32rpx;
  box-sizing: border-box;
  position: relative;
}
.fui-select.data-v-c5509dc0::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #eee;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
}
.fui-input.data-v-c5509dc0 {
  font-size: 32rpx;
  flex: 1;
  padding-right: 8rpx;
  color: #181818;
  pointer-events: none;
}