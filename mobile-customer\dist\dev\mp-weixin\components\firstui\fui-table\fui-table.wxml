<view class="fui-table__wrap data-v-312d7e5c" style="{{'height:' + H + ';' + ('width:' + I)}}"><scroll-view enhanced bounces="{{t}}" show-scrollbar="{{v}}" scroll-x="{{true}}" scroll-y="{{w}}" style="{{'width:' + x + ';' + ('height:' + y)}}" class="{{['fui-table__scroll-view', 'data-v-312d7e5c', z && 'fui-table__flex-row']}}" bindscrolltolower="{{A}}"><view class="fui-table--inner data-v-312d7e5c" style="{{'width:' + s}}"><view wx:if="{{a}}" class="{{['fui-table--tr', 'data-v-312d7e5c', g && 'fui-table__border-bottom', h && 'fui-table__border-top', i && 'fui-table--header-fixed']}}" style="{{'border-bottom-color:' + j + ';' + ('border-top-color:' + k)}}"><view wx:for="{{b}}" wx:for-item="item" wx:key="F" class="{{['fui-table--td', 'data-v-312d7e5c', c && 'fui-table__border-right', item.w && 'fui-table__border-left', item.x && 'fui-table__center', item.y && 'fui-table__right', item.z && 'fui-table--col-fixed']}}" style="{{'border-right-color:' + d + ';' + ('border-left-color:' + item.A) + ';' + ('background:' + item.B) + ';' + ('width:' + item.C) + ';' + ('padding-top:' + e) + ';' + ('padding-bottom:' + f) + ';' + ('left:' + item.D) + ';' + ('right:' + item.E)}}" catchtap="{{item.G}}"><view wx:if="{{item.a}}" class="{{['fui-table__checkbox', 'data-v-312d7e5c', item.d && 'fui-table__checkbox-color']}}" style="{{'background:' + item.e + ';' + ('border-color:' + item.f)}}" catchtap="{{item.g}}"><view class="fui-table__checkmark data-v-312d7e5c" style="{{'border-bottom-color:' + item.b + ';' + ('border-right-color:' + item.c)}}"></view></view><text wx:else class="{{['fui-table--td-text', 'data-v-312d7e5c', item.i && 'fui-text__center', item.j && 'fui-text__right', item.k && 'fui-td__ellipsis']}}" style="{{'width:' + item.l + ';' + ('color:' + item.m) + ';' + ('font-size:' + item.n) + ';' + ('font-weight:' + item.o)}}">{{item.h}}</text><view wx:if="{{item.p}}" class="fui-table__sort-icon data-v-312d7e5c" style="{{'right:' + item.s}}"><fui-icon wx:if="{{item.r}}" class="data-v-312d7e5c" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"></fui-icon></view><view wx:if="{{item.t}}" class="fui-table__td-sk data-v-312d7e5c" style="{{'background-color:' + item.v}}"></view></view></view><view wx:for="{{l}}" wx:for-item="item" wx:key="e" class="{{['fui-table--tr', 'data-v-312d7e5c', q && 'fui-table__border-bottom', item.c && 'fui-table__border-top']}}" style="{{'border-bottom-color:' + r + ';' + ('border-top-color:' + item.d)}}"><view wx:for="{{item.a}}" wx:for-item="model" wx:key="E" class="{{['fui-table--td', 'data-v-312d7e5c', m && 'fui-table__border-right', model.z && 'fui-table__border-left', model.A && 'fui-table__center', model.B && 'fui-table__right', model.C && 'fui-table__td-wrap', model.D && 'fui-table--col-fixed']}}" style="{{'border-right-color:' + n + ';' + ('border-left-color:' + model.F) + ';' + ('background:' + item.b) + ';' + ('width:' + model.G) + ';' + ('padding-top:' + o) + ';' + ('padding-bottom:' + p) + ';' + ('left:' + model.H) + ';' + ('right:' + model.I)}}" catchtap="{{model.J}}"><block wx:if="{{model.a}}"><view wx:if="{{model.b}}" class="{{['fui-table__checkbox', 'data-v-312d7e5c', model.e && 'fui-table__checkbox-color', model.f && 'fui-table__disabled']}}" style="{{'background:' + model.g + ';' + ('border-color:' + model.h)}}" catchtap="{{model.i}}"><view class="fui-table__checkmark data-v-312d7e5c" style="{{'border-bottom-color:' + model.c + ';' + ('border-right-color:' + model.d)}}"></view></view><image wx:elif="{{model.j}}" class="fui-table--td-img data-v-312d7e5c" src="{{model.k}}" mode="widthFix" style="{{'width:' + model.l + ';' + ('height:' + model.m)}}"></image><text wx:else class="{{['fui-table--td-text', 'data-v-312d7e5c', model.o && 'fui-text__center', model.p && 'fui-text__right', model.q && 'fui-td__ellipsis', model.r && 'fui-td__wrap']}}" style="{{'color:' + model.s + ';' + ('font-size:' + model.t) + ';' + ('width:' + model.v)}}">{{model.n}}</text></block><block wx:else><text wx:for="{{model.w}}" wx:for-item="btn" wx:key="f" class="{{['fui-table--btn', 'data-v-312d7e5c', btn.b && 'fui-td__btn-ml']}}" style="{{'font-size:' + btn.c + ';' + ('color:' + btn.d) + ';' + ('font-weight:' + btn.e)}}" catchtap="{{btn.g}}">{{btn.a}}</text></block><view wx:if="{{model.x}}" class="fui-table__td-sk data-v-312d7e5c" style="{{'background-color:' + model.y}}"></view></view></view></view></scroll-view><view wx:if="{{B}}" style="{{'width:' + F}}" class="{{['fui-table--empty', 'data-v-312d7e5c', G && 'fui-table__empty-ab']}}"><text class="fui-table__empty-text data-v-312d7e5c" style="{{'font-size:' + D + ';' + ('color:' + E)}}">{{C}}</text></view></view>