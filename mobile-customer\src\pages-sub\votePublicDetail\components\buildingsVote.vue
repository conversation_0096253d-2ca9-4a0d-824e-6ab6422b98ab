<template>
  <view class="">
    <view class="bg-[#ffffff] p-30rpx">
      <view class="text-30rpx font-bold text-[#333]">
        本次投票范围：{{ allValidVoteBuildingsStr }}
      </view>
      <view class="text-30rpx font-bold mt10rpx text-[#D9001B]">
        本次投票有效范围：{{ validVoteBuildingsStr }}
      </view>
      <view
        class="text-[24rpx] rounded-10rpx bg-[#FDF4F4] p-20rpx mt20rpx line-height-[1.6] text-[#FF2B2B]"
        v-if="invalidVoteBuildingsStr"
      >
        因{{ invalidVoteBuildingsStr }}未达到法定的2/3的投票率，因此{{
          invalidVoteBuildingsStr
        }}本次投票无效，{{
          invalidVoteBuildingsStr
        }}所投的票不计入本小区本次更换电梯的活动的投票结果。平台将尽快为{{
          invalidVoteBuildingsStr
        }}重新开启新的投票，请及时参与。
      </view>
      <view class="flex flex-col gap-20rpx text-[24rpx] mt20rpx">
        <view v-for="item in voteBuildingsList" :key="item.id">
          <view class="text-[28rpx] font-bold text-center">
            <view>{{ item.buildingNo }}幢（{{ item.valid ? '有效票' : '无效票' }}）</view>
          </view>
          <view class="my10rpx">
            <progress
              :percent="item.voteRateValue"
              :stroke-width="12"
              :border-radius="15"
              activeColor="#05D6A2"
            />
          </view>
          <view class="flex justify-between">
            <view>本楼栋投票数：{{ item.voteCount }}</view>
            <view>投票率：{{ item.voteRateValue }}%</view>
          </view>

          <view class="my10rpx">
            <progress
              :percent="item.voteRateValue"
              :stroke-width="12"
              :border-radius="15"
              activeColor="#FFAE00"
            />
          </view>
          <view class="flex justify-between">
            <view>本楼栋投票面积：{{ item.voteArea }}m²</view>
            <view>投票率：{{ item.voteAreaRateValue }}%</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  voteBuildingsList: {
    type: Array,
    default: () => [],
  },
})
// 所有楼栋投票 根据valid
const allValidVoteBuildingsStr = computed(() => {
  return props.voteBuildingsList?.map((item) => `${item.buildingNo}幢`)?.join(';')
})
// 所有有效的楼栋投票 根据valid
const validVoteBuildingsStr = computed(() => {
  return props.voteBuildingsList
    ?.filter((item) => item.valid)
    ?.map((item) => `${item.buildingNo}幢`)
    ?.join(';')
})

// 所有无效的楼栋投票 根据valid
const invalidVoteBuildingsStr = computed(() => {
  return (
    props.voteBuildingsList
      ?.filter((item) => !item.valid)
      ?.map((item) => `${item.buildingNo}幢`)
      ?.join(';') || ''
  )
})
</script>

<style lang="scss" scoped>
//
</style>
