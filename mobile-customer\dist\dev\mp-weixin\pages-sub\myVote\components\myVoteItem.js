"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
if (!Array) {
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  _easycom_fui_tag2();
}
const _easycom_fui_tag = () => "../../../components/firstui/fui-tag/fui-tag.js";
if (!Math) {
  _easycom_fui_tag();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myVoteItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const props = __props;
    const handleClick = () => {
      if (props.item.status === 1) {
        common_vendor.index.navigateTo({
          url: `/pages-sub/voteResultDetail/index?id=${props.item.id}&buildingId=${props.item.buildingId}`
        });
      }
      if (props.item.status >= 2) {
        common_vendor.index.navigateTo({
          url: `/pages-sub/votehasEndResultDetail/index?id=${props.item.id}`
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.t(__props.item.communityName),
        c: common_vendor.t(__props.item.buildingNo ? `${__props.item.buildingNo}幢` : ""),
        d: common_vendor.t(__props.item.totalCount),
        e: common_vendor.t(__props.item.voteCount),
        f: common_vendor.t(__props.item.voteEndDate),
        g: common_vendor.t(__props.item.voteTime),
        h: common_vendor.t(__props.item.voteUserName),
        i: __props.item.status > 1
      }, __props.item.status > 1 ? {
        j: common_vendor.p({
          type: "success",
          background: "#aaaaaa",
          padding: ["8rpx", "16rpx"],
          color: "#fff",
          text: "已结束"
        })
      } : {
        k: common_vendor.p({
          type: "success",
          text: "已投票",
          padding: ["8rpx", "16rpx"]
        })
      }, {
        l: common_vendor.o(handleClick)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f99fde67"]]);
wx.createComponent(Component);
