
.fui-text__wrap.data-v-5cc640f9 {
		align-items: center;
		flex-direction: row;
}
.fui-text__active.data-v-5cc640f9:active {
		opacity: .5;
}
.fui-text__inline.data-v-5cc640f9 {
		display: inline-flex;
}
.fui-text__block.data-v-5cc640f9 {
		display: flex;
}
.fui-text__unshrink.data-v-5cc640f9 {
		flex-shrink: 0;
}
.fui-text__content.data-v-5cc640f9 {
		word-break: break-all;
}
.fui-text__center.data-v-5cc640f9 {
		justify-content: center;
}
.fui-text__right.data-v-5cc640f9 {
		justify-content: flex-end;
}
.fui-text__primary.data-v-5cc640f9 {
		color: var(--fui-color-primary, #465CFF) !important;
}
.fui-text__success.data-v-5cc640f9 {
		color: var(--fui-color-success, #09BE4F) !important;
}
.fui-text__warning.data-v-5cc640f9 {
		color: var(--fui-color-warning, #FFB703) !important;
}
.fui-text__danger.data-v-5cc640f9 {
		color: var(--fui-color-danger, #FF2B2B) !important;
}
.fui-text__purple.data-v-5cc640f9 {
		color: var(--fui-color-purple, #6831FF) !important;
}
.fui-text__gray.data-v-5cc640f9 {
		color: var(--fui-color-label, #B2B2B2) !important;
}
.fui-text__black.data-v-5cc640f9 {
		color: var(--fui-color-title, #181818) !important;
}


