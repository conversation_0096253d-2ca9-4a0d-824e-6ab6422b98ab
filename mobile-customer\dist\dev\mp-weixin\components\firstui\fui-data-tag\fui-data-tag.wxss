

	/* 头条小程序组件内如果不能引入字体，则需要在父级页面引入字体文件*/
@font-face {
		font-family: fuitag;
		src: url(data:font/ttf;base64,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) format("truetype");
}
.fui-dt__icon.data-v-660d5831 {
		font-family: fuitag;
		text-decoration: none;
		text-align: center;
		position: absolute;
		right: -4rpx;
		bottom: -6rpx;
}
.fui-data__tag-wrap.data-v-660d5831 {

		max-width: 100%;
		display: flex;

		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
}
.fui-data__tag-flexwrap.data-v-660d5831 {
		flex-wrap: wrap;
}
.fui-data__tag-nowrap.data-v-660d5831 {
		flex-wrap: nowrap;
}
.fui-data__tag-item.data-v-660d5831 {

		display: inline-flex;
		white-space: nowrap;
		box-sizing: border-box;
		transform: translateZ(0);

		border-width: 1px;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		text-overflow: ellipsis;



		border-style: solid;



		position: relative;
}
.fui-data__tag-inner.data-v-660d5831 {
		position: absolute;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
		overflow: hidden;

		z-index: 1;
}
.fui-data__tag-text.data-v-660d5831 {

		display: block;
		white-space: nowrap;
		overflow: hidden;




		flex: 1;
		text-align: center;
		text-overflow: ellipsis;
		font-weight: normal;
}
.fui-data__tag-disable.data-v-660d5831 {



		opacity: .5;
}
