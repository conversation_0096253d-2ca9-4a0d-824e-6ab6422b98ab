"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_util = require("../../utils/util.js");
require("../../utils/http.js");
const service_app_vote = require("../../service/app/vote.js");
const utils_const = require("../../utils/const.js");
const pagesSub_vote_vota_data = require("./vota.data.js");
if (!Array) {
  const _easycom_fui_count_down2 = common_vendor.resolveComponent("fui-count-down");
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_landscape2 = common_vendor.resolveComponent("fui-landscape");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_count_down2 + _easycom_fui_button2 + _easycom_fui_loading2 + _easycom_fui_landscape2 + _component_layout_default_uni)();
}
const _easycom_fui_count_down = () => "../../components/firstui/fui-count-down/fui-count-down.js";
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_landscape = () => "../../components/firstui/fui-landscape/fui-landscape.js";
if (!Math) {
  (_easycom_fui_count_down + TitleHeader + VoteBrandCheckItem + VoteNotice + _easycom_fui_button + _easycom_fui_loading + _easycom_fui_landscape)();
}
const TitleHeader = () => "../../components/common/titleHeader.js";
const VoteNotice = () => "./components/voteNotice.js";
const VoteBrandCheckItem = () => "./components/voteBrandCheckItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const brands = common_vendor.ref([]);
    const isListenSort = common_vendor.ref(0);
    const showModal = common_vendor.ref(false);
    const selectedCount = common_vendor.computed(() => {
      return brands.value.filter((item) => item.isChecked).length || 1;
    });
    const handleVoteBrandClick = (brand) => __async(this, null, function* () {
      if (brand.stockStatus === pagesSub_vote_vota_data.StockStatusEnum.SELL_OUT) {
        return;
      }
      isListenSort.value = 0;
      brand.isChecked = !brand.isChecked;
      brands.value.findIndex((item) => item.name === brand.name);
      brands.value.filter((item) => item.isChecked);
      yield common_vendor.nextTick$1();
      console.log("brands", brands.value);
      if (brand.isChecked) {
        brand.sortOrder = selectedCount.value;
      } else {
        const oldSortOrder = brand.sortOrder;
        brand.sortOrder = 0;
        brands.value.forEach((item) => {
          if (item.isChecked && item.sortOrder > oldSortOrder) {
            item.sortOrder--;
          }
        });
      }
      yield common_vendor.nextTick$1();
      isListenSort.value = 1;
      yield common_vendor.nextTick$1();
    });
    const allChecked = common_vendor.computed(() => {
      return brands.value.filter((item) => item.isChecked);
    });
    const allCheckedSkus = common_vendor.computed(() => {
      console.log("allChecked", allChecked.value);
      return allChecked.value.map((item) => {
        var _a;
        return ((_a = item == null ? void 0 : item.skus) == null ? void 0 : _a.find((sku) => sku.checked)) || {};
      });
    });
    const maxSkuPrice = common_vendor.computed(() => {
      if (allCheckedSkus.value.length <= 0) {
        return 0;
      }
      return Math.max(...allCheckedSkus.value.map((item) => item.price || 0));
    });
    const minSkuPrice = common_vendor.computed(() => {
      if (allCheckedSkus.value.length <= 0) {
        return 0;
      }
      return Math.min(...allCheckedSkus.value.map((item) => item.price || 0));
    });
    const voteMethod = common_vendor.ref(0);
    const closeModal = () => {
      showModal.value = false;
    };
    const handleConfirmVote = () => {
      showModal.value = false;
      confirmVote();
    };
    const confirmVote = () => __async(this, null, function* () {
      const _voteParams = {
        voteId: id.value,
        buildingId: buildingId.value,
        method: voteMethod.value,
        // 投票品牌
        // products: allChecked.value.map((item) => {
        //   // 找出所有选中品牌的sku
        //   const checkedSkus = item.skus.find((sku) => sku.checked)
        //   console.log('checkedSkus', checkedSkus)
        //   console.log('item', item)
        //   return {
        //     productId: item.productId,
        //     productSort: item.sortOrder,
        //     skuId: checkedSkus.id,
        //     skuName: checkedSkus?.name,
        //   }
        // }),
        supplierList: allChecked.value.map((item) => {
          var _a;
          const checkedSkus = (_a = item == null ? void 0 : item.skus) == null ? void 0 : _a.find((sku) => sku.checked);
          console.log("checkedSkus", checkedSkus);
          console.log("item", item);
          return {
            supplierId: item.supplierId,
            sort: item.sortOrder,
            skuCode: checkedSkus == null ? void 0 : checkedSkus.code,
            productId: item.productId,
            productName: item.productName
          };
        })
      };
      console.log("_voteParams", _voteParams);
      console.log("allChecked.value", allChecked.value);
      try {
        loading.value = true;
        const res = yield service_app_vote.voteByUser({
          body: _voteParams
        });
        console.log("res", res);
        utils_util.showTextToast(res.message || "投票成功");
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/vote/index"
          });
        }, 1e3);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const handleMultiVote = () => {
      console.log("handleMultiVote");
      voteMethod.value = 1;
      showModal.value = true;
    };
    const voteTipInfo = common_vendor.ref({});
    const handleImmediateVote = () => __async(this, null, function* () {
      var _a;
      voteMethod.value = 0;
      if (allChecked.value.length < 3) {
        common_vendor.index.showToast({
          title: "请至少选择3个品牌",
          icon: "none"
        });
        return;
      }
      console.log("res", allChecked.value);
      const insufficientBrands = allChecked.value.filter((item) => item.stockStatus === pagesSub_vote_vota_data.StockStatusEnum.INSUFFICIENT) || [];
      voteTipInfo.value.brandNames = insufficientBrands.map((item) => item.brandName).join(",");
      console.log("voteTipInfo", voteTipInfo.value);
      if ((_a = voteTipInfo.value) == null ? void 0 : _a.brandNames) {
        showModal.value = true;
      } else {
        confirmVote();
      }
    });
    const id = common_vendor.ref("");
    const voteEndDate = common_vendor.ref("");
    const buildingId = common_vendor.ref("");
    const loading = common_vendor.ref(false);
    const voteEndSeconds = common_vendor.ref(0);
    const fetchVoteElevatorList = () => __async(this, null, function* () {
      var _a;
      try {
        loading.value = true;
        const res = yield service_app_vote.queryVoteElevatorList({
          params: {
            voteId: id.value,
            buildingId: buildingId.value
          }
        });
        brands.value = ((_a = res.result) == null ? void 0 : _a.map((item) => {
          var _a2, _b, _c;
          return __spreadProps(__spreadValues({}, item), {
            voteCount: (_a2 = item.voteCount) != null ? _a2 : 0,
            voteRate: (_b = item.voteRate) != null ? _b : "0%",
            communityId: voteData.value.communityId,
            sortOrder: Number(item.sort),
            isChecked: false,
            skus: ((_c = item == null ? void 0 : item.bizSkus) == null ? void 0 : _c.map((sku, index) => {
              return __spreadProps(__spreadValues({}, sku), {
                checked: index === 0,
                skuPrice: sku.price,
                price: utils_util.calculatePrePrice(sku.price, formulaParams.value)
              });
            })) || []
          });
        })) || [];
        console.log("queryVoteElevatorList", res);
        console.log("brands.value", brands.value);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const changeVoteSort = (brand, currentSortOrder) => {
      console.log("changeVoteSort2222", brand, currentSortOrder);
      const targetItem = brands.value.find((item) => item.id === brand.id);
      if (!targetItem)
        return;
      const oldSort = targetItem.sortOrder;
      if (oldSort === currentSortOrder)
        return;
      if (oldSort < currentSortOrder) {
        brands.value.forEach((item) => {
          if (item.id !== targetItem.id && item.sortOrder > oldSort && item.sortOrder <= currentSortOrder) {
            console.log("item.sortOrder--", item.sortOrder);
            item.sortOrder--;
          }
        });
      } else {
        brands.value.forEach((item) => {
          if (item.id !== targetItem.id && item.sortOrder >= currentSortOrder && item.sortOrder < oldSort) {
            item.sortOrder++;
          }
        });
      }
      targetItem.sortOrder = currentSortOrder;
    };
    const formulaParams = common_vendor.ref({ reduction: 0, rate: 1 });
    const fetchPrePrice = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.getPrePrice({
          params: {
            communityId: voteData.value.communityId,
            buildingIds: buildingId.value
          }
        });
        formulaParams.value = res.result;
        console.log("res", res);
      } catch (error) {
        console.log("error", error);
      } finally {
        loading.value = false;
      }
    });
    const voteData = common_vendor.ref({});
    const fetchVoteInfo = () => __async(this, null, function* () {
      try {
        loading.value = true;
        const res = yield service_app_vote.queryBizVoteInfoById({
          params: {
            id: id.value,
            buildingId: buildingId.value
          }
        });
        voteData.value = res.result || {};
        voteEndDate.value = res.result.voteEndDate;
        voteEndSeconds.value = utils_util.calculateVoteEndSeconds(voteEndDate.value);
      } catch (error) {
        console.log("error", error);
        voteData.value = null;
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((options) => __async(this, null, function* () {
      console.log("options", options);
      id.value = options.id;
      buildingId.value = options.buildingId;
      yield fetchVoteInfo();
      yield fetchPrePrice();
      fetchVoteElevatorList();
      common_vendor.index.$on(utils_const.EventName.VOTE_BRAND_SELECTED, (e) => {
        console.log("VOTE_BRAND_SELECTED", e);
        const { skuId, productId, code, skuName } = e;
        const brand = brands.value.find((item) => item.productId === productId);
        console.log("brand-------", brand);
        if (brand) {
          if (!brand.isChecked) {
            handleVoteBrandClick(brand);
          }
          brand.skus.forEach((sku) => {
            sku.checked = sku.code === code || sku.name === skuName;
          });
        }
      });
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isDays: true,
          isColon: false,
          size: "42",
          width: "50",
          height: "50",
          borderColor: "transparent",
          background: "transparent",
          color: "#005ED1",
          colonColor: "#005ED1",
          value: common_vendor.unref(voteEndSeconds)
        }),
        b: common_vendor.p({
          title: "请选择至少3个品牌进行投票"
        }),
        c: common_vendor.f(common_vendor.unref(brands), (brand, index, i0) => {
          return {
            a: common_vendor.o(changeVoteSort, index),
            b: "b41d434a-3-" + i0 + ",b41d434a-0",
            c: common_vendor.p({
              brand,
              index,
              selectedCount: common_vendor.unref(selectedCount),
              isListenSort: common_vendor.unref(isListenSort),
              buildingIds: common_vendor.unref(buildingId),
              voteId: common_vendor.unref(id)
            }),
            d: common_vendor.o(($event) => handleVoteBrandClick(brand), index),
            e: index
          };
        }),
        d: common_vendor.t(common_vendor.unref(minSkuPrice)),
        e: common_vendor.t(common_vendor.unref(maxSkuPrice)),
        f: common_vendor.o(handleMultiVote),
        g: common_vendor.p({
          background: "linear-gradient(-90deg, #EC6E3E 0%, #F69954 100%)",
          width: "184rpx",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "从多投票"
        }),
        h: common_vendor.o(handleImmediateVote),
        i: common_vendor.p({
          type: "primary",
          width: "184rpx",
          height: "84rpx",
          size: 32,
          radius: "10rpx",
          text: "立即投票"
        }),
        j: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {
        k: common_vendor.p({
          isMask: true
        })
      } : {}, {
        l: common_assets._imports_0$4,
        m: common_vendor.unref(voteMethod) === 0
      }, common_vendor.unref(voteMethod) === 0 ? {
        n: common_vendor.t(common_vendor.unref(voteTipInfo).brandNames)
      } : {}, {
        o: common_vendor.t(common_vendor.unref(voteMethod) === 0 ? "继续投票" : "继续从多"),
        p: common_vendor.o(handleConfirmVote),
        q: common_vendor.p({
          radius: "100rpx",
          width: "100%",
          ["disabled-background"]: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          background: "linear-gradient(-90deg, #308BFF 0%, #76BAFF 100%)",
          borderColor: "rgba(0,0,0,0)",
          ["border-width"]: "0"
        }),
        r: common_vendor.o(closeModal),
        s: common_vendor.p({
          radius: "100rpx",
          background: "#fff",
          color: "#000000",
          borderColor: "#ffffff",
          text: "重新选择"
        }),
        t: common_vendor.o(closeModal),
        v: common_vendor.p({
          show: common_vendor.unref(showModal),
          closable: false
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b41d434a"]]);
wx.createPage(MiniProgramPage);
