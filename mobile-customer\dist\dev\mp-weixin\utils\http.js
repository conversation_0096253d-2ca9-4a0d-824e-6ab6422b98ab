"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const utils_util = require("./util.js");
require("../store/index.js");
const store_user = require("../store/user.js");
const http = (options) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.request(__spreadProps(__spreadValues({}, options), {
      dataType: "json",
      // 响应成功
      success(res) {
        return __async(this, null, function* () {
          console.log("options", options);
          console.log("res", res);
          if (res.statusCode === 200) {
            if (res.data.code === 200) {
              console.log("res.data", res.data);
              resolve(res.data);
            } else {
              if (!options.hideErrorToast) {
                yield utils_util.showTextToast(res.data.message || "请求错误");
              }
              reject(res.data);
            }
          } else if (res.statusCode === 401 || res.statusCode === 424) {
            const userStore = store_user.useUserStore();
            userStore.clearUserInfo();
            reject(res);
          } else {
            if (!options.hideErrorToast) {
              yield utils_util.showTextToast(res.data.message || "请求错误");
            }
            reject(res);
          }
        });
      },
      // 响应失败
      fail(err) {
        common_vendor.index.showToast({
          icon: "none",
          title: "网络错误，换个网络试试"
        });
        reject(err);
      }
    }));
  });
};
const httpGet = (url, query) => {
  return http({
    url,
    query,
    method: "GET"
  });
};
const httpPost = (url, data, query) => {
  return http({
    url,
    query,
    data,
    method: "POST"
  });
};
http.get = httpGet;
http.post = httpPost;
const request = (url, options) => {
  const requestOptions = __spreadValues({
    url
  }, options);
  if (options.params) {
    requestOptions.query = requestOptions.params;
    delete requestOptions.params;
  }
  if (options.headers) {
    requestOptions.header = options.headers;
    delete requestOptions.headers;
  }
  return http(requestOptions);
};
exports.request = request;
