"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
if (!Math) {
  (TitleHeader + VoteNoticeItem)();
}
const TitleHeader = () => "../../../components/common/titleHeader.js";
const VoteNoticeItem = () => "../../voteNoticeDetail/components/voteNoticeItem.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteResult",
  props: {
    voteData: {
      type: Object,
      default: () => {
      }
    },
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  setup(__props) {
    const props = __props;
    const brandInfo = common_vendor.computed(() => {
      return {
        vote_rate: props.voteData.voteRateProduct,
        vote_count: props.voteData.voteCountProduct,
        brand_name: props.voteData.resultBrandName,
        brandLogo: props.voteData.resultBrandPic,
        skuName: props.voteData.skuName,
        product_name: props.voteData.resultProductName,
        pic_url: props.voteData.resultBrandPic,
        skuRate: props.voteData.skuRate,
        skuCode_dictText: props.voteData.skuCode_dictText
      };
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.voteData.status
      }, __props.voteData.status ? common_vendor.e({
        b: __props.showTitle
      }, __props.showTitle ? {
        c: common_vendor.p({
          title: "投票结果"
        })
      } : {}, {
        d: __props.voteData.status == 4
      }, __props.voteData.status == 4 ? {
        e: common_assets._imports_0$9
      } : {
        f: common_assets._imports_1$4,
        g: common_vendor.p({
          isShowIndex: false,
          showBottomBorder: false,
          brand: common_vendor.unref(brandInfo),
          showSkuRate: true
        }),
        h: common_vendor.t(__props.voteData.price),
        i: common_vendor.t(__props.voteData.subsidyPrice),
        j: common_vendor.t(__props.voteData.finalPrice)
      }) : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-295555fa"]]);
wx.createComponent(Component);
