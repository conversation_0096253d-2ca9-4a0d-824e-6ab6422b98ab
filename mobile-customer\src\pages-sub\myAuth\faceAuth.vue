<route lang="json5">
{
  style: {
    navigationBarTitleText: '授权认证',
  },
}
</route>

<template>
  <view class="">
    <web-view
      :src="authUrl"
      @onMessage="handleMessage"
      @onPostMessage="handlePostMessage"
    ></web-view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'

const rejectPageUrl = import.meta.env.VITE_REJECT_PAGE_URL

const authUrl = ref('')
onLoad((options: { url: string }) => {
  console.log('onLoad', options)
  const itemUrl = decodeURIComponent(options.url)
  authUrl.value = itemUrl

  // todo 5秒后跳转至企业详情页面（模拟认证成功）
  //   setTimeout(() => {
  //     uni.redirectTo({
  //       url: '/pages-sub/mine/auth/businessAuthForm',
  //     })
  //   }, 5000)
})
const handleMessage = (value: any) => {
  console.log('handleMessage', value)
}

const handlePostMessage = (value: any) => {
  console.log('handlePostMessge', value)
}
</script>

<style scoped lang="scss"></style>
