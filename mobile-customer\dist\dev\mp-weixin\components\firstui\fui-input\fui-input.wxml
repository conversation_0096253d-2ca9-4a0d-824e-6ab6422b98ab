<view class="{{['fui-input__wrap', 'data-v-7ba0827d', ad && 'fui-input__border-nvue']}}" style="{{'padding-top:' + ae + ';' + ('padding-right:' + af) + ';' + ('padding-bottom:' + ag) + ';' + ('padding-left:' + ah) + ';' + ('background:' + ai) + ';' + ('margin-top:' + aj) + ';' + ('border-radius:' + ak) + ';' + ('border-color:' + al)}}" bindtap="{{am}}"><view wx:if="{{a}}" style="{{'background:' + b + ';' + ('left:' + c) + ';' + ('right:' + d)}}" class="{{['fui-input__border-top', 'data-v-7ba0827d', e && 'fui-input__background']}}"></view><view wx:if="{{f}}" class="{{['fui-input__border', 'data-v-7ba0827d', g && 'fui-input__bordercolor']}}" style="{{'border-radius:' + h + ';' + ('border-color:' + i)}}"></view><view wx:if="{{j}}" class="fui-input__required data-v-7ba0827d" style="{{'color:' + k}}">*</view><view wx:if="{{l}}" class="fui-input__label data-v-7ba0827d" style="{{'min-width:' + p}}"><text class="data-v-7ba0827d" style="{{'font-size:' + n + ';' + ('color:' + o)}}">{{m}}</text></view><slot name="left"></slot><block wx:if="{{r0}}"><input class="{{['fui-input__self', 'data-v-7ba0827d', q && 'fui-input__text-right', r && 'fui-input__disabled-styl', s && 'fui-input__disabled']}}" style="{{'font-size:' + t + ';' + ('color:' + v) + ';' + ('text-align:' + w)}}" placeholder-class="fui-input__placeholder" type="{{x}}" name="{{y}}" value="{{z}}" placeholder="{{A}}" password="{{B}}" placeholder-style="{{C}}" disabled="{{D}}" cursor-spacing="{{E}}" maxlength="{{F}}" focus="{{G}}" confirm-type="{{H}}" confirm-hold="{{I}}" cursor="{{J}}" selection-start="{{K}}" selection-end="{{L}}" adjust-position="{{M}}" hold-keyboard="{{N}}" auto-blur="{{O}}" enableNative="{{false}}" always-embed="{{P}}" bindfocus="{{Q}}" bindblur="{{R}}" bindinput="{{S}}" bindconfirm="{{T}}" bindkeyboardheightchange="{{U}}"/></block><view wx:if="{{V}}" class="fui-input__clear-wrap data-v-7ba0827d" style="{{'background:' + W}}" catchtap="{{X}}"><view class="fui-input__clear data-v-7ba0827d"><view class="fui-input__clear-a data-v-7ba0827d"></view></view><view class="fui-input__clear data-v-7ba0827d"><view class="fui-input__clear-b data-v-7ba0827d"></view></view></view><slot></slot><view wx:if="{{Y}}" style="{{'background:' + Z + ';' + ('left:' + aa) + ';' + ('right:' + ab)}}" class="{{['fui-input__border-bottom', 'data-v-7ba0827d', ac && 'fui-input__background']}}"></view></view>