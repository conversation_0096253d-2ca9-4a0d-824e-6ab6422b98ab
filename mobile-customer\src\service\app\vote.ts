/* eslint-disable */
// @ts-ignore
import { request } from '@/utils/http';
import { CustomRequestOptions } from '@/interceptors/request';

import * as API from './types';


/**投票项目信息-分页列表查询 */
export async function queryBizVoteInfoList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**获取公告详情 */
export async function queryBizVoteInfoById({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { id: string, status?: number, buildingId?: string };

  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/queryDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**小区top10 品牌 */
export async function queryTop10Brand({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { communityId?: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/brandVotedTop10', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/**南京top10 品牌 */
export async function queryBrandTop10({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { communityId?: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/brandTop10', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**投票小区 */
export async function queryCommunityList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/listByCurUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/**南京市老旧电梯更新国补进程 */
export async function queryGovProcess({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/getSubsidyProgress', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/**投票电梯列表 */
export async function queryVoteElevatorList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/voteSupplierRelation/listByVoteId', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}



/** 投票 */
export async function voteByUser({
  body,
  options,
}: {
  body: {
    voteId: string,
    supplierList: any[],
    buildingId: string,
  };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/voteByUser', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}


/**获取当前用户投票信息 */
export async function getVoteInfoByCurUser({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: {
    voteId: string,
    buildingId?: string,
  };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/getByCurUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**获取当前用户房屋信息 */
export async function getMyHouse({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/user/userHouse/getByCurUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/**获取我的投票 */
export async function getMyVote({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/queryPageListByCurUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**用户是否有投票权限 */
export async function getCanVote({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { voteId: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/canVote', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**商品详情 */
export async function getProductDetail({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: {
    id: string;
  };
  options?: CustomRequestOptions;
}) {
  const { id } = params  
  return request<API.R>(`/supplier/product/detail/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}
/** 投票结果品牌排名 */
export async function getVoteRankProductTop10({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { voteId: string,unVoted?: number };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/brandVotedTop10', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**投票注意事项操作记录 */
// 0:拒绝 1:同意 
export async function addVoteNoticeOperation({
  body,
  options,
}: {
  body: {
    operationType?: number,
    voteId: string,
    buildingId: string,
  };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/record/recVoteNoticeOperation/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**判断有没有点击过投票 */
// 0:拒绝 1:同意 
export async function getHasClickVoteNote({
  params,
  options,
}: {
  params: {
    voteId: string,
    buildingId: string,
  };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/record/recVoteNoticeOperation/queryByVoteId', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取地址列表 */
export async function getAddressList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { communityId: string, buildingIds: string[] };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/user/userHouse/getByCurUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
/** 获取地址列表 */
export async function getAllGrantedAndSelfList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { communityId: string, buildingIds: string[] };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/user/userHouse/getAllGrantedAndSelfList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询栋投票数及投票率 */
export async function getBuildingRateByVote({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { voteId: string, buildingIds?: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/queryBuildingRateByVote', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**获取电梯预估价格 */
export async function getPrePrice({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { minPrice?: number, maxPrice?: number, communityId: string, buildingIds: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/getPrePrice', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**投票时是否提示 */
export async function queryExceedBrandList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { brandNames?: string };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizVoteInfo/queryExceedBrandList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**国补每台价格 */
export async function querySubsidyPrice({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params?: any,
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/statesubsidies/bizStateSubsidies/getConfig', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**商品得票数 */
export async function queryProductVoteCnt({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: { communityId: string, productId: string },
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/vote/bizUserVote/queryProductVoteCnt', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
/**纠错 */
export async function addErrorCorrectionHouse({
  body,
  options,
}: {
  body: {
    remark: string,
    imgUrl?: string,
    userHouseId?: string,
  };
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/error/bizErrorHouse/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}


/**其他消息列表（分页） */
export async function queryBizErrorHouseList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/error/bizErrorHouse/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**其他消息详情 */
export async function queryBizErrorHouseDetail({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: {id: string};
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/error/bizErrorHouse/queryById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/** 我的合同列表 */
export async function queryContractList({
  params,
  options,
}: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: any;
  options?: CustomRequestOptions;
}) {
  return request<API.R>('/contract/bizContract/getListByUser', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}