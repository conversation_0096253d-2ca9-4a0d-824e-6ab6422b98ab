"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const utils_http = require("../../utils/http.js");
function queryBizVoteInfoList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/list", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryBizVoteInfoById(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/queryDetail", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryBrandTop10(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/brandTop10", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryCommunityList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/listByCurUser", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryGovProcess(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/getSubsidyProgress", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryVoteElevatorList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/voteSupplierRelation/listByVoteId", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function voteByUser(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/voteByUser", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function getVoteInfoByCurUser(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/getByCurUser", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getMyHouse(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/user/userHouse/getByCurUser", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getMyVote(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/queryPageListByCurUser", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getCanVote(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizVoteInfo/canVote", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getProductDetail(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    const { id } = params;
    return utils_http.request(`/supplier/product/detail/${id}`, __spreadValues({
      method: "GET"
    }, options || {}));
  });
}
function getVoteRankProductTop10(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/brandVotedTop10", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function addVoteNoticeOperation(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/record/recVoteNoticeOperation/add", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function getHasClickVoteNote(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/record/recVoteNoticeOperation/queryByVoteId", __spreadValues({
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      },
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getAllGrantedAndSelfList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/user/userHouse/getAllGrantedAndSelfList", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getBuildingRateByVote(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/queryBuildingRateByVote", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function getPrePrice(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/getPrePrice", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function querySubsidyPrice(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/statesubsidies/bizStateSubsidies/getConfig", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryProductVoteCnt(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/vote/bizUserVote/queryProductVoteCnt", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function addErrorCorrectionHouse(_0) {
  return __async(this, arguments, function* ({
    body,
    options
  }) {
    return utils_http.request("/error/bizErrorHouse/add", __spreadValues({
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: body
    }, options || {}));
  });
}
function queryBizErrorHouseList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/error/bizErrorHouse/list", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryBizErrorHouseDetail(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/error/bizErrorHouse/queryById", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
function queryContractList(_0) {
  return __async(this, arguments, function* ({
    params,
    options
  }) {
    return utils_http.request("/contract/bizContract/getListByUser", __spreadValues({
      method: "GET",
      params: __spreadValues({}, params)
    }, options || {}));
  });
}
exports.addErrorCorrectionHouse = addErrorCorrectionHouse;
exports.addVoteNoticeOperation = addVoteNoticeOperation;
exports.getAllGrantedAndSelfList = getAllGrantedAndSelfList;
exports.getBuildingRateByVote = getBuildingRateByVote;
exports.getCanVote = getCanVote;
exports.getHasClickVoteNote = getHasClickVoteNote;
exports.getMyHouse = getMyHouse;
exports.getMyVote = getMyVote;
exports.getPrePrice = getPrePrice;
exports.getProductDetail = getProductDetail;
exports.getVoteInfoByCurUser = getVoteInfoByCurUser;
exports.getVoteRankProductTop10 = getVoteRankProductTop10;
exports.queryBizErrorHouseDetail = queryBizErrorHouseDetail;
exports.queryBizErrorHouseList = queryBizErrorHouseList;
exports.queryBizVoteInfoById = queryBizVoteInfoById;
exports.queryBizVoteInfoList = queryBizVoteInfoList;
exports.queryBrandTop10 = queryBrandTop10;
exports.queryCommunityList = queryCommunityList;
exports.queryContractList = queryContractList;
exports.queryGovProcess = queryGovProcess;
exports.queryProductVoteCnt = queryProductVoteCnt;
exports.querySubsidyPrice = querySubsidyPrice;
exports.queryVoteElevatorList = queryVoteElevatorList;
exports.voteByUser = voteByUser;
