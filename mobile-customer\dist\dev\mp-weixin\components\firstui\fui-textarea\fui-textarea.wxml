<view class="{{['fui-textarea__wrap', 'data-v-75500ed7', af && 'fui-textarea__flex-start', ag && 'fui-textarea__border-nvue']}}" style="{{'padding-top:' + ah + ';' + ('padding-right:' + ai) + ';' + ('padding-bottom:' + aj) + ';' + ('padding-left:' + ak) + ';' + ('background:' + al) + ';' + ('border-radius:' + am) + ';' + ('border-color:' + an) + ';' + ('margin-top:' + ao)}}" bindtap="{{ap}}"><view wx:if="{{a}}" style="{{'background:' + b + ';' + ('left:' + c) + ';' + ('right:' + d)}}" class="{{['fui-textarea__border-top', 'data-v-75500ed7', e && 'fui-textarea__background']}}"></view><view wx:if="{{f}}" class="{{['fui-textarea__border', 'data-v-75500ed7', g && 'fui-textarea__bordercolor']}}" style="{{'border-radius:' + h + ';' + ('border-color:' + i)}}"></view><view wx:if="{{j}}" class="{{['fui-textarea__required', 'data-v-75500ed7', k && 'fui-required__flex-start']}}" style="{{'color:' + l + ';' + ('top:' + m)}}">*</view><view wx:if="{{n}}" class="fui-textarea__label data-v-75500ed7" style="{{'min-width:' + r}}"><text class="data-v-75500ed7" style="{{'font-size:' + p + ';' + ('color:' + q)}}">{{o}}</text></view><slot name="left"></slot><view class="fui-textarea__flex-1 data-v-75500ed7"><block wx:if="{{r0}}"><textarea ref="fuiTextarea" class="{{['fui-textarea__self', 'data-v-75500ed7', s && 'fui-text__right', t && 'fui-textarea__disabled-styl']}}" style="{{'height:' + v + ';' + ('min-height:' + w) + ';' + ('font-size:' + x) + ';' + ('color:' + y)}}" placeholder-class="fui-textarea-placeholder" name="{{z}}" value="{{A}}" placeholder="{{B}}" placeholderStyle="{{C}}" disabled="{{D}}" cursor-spacing="{{E}}" maxlength="{{F}}" focus="{{G}}" auto-height="{{H}}" fixed="{{I}}" show-confirm-bar="{{J}}" cursor="{{K}}" selection-start="{{L}}" selection-end="{{M}}" adjust-position="{{N}}" hold-keyboard="{{O}}" disable-default-padding="{{P}}" enableNative="{{false}}" show-count="{{false}}" bindfocus="{{Q}}" bindblur="{{R}}" bindinput="{{S}}" bindconfirm="{{T}}" bindlinechange="{{U}}" bindkeyboardheightchange="{{V}}"></textarea></block><view wx:if="{{W}}" class="fui-textarea__counter data-v-75500ed7"><text class="data-v-75500ed7" style="{{'font-size:' + Y + ';' + ('color:' + Z)}}">{{X}}</text></view></view><slot></slot><view wx:if="{{aa}}" style="{{'background:' + ab + ';' + ('left:' + ac) + ';' + ('right:' + ad)}}" class="{{['fui-textarea__border-bottom', 'data-v-75500ed7', ae && 'fui-textarea__background']}}"></view></view>