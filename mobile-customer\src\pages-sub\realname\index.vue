<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人实名认证',
  },
}
</route>

<template>
  <view class="">
    <view class="pt20rpx">
      <div class="flex flex-col items-center justify-between mb40rpx">
        <div class="flex items-center w100% h420rpx">
          <image src="../static/images/dt_011.jpg" mode="scaleToFill" class="w100% h100%" />
        </div>
      </div>
      <fui-form ref="formRef" :show="false">
        <fui-form-item label="姓名" asterisk prop="name" error-align="left">
          <fui-input
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请输入姓名"
            v-model="formData.name"
          ></fui-input>
        </fui-form-item>
        <!-- <fui-form-item label="手机区号" asterisk prop="phoneCode" error-align="left">
          <fui-input
            :maxlength="5"
            type="number"
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请输入手机区号"
            v-model="formData.phoneCode"
          ></fui-input>
        </fui-form-item> -->
        <!-- <fui-form-item label="手机号" asterisk prop="phone" error-align="left">
          <fui-input
            :maxlength="11"
            type="number"
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请输入手机号"
            v-model="formData.phone"
          ></fui-input>
        </fui-form-item> -->
        <!-- <fui-form-item
          label="证件类型"
          highlight
          arrow
          asterisk
          prop="cardType"
          error-align="left"
          @click="onTap"
        >
          <fui-input
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请选择证件类型"
            disabled
            :value="formData.cardType.text"
          ></fui-input>
        </fui-form-item> -->
        <fui-form-item label="证件号" asterisk prop="cardCode" error-align="left">
          <fui-input
            :maxlength="18"
            type="cardCode"
            text-align="right"
            :padding="[0]"
            :borderBottom="false"
            backgroundColor="transparent"
            placeholder="请输入证件号"
            v-model="formData.cardCode"
          ></fui-input>
        </fui-form-item>
      </fui-form>

      <view class="fui-btn__box mt40rpx px40rpx">
        <fui-button text="刷脸认证" @click="handleCertification"></fui-button>
        <!-- <fui-button text="测试地址" @click="handleTestUrl"></fui-button> -->
      </view>
      <view class="mt20rpx">
        <view class="flex items-center justify-center">
          <fui-checkbox-group name="checkbox">
            <view class="fui-list__item">
              <fui-label>
                <view class="flex items-center">
                  <fui-checkbox
                    class="flex items-center"
                    :checked="isAgree"
                    :scaleRatio="0.9"
                    @change="handleAgree"
                  ></fui-checkbox>
                  <view class="pl16rpx text-24rpx text-[#7F7F7F]">已阅读并同意</view>
                </view>
              </fui-label>
            </view>
          </fui-checkbox-group>
          <fui-button color="#FF9900" type="link" :size="24" @click="handleShowAgreement">
            <text>《用户服务协议》</text>
          </fui-button>
          <fui-button color="#FF9900" type="link" :size="24" @click="handleShowPrivacyPolicy">
            <text>《隐私权政策》</text>
          </fui-button>
        </view>
      </view>
    </view>
    <fui-picker
      linkage
      :value="pickerValue"
      :options="pickerOptions"
      :show="showPick"
      @change="pickerChange"
      @cancel="pickerCancel"
    ></fui-picker>
    <fui-loading v-if="loading" isMask></fui-loading>
  </view>
</template>

<script lang="ts" setup>
import { personalAuth } from '@/service/app'
import { useUserStore } from '@/store'
import { getEnvBaseUrl } from '@/utils'
import { showTextToast } from '@/utils/util'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()

const { userInfo } = storeToRefs(userStore)

// 上传url
const baseUrl = getEnvBaseUrl()
const uploadUrl = ref(`${baseUrl}/file/uploadFile`)
// 上传头
const uploadHeader = ref({
  token: userInfo.value.token,
})

// 表单
const formRef = ref()

// 表单数据
const formData = ref({
  name: '',
  phone: '',
  cardCode: '',
  phoneCode: '86',
  /**
   * 证件类型
   *0 二代身份证
   *1 护照
   *2港澳通行证
   *3 台胞证
   */

  cardType: { value: 0, text: '二代身份证' },
  cardPersonImg: '',
  cardInfoImg: '',
})

const rules = [
  {
    name: 'name',
    rule: ['required'],
    msg: ['请输入姓名'],
  },
  // {
  //   name: 'phone',
  //   rule: ['required', 'isMobile'],
  //   msg: ['请输入手机号', '请输入正确的手机号'],
  // },
  // {
  //   name: 'phoneCode',
  //   rule: ['required'],
  //   msg: ['请输入手机区号'],
  // },

  {
    name: 'cardCode',
    rule: ['required', 'isIdCard'],
    msg: ['请输入证件号', '请输入正确的证件号'],
  },
]
// 选择器
const pickerKey = ref('')
const pickerValue: any = ref('二代身份证')
const pickerOptions = ref([
  { value: 0, text: '二代身份证' },
  { value: 1, text: '护照' },
  { value: 2, text: '港澳通行证' },
  { value: 3, text: '台胞证' },
])
const showPick = ref(false)
const onTap = (e: any) => {
  showPick.value = true
}
// 选择器改变
const pickerChange = (e: any) => {
  console.log('pickerChange', e)
  formData.value.cardType.value = e.value
  formData.value.cardType.text = e.text
  pickerCancel()
}
// 选择器取消
const pickerCancel = () => {
  showPick.value = false
  pickerKey.value = ''
}

const handleCertification = () => {
  console.log('handleCertification')

  if (!isAgree.value) {
    showTextToast('请先同意用户服务协议和隐私权政策')
    return
  }

  const _rules = [...rules]
  console.log('_rules', _rules)
  console.log(formData.value.cardType)
  console.log(formData.value.cardType?.text?.includes('身份证'))

  // if (formData.value.cardType?.text?.includes('身份证')) {
  //   _rules[_rules.length - 1].rule = ['required', 'isIdCard']
  //   _rules[_rules.length - 1].msg = ['请输入证件号', '请输入正确的证件号']
  //   console.log('_rules', _rules)
  // } else {
  //   _rules[_rules.length - 1].rule = ['required']
  //   _rules[_rules.length - 1].msg = ['请输入证件号']
  // }

  formRef.value.validator(formData.value, _rules).then(async (res: any) => {
    console.log('res', res)
    if (res.isPassed) {
      // todo 获取个人认证页面

      const { name, phone, phoneCode, cardType, cardCode, cardPersonImg, cardInfoImg } =
        formData.value

      try {
        loading.value = true
        const res = await personalAuth({
          body: {
            name,
            phone,
            // phoneCode,
            cardType: cardType.value,
            cardCode,
            // cardPersonImg,
            // cardInfoImg,
          },
        })
        console.log('res', res)
        if (res.result?.result) {
          const encodeUrl = encodeURIComponent(res.result?.result)
          // 使用redirectTo 关闭当前页面 打开认证页面
          uni.redirectTo({
            url: `/pages-sub/realname/personalAuth?url=${encodeUrl}`,
          })
        } else {
          uni.navigateBack()
        }
      } catch (e) {
        console.log('e', e)

        // showTextToast(e.message || '认证失败')
      } finally {
        loading.value = false
      }

      console.log('cardPersonImg', cardPersonImg)
      console.log('cardInfoImg', cardInfoImg)
    } else {
      showTextToast(res.errorMsg)
    }
  })
}

const loading = ref(false)

const isAgree = ref(false)
const handleAgree = (e: any) => {
  isAgree.value = e.checked
}

// 用户服务协议
const handleShowAgreement = () => {
  console.log('用户服务协议')
  uni.navigateTo({ url: '/pages-sub/userAgreement/index' })
}

// 隐私权政策
const handleShowPrivacyPolicy = () => {
  console.log('隐私权政策')
  uni.navigateTo({ url: '/pages-sub/privacyPolicy/index' })
}

// 获取个人信息
const fetchUserInfo = async () => {
  try {
    const res = await userStore.checkUserInfo()
    console.log('fetchUserInfo', res.result?.isRealAuth)
    await nextTick()
    if (res.result?.isRealAuth === 1) {
      showTextToast('用户已实名认证')
      setTimeout(() => {
        // 如果认证成功 直接返回
        uni.navigateBack()
      }, 1000)
    }
  } catch (e) {
    console.log('e', e)
  } finally {
  }
}

onShow(() => {
  console.log('onShow===实名认证页面')
  // fetchUserInfo()
  fetchUserInfo()
})

onLoad(() => {
  // fetchcardCodeDict()
  // generateSnowflakeId()
})
</script>

<style lang="scss" scoped>
.add-repair-container {
  background-color: #f7f7f7;
}
.fui-list__cell {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fui-img {
  width: 100%;
  height: 268rpx;
  display: block;
  margin-top: 24rpx;
}

.fui-content--box {
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  &.active {
    color: var(--fui-color-primary);
  }
}

.fui-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
}

.fui-descr {
  display: block;
  font-size: 24rpx;
  padding-top: 24rpx;
}
::v-deep .fui-upload__wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10rpx;
  .fui-upload__item {
    margin-right: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
}
</style>
