<template>
  <text :style="{ fontSize: size + 'rpx', color: color }">{{ prefix }}{{ moneyFormatterStr }}</text>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
const props = defineProps({
  prefix: {
    type: String,
    default: '￥',
  },

  money: {
    type: Number,
    default: 0,
  },
  size: {
    type: Number,
    default: 24,
  },
  color: {
    type: String,
    default: '#000',
  },
  toFixed: {
    type: Number,
    default: 0,
  },
})
// 响应式变量
const moneyFormatterStr = computed(() => {
  return moneyFormatter(props.money)
})
const moneyFormatter = (money) => {
  return parseFloat(money)
    .toFixed(props.toFixed)
    .toString()
    .split('')
    .reverse()
    .join('')
    .replace(/(\d{3})/g, '$1,')
    .replace(/\,$/, '')
    .split('')
    .reverse()
    .join('')
}
// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped></style>
