VITE_APP_TITLE = '电梯更新服务-用户端'
VITE_APP_PORT = 9000

# UNI APPID
VITE_UNI_APPID = 'H57F2ACE4'
# 小程序APPID
VITE_WX_APPID = 'wx8b81602d12bc9760'

# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE=/

VITE_SERVER_BASEURL = 'http://10.0.9.193:8091/dfloor'
#  VITE_SERVER_BASEURL = 'http://192.168.16.110:8090/dfloor'
# VITE_SERVER_BASEURL = 'http://192.168.16.176:8012/dfloor'
# VITE_SERVER_BASEURL = 'http://192.168.16.160:8012/dfloor'
VITE_UPLOAD_BASEURL = 'https://ukw0y1.laf.run/upload'
; VITE_FILE_PREVIEW_PUBLIC_URL = 'http://10.0.9.211:9000/lease-file'
VITE_FILE_PREVIEW_PUBLIC_URL = 'http://10.0.9.193:8091/dfloor/s3/getPublicObjectByStream?fileName='
; VITE_REJECT_PAGE_URL = 'http://10.0.9.229:8788/redirectPage.html'
VITE_REJECT_PAGE_URL = 'https://cjwll.com/redirectPage.html'
VITE_FILE_STREAM_PATH = '/s3/getObjectByStream'
VITE_FILE_CONTRACT_STREAM_PATH = '/s3/download'
# 可信授权页面重定向地址(用在反向授权，人脸验证)
VITE_IDENTITY_AUTH_REDIRECT_URL='http://10.0.9.193:8012/redirectPages/identityAuthRedirect.html'


# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = ''
VITE_SERVER_BASEURL__WEIXIN_TRIAL = ''
VITE_SERVER_BASEURL__WEIXIN_RELEASE = ''

VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP = ''
VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = ''
VITE_UPLOAD_BASEURL__WEIXIN_RELEASE = ''

# h5是否需要配置代理
VITE_APP_PROXY=false
VITE_APP_PROXY_PREFIX = '/wx'

